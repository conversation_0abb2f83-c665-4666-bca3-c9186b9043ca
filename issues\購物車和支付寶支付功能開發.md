# 購物車和支付寶支付功能開發

## 項目背景
基於現有的Vue3 + TypeScript + SpringBoot + Redis項目，添加完整的電商購物車和支付寶支付功能。

## 技術架構
- 後端：SpringBoot + JPA + MySQL + Redis
- 前端：Vue3 + TypeScript + Pinia
- 支付：支付寶沙箱環境
- 緩存：Redis用於購物車臨時存儲

## 開發計劃

### 階段1：後端數據庫設計和實體創建
1. Cart實體 - 購物車主表
2. CartItem實體 - 購物車項目
3. Order實體 - 訂單主表  
4. OrderItem實體 - 訂單項目
5. Payment實體 - 支付記錄

### 階段2：支付寶配置和依賴
1. 添加支付寶SDK依賴
2. 創建支付寶配置類
3. 配置application.yml

### 階段3-8：Repository、Service、Controller、前端開發

## 支付寶配置信息
- 應用ID: 9021000129631387
- 沙箱網關: https://openapi-sandbox.dl.alipaydev.com/gateway.do
- 商戶PID: 2088721074001026
- 回調地址: 使用ngrok提供的公網地址

## 已完成功能

### 後端實現 ✅
1. **實體類設計**：
   - Cart（購物車）
   - CartItem（購物車項目）
   - Order（訂單）
   - OrderItem（訂單項目）
   - Payment（支付記錄）

2. **Repository層**：
   - CartRepository - 購物車數據訪問
   - CartItemRepository - 購物車項目數據訪問
   - OrderRepository - 訂單數據訪問
   - OrderItemRepository - 訂單項目數據訪問
   - PaymentRepository - 支付記錄數據訪問

3. **Service層**：
   - CartService - 購物車業務邏輯
   - OrderService - 訂單業務邏輯
   - PaymentService - 支付業務邏輯

4. **Controller層**：
   - CartController - 購物車API接口
   - OrderController - 訂單API接口
   - PaymentController - 支付API接口

5. **支付寶集成**：
   - AlipayConfig - 支付寶配置類
   - 支付寶SDK依賴添加
   - 沙箱環境配置完成

### 前端實現 ✅
1. **購物車頁面**（CartView.vue）：
   - 購物車商品展示
   - 商品數量調整
   - 商品選中/取消選中
   - 商品刪除和清空購物車
   - 結算功能

2. **訂單確認頁面**（CheckoutView.vue）：
   - 收貨地址填寫
   - 商品清單確認
   - 支付方式選擇
   - 訂單摘要展示
   - 訂單提交

3. **支付頁面**（PaymentView.vue）：
   - 訂單信息展示
   - 支付狀態顯示
   - 支付寶支付發起
   - 支付狀態實時檢查
   - 訂單取消功能

4. **訂單管理頁面**（OrdersView.vue）：
   - 訂單列表展示
   - 訂單狀態篩選
   - 訂單操作（支付、取消、確認收貨）
   - 分頁功能

## 核心功能流程

### 購物車流程
1. 用戶瀏覽商品 → 加入購物車
2. 購物車頁面管理商品（數量調整、刪除等）
3. 選擇商品進入結算

### 訂單流程
1. 填寫收貨信息 → 確認訂單
2. 創建訂單 → 跳轉支付頁面
3. 發起支付寶支付 → 等待支付結果
4. 支付成功 → 訂單狀態更新

### 支付流程
1. 調用支付寶API創建支付
2. 跳轉支付寶沙箱環境
3. 用戶完成支付
4. 支付寶異步回調更新訂單狀態
5. 前端實時檢查支付狀態

## 技術特點
- 完整的電商購物車系統
- 支付寶沙箱環境集成
- 實時支付狀態檢查
- 庫存管理和併發控制
- 完整的訂單狀態流轉
- 響應式前端界面設計
