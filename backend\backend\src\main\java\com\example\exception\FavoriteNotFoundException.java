package com.example.exception;

/**
 * 收藏记录不存在异常
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public class FavoriteNotFoundException extends FavoriteException {
    
    public FavoriteNotFoundException(String message) {
        super(message, "FAVORITE_NOT_FOUND");
    }
    
    public FavoriteNotFoundException(Long userId, Long itemId) {
        super(String.format("用户 %d 未收藏内容 %d", userId, itemId), "FAVORITE_NOT_FOUND");
    }
}
