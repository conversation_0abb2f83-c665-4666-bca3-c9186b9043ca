/**
 * 性能优化工具
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: number | undefined
  
  return function (...args: Parameters<T>) {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param delay 延迟时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0
  
  return function (...args: Parameters<T>) {
    const now = Date.now()
    
    if (now - lastCall >= delay) {
      lastCall = now
      func.apply(this, args)
    }
  }
}

/**
 * 内存缓存类
 */
export class MemoryCache<T = any> {
  private cache = new Map<string, { value: T; expiry: number }>()
  private defaultTTL: number

  constructor(defaultTTL: number = 5 * 60 * 1000) { // 默认5分钟
    this.defaultTTL = defaultTTL
  }

  /**
   * 设置缓存
   */
  set(key: string, value: T, ttl?: number): void {
    const expiry = Date.now() + (ttl || this.defaultTTL)
    this.cache.set(key, { value, expiry })
  }

  /**
   * 获取缓存
   */
  get(key: string): T | null {
    const item = this.cache.get(key)
    
    if (!item) {
      return null
    }
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key)
      return null
    }
    
    return item.value
  }

  /**
   * 删除缓存
   */
  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    return this.cache.size
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key)
      }
    }
  }
}

/**
 * 创建带缓存的异步函数
 */
export function withCache<T extends (...args: any[]) => Promise<any>>(
  func: T,
  cache: MemoryCache,
  keyGenerator?: (...args: Parameters<T>) => string,
  ttl?: number
): T {
  return (async (...args: Parameters<T>) => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args)
    
    // 尝试从缓存获取
    const cached = cache.get(key)
    if (cached !== null) {
      return cached
    }
    
    // 执行函数并缓存结果
    const result = await func(...args)
    cache.set(key, result, ttl)
    
    return result
  }) as T
}

/**
 * 批量请求优化器
 */
export class BatchRequestOptimizer<T, R> {
  private pending = new Map<string, Promise<R>>()
  private batchSize: number
  private batchDelay: number

  constructor(batchSize: number = 10, batchDelay: number = 100) {
    this.batchSize = batchSize
    this.batchDelay = batchDelay
  }

  /**
   * 添加请求到批次
   */
  request(key: string, requestFn: () => Promise<R>): Promise<R> {
    // 如果已有相同的请求在处理，直接返回
    if (this.pending.has(key)) {
      return this.pending.get(key)!
    }

    const promise = requestFn()
    this.pending.set(key, promise)

    // 请求完成后清理
    promise.finally(() => {
      this.pending.delete(key)
    })

    return promise
  }

  /**
   * 获取待处理请求数量
   */
  getPendingCount(): number {
    return this.pending.size
  }
}

/**
 * 虚拟滚动辅助类
 */
export class VirtualScrollHelper {
  private itemHeight: number
  private containerHeight: number
  private buffer: number

  constructor(itemHeight: number, containerHeight: number, buffer: number = 5) {
    this.itemHeight = itemHeight
    this.containerHeight = containerHeight
    this.buffer = buffer
  }

  /**
   * 计算可见范围
   */
  getVisibleRange(scrollTop: number, totalItems: number): { start: number; end: number } {
    const visibleCount = Math.ceil(this.containerHeight / this.itemHeight)
    const start = Math.max(0, Math.floor(scrollTop / this.itemHeight) - this.buffer)
    const end = Math.min(totalItems, start + visibleCount + this.buffer * 2)

    return { start, end }
  }

  /**
   * 计算总高度
   */
  getTotalHeight(totalItems: number): number {
    return totalItems * this.itemHeight
  }

  /**
   * 计算偏移量
   */
  getOffset(startIndex: number): number {
    return startIndex * this.itemHeight
  }
}

/**
 * 图片懒加载辅助函数
 */
export function createImageLazyLoader(options: {
  rootMargin?: string
  threshold?: number
} = {}) {
  const { rootMargin = '50px', threshold = 0.1 } = options

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement
        const src = img.dataset.src
        
        if (src) {
          img.src = src
          img.removeAttribute('data-src')
          observer.unobserve(img)
        }
      }
    })
  }, {
    rootMargin,
    threshold
  })

  return {
    observe: (img: HTMLImageElement) => observer.observe(img),
    unobserve: (img: HTMLImageElement) => observer.unobserve(img),
    disconnect: () => observer.disconnect()
  }
}

/**
 * 性能监控
 */
export class PerformanceMonitor {
  private marks = new Map<string, number>()

  /**
   * 开始计时
   */
  start(name: string): void {
    this.marks.set(name, performance.now())
  }

  /**
   * 结束计时并返回耗时
   */
  end(name: string): number {
    const startTime = this.marks.get(name)
    if (!startTime) {
      console.warn(`Performance mark "${name}" not found`)
      return 0
    }

    const duration = performance.now() - startTime
    this.marks.delete(name)
    
    return duration
  }

  /**
   * 测量函数执行时间
   */
  measure<T>(name: string, fn: () => T): T {
    this.start(name)
    const result = fn()
    const duration = this.end(name)
    
    console.log(`${name} took ${duration.toFixed(2)}ms`)
    
    return result
  }

  /**
   * 测量异步函数执行时间
   */
  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    this.start(name)
    const result = await fn()
    const duration = this.end(name)
    
    console.log(`${name} took ${duration.toFixed(2)}ms`)
    
    return result
  }
}

// 导出单例实例
export const performanceMonitor = new PerformanceMonitor()
export const favoriteCache = new MemoryCache(10 * 60 * 1000) // 10分钟缓存
export const batchOptimizer = new BatchRequestOptimizer()
