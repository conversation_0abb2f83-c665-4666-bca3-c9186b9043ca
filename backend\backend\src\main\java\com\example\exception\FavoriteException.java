package com.example.exception;

/**
 * 收藏功能相关异常基类
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public class FavoriteException extends RuntimeException {
    
    private final String errorCode;
    
    public FavoriteException(String message) {
        super(message);
        this.errorCode = "FAVORITE_ERROR";
    }
    
    public FavoriteException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public FavoriteException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "FAVORITE_ERROR";
    }
    
    public FavoriteException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
}
