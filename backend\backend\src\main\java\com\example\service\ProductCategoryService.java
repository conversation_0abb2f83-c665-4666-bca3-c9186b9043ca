package com.example.service;

import com.example.dto.ApiResponse;
import com.example.dto.PagedResponse;
import com.example.dto.ProductCategoryDTO;
import com.example.entity.ProductCategory;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 商品分類服務接口
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
public interface ProductCategoryService {
    
    /**
     * 獲取分類樹（帶緩存）
     * 實現Redis緩存，提高查詢性能
     */
    ApiResponse<List<ProductCategoryDTO>> getCategoryTree();
    
    /**
     * 獲取根分類列表
     */
    ApiResponse<List<ProductCategory>> getRootCategories();
    
    /**
     * 根據父ID獲取子分類
     */
    ApiResponse<List<ProductCategory>> getChildCategories(Long parentId);
    
    /**
     * 根據ID獲取分類詳情
     */
    ApiResponse<ProductCategory> getCategoryById(Long id);
    
    /**
     * 獲取分類路徑（從根到當前分類）
     */
    ApiResponse<String> getCategoryPath(Long categoryId);

    /**
     * 獲取分類及其所有子分類的ID列表
     */
    List<Long> getCategoryAndDescendantIds(Long categoryId);
    
    /**
     * 創建分類
     * 實現延遲雙刪緩存策略
     */
    ApiResponse<ProductCategory> createCategory(ProductCategory category, Long createdBy);
    
    /**
     * 更新分類
     * 實現緩存一致性處理
     */
    ApiResponse<ProductCategory> updateCategory(Long id, ProductCategory category, Long updatedBy);
    
    /**
     * 刪除分類
     * 檢查是否有子分類和商品，實現緩存清理
     */
    ApiResponse<String> deleteCategory(Long id, Long deletedBy);
    
    /**
     * 批量刪除分類
     */
    ApiResponse<String> batchDeleteCategories(List<Long> ids, Long deletedBy);
    
    /**
     * 啟用/禁用分類
     */
    ApiResponse<String> toggleCategoryStatus(Long id, Integer status, Long updatedBy);
    
    /**
     * 調整分類排序
     */
    ApiResponse<String> updateCategorySort(Long id, Integer sortOrder, Long updatedBy);
    
    /**
     * 移動分類（更改父分類）
     */
    ApiResponse<String> moveCategory(Long categoryId, Long newParentId, Long updatedBy);
    
    /**
     * 分頁查詢分類（管理後台使用）
     */
    ApiResponse<PagedResponse<ProductCategory>> getCategoriesWithFilters(
            String name, Long parentId, Integer status, Pageable pageable);
    
    /**
     * 獲取葉子分類（可以添加商品的分類）
     */
    ApiResponse<List<ProductCategory>> getLeafCategories();
    
    /**
     * 統計分類下的商品數量
     */
    ApiResponse<Long> getProductCountByCategory(Long categoryId);
    
    /**
     * 檢查分類名稱是否存在
     */
    ApiResponse<Boolean> checkCategoryNameExists(String name, Long parentId, Long excludeId);
    
    /**
     * 獲取分類統計信息
     */
    ApiResponse<Object> getCategoryStatistics();
    
    /**
     * 刷新分類緩存
     */
    ApiResponse<String> refreshCategoryCache();
    
    /**
     * 重建分類樹結構
     * 修復數據不一致問題
     */
    ApiResponse<String> rebuildCategoryTree();
}
