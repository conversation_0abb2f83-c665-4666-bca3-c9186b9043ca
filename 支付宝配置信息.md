# 支付宝沙箱配置信息

## 應用信息
- **應用ID**: 9021000129631387

## 密鑰信息

### 應用公鑰
```
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAo+7hjM7IMDtXm1Q3t19PkOpAGqE/6pL+t1pa0Fjs+9x9ikNxijcqA7oNIQN6aCIxhMPoCZk8+1TdyypkrM9Kf8AQyOijpFdO0LrE85FbaBarzYdoDij/nREDc2Vcay6CIX9PO7VT9baB/xOBXtYrGeIgInaQPgbzkeL+vXdCxL+HYHlmihEvhBSqsWop7tbzaULJe2nPBPWUvrhnrdR7nrXd4B2nHp+bi3441pqetTxuu39G3YfKCTSQxU31W3jPsMY6O70MWUp5+S7jajPbzhWYAmFaEPSSg1VEhgTDBcBW7limYZ7bAY0az0eeAZWkkvfLVnNOSamRRSn3vQH/AQIDAQAB
```

### 應用私鑰
```
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCj7uGMzsgwO1ebVDe3X0+Q6kAaoT/qkv63WlrQWOz73H2KQ3GKNyoDug0hA3poIjGEw+gJmTz7VN3LKmSsz0p/wBDI6KOkV07QusTzkVtoFqvNh2gOKP+dEQNzZVxrLoIhf087tVP1toH/E4Fe1isZ4iAidpA+BvOR4v69d0LEv4dgeWaKES+EFKqxainu1vNpQsl7ac8E9ZS+uGet1Huetd3gHacen5uLfjjWmp61PG67f0bdh8oJNJDFTfVbeM+wxjo7vQxZSnn5LuNqM9vOFZgCYVoQ9JKDVUSGBMMFwFbuWKZhntsBjRrPR54BlaSS98tWc05JqZFFKfe9Af8BAgMBAAECggEANQ3onUOG8mUiMzkMVyJXg9TsLjIll2s/WGO7pFZDTDfNwWOWaV71EAO0oAyaHv2B/S/R9tlLLSQIK+pqbqsftzyXsg3oeBVxgogPmaxfYs6Dh57dBEgsgc6xyTTythV1NBYZtH84zvrkE4NiiSjTBityrvSkNg7V4U8Plo30Y+v76boXcbd1/jHjRSuLwIk/odO7VOYXQ2w2hiDJAeydJR01VEbA3WU+yHcNcjkSaiYEJPL2aeiZMhtq1LuXcb2nIMUOrxDV6pdRZBXCIeURgLrW/YH/XraxU2R7j2CYRA/vaR+hcmYtV8fZhmrRw7fYotkBNPg6a7lKV5be1DXNgQKBgQDRAkZMDasqcLDzItreT9lxX+nv9EWFkLHux/45TM4kdBthBK85XdG9+OteKW4DsfbkW0rWJ7+T0vt+ZX6/4p+PYbsN3u17gx71LV5NGHcfrzcjNOd9R1QaOJZpJe6vEad01sWcEV7qlhnKzayryX13Ee0BVQDc/bAQcFjsQNFqCQKBgQDIyjldmKGA0bs6t+zTNkLDJuz67e9Y0BLELrDsCPGwMkaMlAaK3RgqOBm/Qz5dUkyiT4LUQEha98Q5mWa7EYShrsE3XmK7mDy00SLBb8z2wgxZU9+HFWZwG44VYmbaa8dYmsKi+L+XnF3u6euR8a8ePU+WDsE3oMGYYYLsmPsLOQKBgQCYicZwPxGK2c/qwqdl1HTnr452V5pdjmqt0DwT6aARLsPEnLyda4Fl2kM855OpErsTkiUeAshoxHRTnRNSS70T0cnIp8g+ekWfvkqyjYZzE1d0VGoWHnac5GuxtcNq9cF3Hj/+VRcmsgGE53J7tYPh4K1OaZFFt8hFVxku42dysQKBgCFUqGcoEg8vQZRCudZp2HVKveX9n1Cv4Z2dXpMf/PbRsbJeVCOzBfLkMynzwKN/KOd8qWwQa7JmFLW3CD2fb9PjDYHiciNP5yvg15MiuvRvS9t8N8vVupZ7jH9yQT6ay5GixxKEllFVjKY/QHKOmxI6/T4cnuhcECb6cOBTmMDJAoGBAIRhbVydzmftOnUrDtrGt5s6gBAWFP6N5SOl27Zq7msD91NO34yRxxwuuUEltX8hyUVfQFG4xx/4tVGhXRWMH/ZRfc+xwCPPVRGG8nV6IydrXuCaiKWOV7AxAhZWJ+i6g9BqgbT6K0IDhjwx1U2iyfT/c6/lhc3qRJJ0Z+tj2hju
```

### 支付宝公鑰
```
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnvhJ5idfRRgEB1mZysTpeSWQsdas1pJtAKhss2rY2kHhtN1pP7ha2lZQhFYK2ve6Welq/6N74JXs4wS6e2+3ILyHU0lcG6szpO6KynrxEeDbul4YnhIpA3czHD+9B+/i7qN5Onha3f43RlqDnQN0vOfhy1lgEjXtgzrvKa8jnd7Uu87mNFLUM9BahDAPhSS6T0cDtOj6jlWYdgMqdl/w6yfbF5OjIlzfV/JpnS6E7ZgLxHsjR7ssvg6vzuhJpu0QivyrxmLzXVOIeSlkCTRi/igItdcBffGlvgCvai4hbidWDRfRyqX9KYnWXkow4JeKzY0sxLwQUntxnJWmUAjLCwIDAQAB
```

## 沙箱賬號信息

### 商家信息
- **商戶賬號**: <EMAIL>
- **登錄密碼**: 111111
- **商戶PID**: 2088721074001026
- **賬戶餘額**: 1000000.00

### 買家信息
- **買家賬號**: <EMAIL>
- **登錄密碼**: 111111
- **支付密碼**: 111111
- **用戶UID**: 2088722074001034
- **用戶名稱**: bnuhtx6909
- **證件類型**: IDENTITY_CARD
- **證件賬號**: 373671190505140514
- **賬戶餘額**: 1000000.00

## 網關配置
- **沙箱網關**: https://openapi-sandbox.dl.alipaydev.com/gateway.do

## 回調地址配置
- **回調地址**: 需要配置 ngrok 提供的公網地址
- **返回地址**: 支付完成後的跳轉頁面

## 注意事項
1. 這是沙箱環境配置，僅用於開發測試
2. 生產環境需要使用正式的應用ID和密鑰
3. 回調地址必須是公網可訪問的地址
4. 需要下載最新版本的沙箱支付寶APP進行測試
