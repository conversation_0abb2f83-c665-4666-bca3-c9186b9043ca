import { api, type ApiResponse } from './index'

// 購物車項目接口
export interface CartItem {
  id: number
  productId: number
  productName: string
  productImageUrl: string
  price: number
  originalPrice?: number
  quantity: number
  selected: boolean
  invalid?: boolean
  stock?: number
  specs?: Record<string, string>
}

// 購物車接口
export interface Cart {
  id: number
  userId: number
  totalAmount: number
  totalQuantity: number
  status: number
  cartItems: CartItem[]
  createdAt: string
  updatedAt: string
}

// 購物車API
export const cartAPI = {
  // 獲取購物車
  getCart: (): Promise<ApiResponse<Cart>> =>
    api.get('/cart'),

  // 添加商品到購物車
  addToCart: (productId: number, quantity: number): Promise<ApiResponse<string>> =>
    api.post('/cart/add', null, {
      params: {
        productId,
        quantity
      }
    }),

  // 更新購物車項目數量
  updateCartItem: (cartItemId: number, quantity: number): Promise<ApiResponse<string>> =>
    api.put(`/cart/update/${cartItemId}`, null, {
      params: {
        quantity
      }
    }),

  // 從購物車移除商品
  removeFromCart: (cartItemId: number): Promise<ApiResponse<string>> =>
    api.delete(`/cart/remove/${cartItemId}`),

  // 清空購物車
  clearCart: (): Promise<ApiResponse<string>> =>
    api.delete('/cart/clear'),

  // 切換商品選中狀態
  toggleSelected: (cartItemId: number): Promise<ApiResponse<string>> =>
    api.put(`/cart/toggle-selected/${cartItemId}`),

  // 獲取選中的購物車項目
  getSelectedItems: (): Promise<ApiResponse<CartItem[]>> =>
    api.get('/cart/selected')
}

// 購物車狀態枚舉
export const CartStatus = {
  CONVERTED_TO_ORDER: -1,  // 已轉為訂單
  CLEARED: 0,              // 已清空
  ACTIVE: 1                // 正常
} as const

export type CartStatusType = typeof CartStatus[keyof typeof CartStatus]
