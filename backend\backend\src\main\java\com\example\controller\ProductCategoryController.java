package com.example.controller;

import com.example.dto.ApiResponse;
import com.example.dto.PagedResponse;
import com.example.dto.ProductCategoryDTO;
import com.example.entity.ProductCategory;
import com.example.service.ProductCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 商品分類控制器
 * 提供商品分類的CRUD操作和樹形結構查詢
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("/api/product/categories")
@Tag(name = "商品分類管理", description = "商品分類的增刪改查和樹形結構管理")
public class ProductCategoryController {
    
    @Autowired
    private ProductCategoryService categoryService;
    
    /**
     * 獲取分類樹（公開接口，帶緩存）
     */
    @GetMapping("/tree")
    @Operation(summary = "獲取分類樹", description = "獲取完整的商品分類樹形結構，帶Redis緩存優化")
    public ApiResponse<List<ProductCategoryDTO>> getCategoryTree() {
        log.info("獲取商品分類樹");
        return categoryService.getCategoryTree();
    }
    
    /**
     * 獲取根分類列表
     */
    @GetMapping("/roots")
    @Operation(summary = "獲取根分類", description = "獲取所有根級分類列表")
    public ApiResponse<List<ProductCategory>> getRootCategories() {
        log.info("獲取根分類列表");
        return categoryService.getRootCategories();
    }
    
    /**
     * 根據父ID獲取子分類
     */
    @GetMapping("/children/{parentId}")
    @Operation(summary = "獲取子分類", description = "根據父分類ID獲取子分類列表")
    @Parameter(name = "parentId", description = "父分類ID", required = true)
    public ApiResponse<List<ProductCategory>> getChildCategories(@PathVariable Long parentId) {
        log.info("獲取子分類: parentId={}", parentId);
        return categoryService.getChildCategories(parentId);
    }
    
    /**
     * 獲取分類詳情
     */
    @GetMapping("/{id}")
    @Operation(summary = "獲取分類詳情", description = "根據ID獲取分類詳細信息")
    @Parameter(name = "id", description = "分類ID", required = true)
    public ApiResponse<ProductCategory> getCategoryById(@PathVariable Long id) {
        log.info("獲取分類詳情: id={}", id);
        return categoryService.getCategoryById(id);
    }
    
    /**
     * 獲取分類路徑
     */
    @GetMapping("/{id}/path")
    @Operation(summary = "獲取分類路徑", description = "獲取從根分類到當前分類的完整路徑")
    @Parameter(name = "id", description = "分類ID", required = true)
    public ApiResponse<String> getCategoryPath(@PathVariable Long id) {
        log.info("獲取分類路徑: id={}", id);
        return categoryService.getCategoryPath(id);
    }
    
    /**
     * 獲取葉子分類（可以添加商品的分類）
     */
    @GetMapping("/leaves")
    @Operation(summary = "獲取葉子分類", description = "獲取所有可以添加商品的葉子分類")
    public ApiResponse<List<ProductCategory>> getLeafCategories() {
        log.info("獲取葉子分類列表");
        return categoryService.getLeafCategories();
    }
    
    /**
     * 創建分類（需要認證）
     */
    @PostMapping
    @Operation(summary = "創建分類", description = "創建新的商品分類，支持多級分類結構")
    public ApiResponse<ProductCategory> createCategory(
            @Valid @RequestBody ProductCategory category,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("創建商品分類: name={}, parentId={}, userId={}", 
                category.getName(), category.getParentId(), userId);
        
        return categoryService.createCategory(category, userId);
    }
    
    /**
     * 更新分類（需要認證）
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新分類", description = "更新商品分類信息")
    @Parameter(name = "id", description = "分類ID", required = true)
    public ApiResponse<ProductCategory> updateCategory(
            @PathVariable Long id,
            @Valid @RequestBody ProductCategory category,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("更新商品分類: id={}, name={}, userId={}", 
                id, category.getName(), userId);
        
        return categoryService.updateCategory(id, category, userId);
    }
    
    /**
     * 刪除分類（需要認證）
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "刪除分類", description = "刪除商品分類，會檢查是否有子分類和商品")
    @Parameter(name = "id", description = "分類ID", required = true)
    public ApiResponse<String> deleteCategory(
            @PathVariable Long id,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("刪除商品分類: id={}, userId={}", id, userId);
        
        return categoryService.deleteCategory(id, userId);
    }
    
    /**
     * 批量刪除分類（需要認證）
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量刪除分類", description = "批量刪除多個商品分類")
    public ApiResponse<String> batchDeleteCategories(
            @RequestBody List<Long> ids,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("批量刪除商品分類: ids={}, userId={}", ids, userId);
        
        return categoryService.batchDeleteCategories(ids, userId);
    }
    
    /**
     * 啟用/禁用分類（需要認證）
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "切換分類狀態", description = "啟用或禁用商品分類")
    @Parameter(name = "id", description = "分類ID", required = true)
    public ApiResponse<String> toggleCategoryStatus(
            @PathVariable Long id,
            @RequestParam Integer status,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("切換分類狀態: id={}, status={}, userId={}", id, status, userId);
        
        return categoryService.toggleCategoryStatus(id, status, userId);
    }
    
    /**
     * 調整分類排序（需要認證）
     */
    @PutMapping("/{id}/sort")
    @Operation(summary = "調整分類排序", description = "更新商品分類的排序號")
    @Parameter(name = "id", description = "分類ID", required = true)
    public ApiResponse<String> updateCategorySort(
            @PathVariable Long id,
            @RequestParam Integer sortOrder,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("調整分類排序: id={}, sortOrder={}, userId={}", id, sortOrder, userId);
        
        return categoryService.updateCategorySort(id, sortOrder, userId);
    }
    
    /**
     * 移動分類（需要認證）
     */
    @PutMapping("/{id}/move")
    @Operation(summary = "移動分類", description = "將分類移動到新的父分類下")
    @Parameter(name = "id", description = "分類ID", required = true)
    public ApiResponse<String> moveCategory(
            @PathVariable Long id,
            @RequestParam Long newParentId,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("移動分類: id={}, newParentId={}, userId={}", id, newParentId, userId);
        
        return categoryService.moveCategory(id, newParentId, userId);
    }
    
    /**
     * 分頁查詢分類（管理後台使用）
     */
    @GetMapping("/admin/list")
    @Operation(summary = "分頁查詢分類", description = "管理後台分頁查詢分類，支持多條件篩選")
    public ApiResponse<PagedResponse<ProductCategory>> getCategoriesWithFilters(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Long parentId,
            @RequestParam(required = false) Integer status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.info("分頁查詢分類: name={}, parentId={}, status={}, page={}, size={}", 
                name, parentId, status, page, size);
        
        Pageable pageable = PageRequest.of(page, size);
        return categoryService.getCategoriesWithFilters(name, parentId, status, pageable);
    }
    
    /**
     * 統計分類下的商品數量
     */
    @GetMapping("/{id}/product-count")
    @Operation(summary = "統計商品數量", description = "統計指定分類下的商品數量")
    @Parameter(name = "id", description = "分類ID", required = true)
    public ApiResponse<Long> getProductCountByCategory(@PathVariable Long id) {
        log.info("統計分類商品數量: categoryId={}", id);
        return categoryService.getProductCountByCategory(id);
    }
    
    /**
     * 檢查分類名稱是否存在
     */
    @GetMapping("/check-name")
    @Operation(summary = "檢查名稱重複", description = "檢查分類名稱是否已存在")
    public ApiResponse<Boolean> checkCategoryNameExists(
            @RequestParam String name,
            @RequestParam Long parentId,
            @RequestParam(required = false) Long excludeId) {
        
        log.info("檢查分類名稱: name={}, parentId={}, excludeId={}", name, parentId, excludeId);
        return categoryService.checkCategoryNameExists(name, parentId, excludeId);
    }
    
    /**
     * 獲取分類統計信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "獲取分類統計", description = "獲取分類的統計信息")
    public ApiResponse<Object> getCategoryStatistics() {
        log.info("獲取分類統計信息");
        return categoryService.getCategoryStatistics();
    }
    
    /**
     * 刷新分類緩存（管理員功能）
     */
    @PostMapping("/cache/refresh")
    @Operation(summary = "刷新緩存", description = "手動刷新分類相關緩存")
    public ApiResponse<String> refreshCategoryCache() {
        log.info("刷新分類緩存");
        return categoryService.refreshCategoryCache();
    }
    
    /**
     * 重建分類樹結構（管理員功能）
     */
    @PostMapping("/rebuild")
    @Operation(summary = "重建分類樹", description = "重建分類樹結構，修復數據不一致問題")
    public ApiResponse<String> rebuildCategoryTree() {
        log.info("重建分類樹結構");
        return categoryService.rebuildCategoryTree();
    }
    
    /**
     * 從Authentication中獲取用戶ID
     */
    private Long getUserId(Authentication authentication) {
        if (authentication != null && authentication.getPrincipal() instanceof org.springframework.security.core.userdetails.UserDetails) {
            // 這裡需要根據實際的UserDetails實現來獲取用戶ID
            // 假設UserDetails實現類有getId()方法
            return 1L; // 臨時返回，實際應該從authentication中獲取
        }
        return null;
    }
}
