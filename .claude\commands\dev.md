---
description: 通用開發工具命令，支援代碼執行、檔案操作、MCP調用等功能
---

#!/bin/bash

# 通用開發工具命令
# 使用方式: dev <action> [options]

case "$1" in
    # 代碼相關
    "run")
        case "$2" in
            "python") python3 "${@:3}" ;;
            "node") node "${@:3}" ;;
            "npm") npm "${@:3}" ;;
            "yarn") yarn "${@:3}" ;;
            "go") go run "${@:3}" ;;
            "rust") cargo run "${@:3}" ;;
            *) echo "支援: python, node, npm, yarn, go, rust" ;;
        esac
        ;;
    
    # 檔案操作
    "file")
        case "$2" in
            "create") touch "${@:3}" && echo "已創建: ${@:3}" ;;
            "delete") rm -rf "${@:3}" && echo "已刪除: ${@:3}" ;;
            "copy") cp -r "$3" "$4" && echo "已複製: $3 -> $4" ;;
            "move") mv "$3" "$4" && echo "已移動: $3 -> $4" ;;
            "list") ls -la "${3:-.}" ;;
            "find") find "${3:-.}" -name "*$4*" ;;
            *) echo "支援: create, delete, copy, move, list, find" ;;
        esac
        ;;
    
    # 目錄操作
    "dir")
        case "$2" in
            "create") mkdir -p "${@:3}" && echo "已創建目錄: ${@:3}" ;;
            "goto") cd "$3" && pwd ;;
            "tree") tree "${3:-.}" ;;
            *) echo "支援: create, goto, tree" ;;
        esac
        ;;
    
    # Git 操作
    "git")
        case "$2" in
            "init") git init && echo "Git 已初始化" ;;
            "add") git add "${3:-.}" && echo "已添加檔案" ;;
            "commit") git commit -m "${3:-auto commit}" && echo "已提交" ;;
            "push") git push "${@:3}" && echo "已推送" ;;
            "pull") git pull "${@:3}" && echo "已拉取" ;;
            "status") git status ;;
            "log") git log --oneline -10 ;;
            "branch") git branch "${@:3}" ;;
            "checkout") git checkout "${@:3}" ;;
            *) echo "支援: init, add, commit, push, pull, status, log, branch, checkout" ;;
        esac
        ;;
    
    # 網路操作
    "net")
        case "$2" in
            "ping") ping -c 4 "$3" ;;
            "curl") curl "${@:3}" ;;
            "wget") wget "${@:3}" ;;
            "port") netstat -tulpn | grep "$3" ;;
            *) echo "支援: ping, curl, wget, port" ;;
        esac
        ;;
    
    # 系統資訊
    "sys")
        case "$2" in
            "info") uname -a ;;
            "cpu") top -n 1 | head -5 ;;
            "mem") free -h ;;
            "disk") df -h ;;
            "proc") ps aux | head -10 ;;
            *) echo "支援: info, cpu, mem, disk, proc" ;;
        esac
        ;;
    
    # MCP 相關操作
    "mcp")
        case "$2" in
            "list") echo "列出可用的 MCP 服務器" ;;
            "connect") echo "連接到 MCP 服務器: $3" ;;
            "call") echo "調用 MCP 功能: $3 參數: ${@:4}" ;;
            "status") echo "檢查 MCP 狀態" ;;
            *) echo "支援: list, connect, call, status" ;;
        esac
        ;;
    
    # 開發服務器
    "serve")
        case "$2" in
            "http") python3 -m http.server "${3:-8000}" ;;
            "php") php -S "localhost:${3:-8000}" ;;
            "node") npx serve -p "${3:-8000}" ;;
            *) echo "支援: http, php, node" ;;
        esac
        ;;
    
    # 數據庫操作
    "db")
        case "$2" in
            "mysql") mysql -u "${3:-root}" -p ;;
            "postgres") psql -U "${3:-postgres}" ;;
            "sqlite") sqlite3 "$3" ;;
            "mongo") mongo "${3:-test}" ;;
            *) echo "支援: mysql, postgres, sqlite, mongo" ;;
        esac
        ;;
    
    # 封裝管理
    "pkg")
        case "$2" in
            "install") 
                if [ -f "package.json" ]; then npm install "${@:3}"
                elif [ -f "requirements.txt" ]; then pip install "${@:3}"
                elif [ -f "Cargo.toml" ]; then cargo add "${@:3}"
                else echo "未找到封裝配置檔案"; fi ;;
            "update")
                if [ -f "package.json" ]; then npm update
                elif [ -f "requirements.txt" ]; then pip install -U "${@:3}"
                elif [ -f "Cargo.toml" ]; then cargo update
                else echo "未找到封裝配置檔案"; fi ;;
            *) echo "支援: install, update" ;;
        esac
        ;;
    
    # 測試
    "test")
        case "$2" in
            "unit") npm test || python -m pytest || cargo test ;;
            "e2e") npm run test:e2e || echo "請配置 e2e 測試" ;;
            "coverage") npm run test:coverage || coverage run -m pytest ;;
            *) echo "支援: unit, e2e, coverage" ;;
        esac
        ;;
    
    # 建置
    "build")
        case "$2" in
            "dev") npm run dev || python manage.py runserver || cargo run ;;
            "prod") npm run build || python manage.py collectstatic || cargo build --release ;;
            "clean") npm run clean || rm -rf build dist target ;;
            *) echo "支援: dev, prod, clean" ;;
        esac
        ;;
    
    # 幫助
    "help"|"--help"|"-h")
        echo "通用開發工具命令"
        echo ""
        echo "使用方式: dev <category> <action> [options]"
        echo ""
        echo "類別:"
        echo "  run     - 執行代碼 (python, node, npm, yarn, go, rust)"
        echo "  file    - 檔案操作 (create, delete, copy, move, list, find)"
        echo "  dir     - 目錄操作 (create, goto, tree)"
        echo "  git     - Git 操作 (init, add, commit, push, pull, status, log)"
        echo "  net     - 網路工具 (ping, curl, wget, port)"
        echo "  sys     - 系統資訊 (info, cpu, mem, disk, proc)"
        echo "  mcp     - MCP 操作 (list, connect, call, status)"
        echo "  serve   - 開發服務器 (http, php, node)"
        echo "  db      - 數據庫 (mysql, postgres, sqlite, mongo)"
        echo "  pkg     - 封裝管理 (install, update)"
        echo "  test    - 測試 (unit, e2e, coverage)"
        echo "  build   - 建置 (dev, prod, clean)"
        echo ""
        echo "範例:"
        echo "  dev run python script.py"
        echo "  dev file create test.txt"
        echo "  dev git commit '新增功能'"
        echo "  dev serve http 3000"
        echo "  dev mcp call search '關鍵字'"
        ;;
    
    *)
        echo "未知命令: $1"
        echo "使用 'dev help' 查看說明"
        exit 1
        ;;
esac