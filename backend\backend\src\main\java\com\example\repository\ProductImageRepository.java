package com.example.repository;

import com.example.entity.ProductImage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 商品圖片數據訪問層
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Repository
public interface ProductImageRepository extends JpaRepository<ProductImage, Long> {
    
    /**
     * 根據商品ID查詢所有圖片，按排序號排序
     */
    @Query("SELECT pi FROM ProductImage pi WHERE pi.productId = :productId ORDER BY pi.sortOrder ASC, pi.id ASC")
    List<ProductImage> findByProductIdOrderBySortOrder(@Param("productId") Long productId);
    
    /**
     * 查詢商品主圖
     */
    @Query("SELECT pi FROM ProductImage pi WHERE pi.productId = :productId AND pi.isMain = 1")
    Optional<ProductImage> findMainImageByProductId(@Param("productId") Long productId);
    
    /**
     * 查詢商品的非主圖列表
     */
    @Query("SELECT pi FROM ProductImage pi WHERE pi.productId = :productId AND pi.isMain = 0 ORDER BY pi.sortOrder ASC")
    List<ProductImage> findNonMainImagesByProductId(@Param("productId") Long productId);
    
    /**
     * 統計商品圖片數量
     */
    @Query("SELECT COUNT(pi) FROM ProductImage pi WHERE pi.productId = :productId")
    long countByProductId(@Param("productId") Long productId);
    
    /**
     * 查詢商品的最大排序號
     */
    @Query("SELECT COALESCE(MAX(pi.sortOrder), 0) FROM ProductImage pi WHERE pi.productId = :productId")
    Integer findMaxSortOrderByProductId(@Param("productId") Long productId);
    
    /**
     * 批量刪除商品圖片
     */
    @Modifying
    @Query("DELETE FROM ProductImage pi WHERE pi.productId = :productId")
    void deleteByProductId(@Param("productId") Long productId);
    
    /**
     * 批量刪除指定圖片
     */
    @Modifying
    @Query("DELETE FROM ProductImage pi WHERE pi.id IN :imageIds")
    void deleteByIds(@Param("imageIds") List<Long> imageIds);
    
    /**
     * 清除商品的主圖標識（設置新主圖前使用）
     */
    @Modifying
    @Query("UPDATE ProductImage pi SET pi.isMain = 0 WHERE pi.productId = :productId")
    void clearMainImageFlag(@Param("productId") Long productId);
    
    /**
     * 設置主圖
     */
    @Modifying
    @Query("UPDATE ProductImage pi SET pi.isMain = 1 WHERE pi.id = :imageId")
    void setAsMainImage(@Param("imageId") Long imageId);
    
    /**
     * 更新圖片排序
     */
    @Modifying
    @Query("UPDATE ProductImage pi SET pi.sortOrder = :sortOrder WHERE pi.id = :imageId")
    void updateSortOrder(@Param("imageId") Long imageId, @Param("sortOrder") Integer sortOrder);
    
    /**
     * 批量更新圖片排序
     */
    @Modifying
    @Query("UPDATE ProductImage pi SET pi.sortOrder = " +
           "CASE " +
           "WHEN pi.id = :imageId1 THEN :sortOrder1 " +
           "WHEN pi.id = :imageId2 THEN :sortOrder2 " +
           "ELSE pi.sortOrder END " +
           "WHERE pi.id IN (:imageId1, :imageId2)")
    void swapSortOrder(@Param("imageId1") Long imageId1, @Param("sortOrder1") Integer sortOrder1,
                       @Param("imageId2") Long imageId2, @Param("sortOrder2") Integer sortOrder2);
    
    /**
     * 根據上傳者查詢圖片
     */
    @Query("SELECT pi FROM ProductImage pi WHERE pi.uploadedBy = :uploadedBy ORDER BY pi.createdAt DESC")
    List<ProductImage> findByUploadedBy(@Param("uploadedBy") Long uploadedBy);
    
    /**
     * 查詢指定時間範圍內上傳的圖片
     */
    @Query("SELECT pi FROM ProductImage pi WHERE pi.createdAt >= :startTime AND pi.createdAt <= :endTime ORDER BY pi.createdAt DESC")
    List<ProductImage> findByCreatedAtBetween(@Param("startTime") java.time.LocalDateTime startTime, 
                                              @Param("endTime") java.time.LocalDateTime endTime);
    
    /**
     * 統計圖片總大小
     */
    @Query("SELECT COALESCE(SUM(pi.fileSize), 0) FROM ProductImage pi WHERE pi.productId = :productId")
    Long sumFileSizeByProductId(@Param("productId") Long productId);
    
    /**
     * 查詢大文件圖片（超過指定大小）
     */
    @Query("SELECT pi FROM ProductImage pi WHERE pi.fileSize > :maxSize ORDER BY pi.fileSize DESC")
    List<ProductImage> findLargeImages(@Param("maxSize") Long maxSize);
}
