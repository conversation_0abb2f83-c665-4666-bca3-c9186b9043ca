package com.example.dto;

import java.time.LocalDateTime;

/**
 * 收藏统计数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public class FavoriteStatsDto {
    
    private Long itemId;
    private Integer favoriteCount;
    private Boolean isFavorited;
    private LocalDateTime lastFavoriteTime;
    
    // 构造函数
    public FavoriteStatsDto() {
    }
    
    public FavoriteStatsDto(Long itemId, Integer favoriteCount, Boolean isFavorited, LocalDateTime lastFavoriteTime) {
        this.itemId = itemId;
        this.favoriteCount = favoriteCount;
        this.isFavorited = isFavorited;
        this.lastFavoriteTime = lastFavoriteTime;
    }
    
    // Getter和Setter方法
    public Long getItemId() {
        return itemId;
    }
    
    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }
    
    public Integer getFavoriteCount() {
        return favoriteCount;
    }
    
    public void setFavoriteCount(Integer favoriteCount) {
        this.favoriteCount = favoriteCount;
    }
    
    public Boolean getIsFavorited() {
        return isFavorited;
    }
    
    public void setIsFavorited(Boolean isFavorited) {
        this.isFavorited = isFavorited;
    }
    
    public LocalDateTime getLastFavoriteTime() {
        return lastFavoriteTime;
    }
    
    public void setLastFavoriteTime(LocalDateTime lastFavoriteTime) {
        this.lastFavoriteTime = lastFavoriteTime;
    }
    
    @Override
    public String toString() {
        return "FavoriteStatsDto{" +
                "itemId=" + itemId +
                ", favoriteCount=" + favoriteCount +
                ", isFavorited=" + isFavorited +
                ", lastFavoriteTime=" + lastFavoriteTime +
                '}';
    }
}
