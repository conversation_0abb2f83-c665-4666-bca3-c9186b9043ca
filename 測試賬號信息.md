# 測試賬號信息

## 用戶登錄測試賬號

### 主要測試賬號
- **用戶名**: how
- **密碼**: howhowhowtogo
- **郵箱**: <EMAIL>
- **狀態**: 已驗證用戶

### 其他測試賬號
- **用戶名**: playwright_test
- **密碼**: test123
- **郵箱**: <EMAIL>
- **狀態**: 測試用戶（手動創建）

## 管理員賬號
- **用戶名**: admin
- **密碼**: admin
- **權限**: 管理員權限

## 服務狀態
- **前端服務**: http://localhost:5173 ✅
- **後端服務**: http://localhost:8080 ✅
- **Redis服務**: localhost:6379 ✅ (已啟動)
- **MySQL數據庫**: java_springboot_redis_mail_login_test_250708 ✅
- **ngrok服務**: 8080端口映射 ✅

## 測試流程
1. 使用 how/howhowhowtogo 登錄系統
2. 訪問商品頁面
3. 測試購物車功能
4. 測試訂單創建
5. 測試支付寶支付流程

## 注意事項
- Redis服務已啟動，郵箱驗證碼功能可用
- ngrok已配置用於支付寶回調
- 所有服務都在正常運行狀態
