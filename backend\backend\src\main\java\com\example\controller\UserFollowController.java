package com.example.controller;

import com.example.dto.ApiResponse;
import com.example.entity.User;
import com.example.service.UserFollowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/user")
@CrossOrigin(origins = "*")
@Slf4j
public class UserFollowController {
    
    @Autowired
    private UserFollowService userFollowService;
    
    /**
     * 關注用戶
     */
    @PostMapping("/follow/{userId}")
    public ResponseEntity<ApiResponse<Map<String, Object>>> followUser(
            @PathVariable Long userId,
            Authentication authentication) {

        try {
            User currentUser = (User) authentication.getPrincipal();
            Long currentUserId = currentUser.getId();

            if (currentUserId.equals(userId)) {
                return ResponseEntity.badRequest().body(ApiResponse.error("不能關注自己"));
            }

            boolean success = userFollowService.followUser(currentUserId, userId);

            Map<String, Object> result = new HashMap<>();
            result.put("isFollowing", true); // 無論是新關注還是已經關注，最終狀態都是已關注

            if (success) {
                log.info("用戶 {} 關注了用戶 {}", currentUserId, userId);
                return ResponseEntity.ok(ApiResponse.success("關注成功", result));
            } else {
                log.info("用戶 {} 已經關注過用戶 {}", currentUserId, userId);
                return ResponseEntity.ok(ApiResponse.success("已經關注過該用戶", result));
            }

        } catch (Exception e) {
            log.error("關注用戶失敗: userId={}", userId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 取消關注用戶
     */
    @DeleteMapping("/follow/{userId}")
    public ResponseEntity<ApiResponse<Map<String, Object>>> unfollowUser(
            @PathVariable Long userId,
            Authentication authentication) {

        try {
            User currentUser = (User) authentication.getPrincipal();
            Long currentUserId = currentUser.getId();

            userFollowService.unfollowUser(currentUserId, userId);

            Map<String, Object> result = new HashMap<>();
            result.put("isFollowing", false);

            log.info("用戶 {} 取消關注了用戶 {}", currentUserId, userId);
            return ResponseEntity.ok(ApiResponse.success("取消關注成功", result));

        } catch (Exception e) {
            log.error("取消關注失敗: userId={}", userId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 檢查是否已關注某用戶
     */
    @GetMapping("/follow/status/{userId}")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getFollowStatus(
            @PathVariable Long userId,
            Authentication authentication) {

        try {
            User currentUser = (User) authentication.getPrincipal();
            Long currentUserId = currentUser.getId();

            boolean isFollowing = userFollowService.isFollowing(currentUserId, userId);
            boolean isFollowedBy = userFollowService.isFollowing(userId, currentUserId);

            Map<String, Object> result = new HashMap<>();
            result.put("isFollowing", isFollowing);
            result.put("isFollowedBy", isFollowedBy);
            result.put("isMutual", isFollowing && isFollowedBy);

            return ResponseEntity.ok(ApiResponse.success(result));

        } catch (Exception e) {
            log.error("檢查關注狀態失敗: userId={}", userId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error("檢查關注狀態失敗"));
        }
    }
    
    /**
     * 獲取關注列表
     */
    @GetMapping("/following")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getFollowingList(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {

        try {
            User currentUser = (User) authentication.getPrincipal();
            Long currentUserId = currentUser.getId();

            List<User> followingList = userFollowService.getFollowingList(currentUserId, page, size);
            long totalCount = userFollowService.getFollowingCount(currentUserId);

            Map<String, Object> result = new HashMap<>();
            result.put("data", followingList);
            result.put("totalCount", totalCount);
            result.put("page", page);
            result.put("size", size);

            return ResponseEntity.ok(ApiResponse.success(result));

        } catch (Exception e) {
            log.error("獲取關注列表失敗: userId={}", ((User) authentication.getPrincipal()).getId(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error("獲取關注列表失敗"));
        }
    }
    
    /**
     * 獲取粉絲列表
     */
    @GetMapping("/followers")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getFollowersList(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {

        try {
            User currentUser = (User) authentication.getPrincipal();
            Long currentUserId = currentUser.getId();

            List<User> followersList = userFollowService.getFollowersList(currentUserId, page, size);
            long totalCount = userFollowService.getFollowersCount(currentUserId);

            Map<String, Object> result = new HashMap<>();
            result.put("data", followersList);
            result.put("totalCount", totalCount);
            result.put("page", page);
            result.put("size", size);

            return ResponseEntity.ok(ApiResponse.success(result));

        } catch (Exception e) {
            log.error("獲取粉絲列表失敗: userId={}", ((User) authentication.getPrincipal()).getId(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error("獲取粉絲列表失敗"));
        }
    }
    
    /**
     * 獲取關注和粉絲數量統計
     */
    @GetMapping("/follow/stats")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getFollowStats(Authentication authentication) {

        try {
            User currentUser = (User) authentication.getPrincipal();
            Long currentUserId = currentUser.getId();

            long followingCount = userFollowService.getFollowingCount(currentUserId);
            long followersCount = userFollowService.getFollowersCount(currentUserId);

            Map<String, Object> stats = new HashMap<>();
            stats.put("followingCount", followingCount);
            stats.put("followersCount", followersCount);

            return ResponseEntity.ok(ApiResponse.success(stats));

        } catch (Exception e) {
            log.error("獲取關注統計失敗: userId={}", ((User) authentication.getPrincipal()).getId(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error("獲取統計信息失敗"));
        }
    }
    
    /**
     * 推薦用戶
     */
    @GetMapping("/recommend")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> recommendUsers(
            @RequestParam(defaultValue = "10") int limit,
            Authentication authentication) {

        try {
            User currentUser = (User) authentication.getPrincipal();
            Long currentUserId = currentUser.getId();

            List<User> recommendedUsers = userFollowService.recommendUsers(currentUserId, limit);

            // 轉換為包含更多信息的Map格式
            List<Map<String, Object>> result = recommendedUsers.stream()
                .map(user -> {
                    Map<String, Object> userInfo = new HashMap<>();
                    userInfo.put("id", user.getId());
                    userInfo.put("username", user.getUsername());
                    userInfo.put("email", user.getEmail());
                    userInfo.put("realName", user.getRealName());
                    userInfo.put("createdAt", user.getCreatedAt());
                    return userInfo;
                })
                .toList();

            return ResponseEntity.ok(ApiResponse.success(result));

        } catch (Exception e) {
            log.error("推薦用戶失敗: userId={}", ((User) authentication.getPrincipal()).getId(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error("獲取推薦用戶失敗"));
        }
    }
    
    /**
     * 獲取與指定用戶的共同關注
     */
    @GetMapping("/mutual-follows/{userId}")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getMutualFollows(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {

        try {
            User currentUser = (User) authentication.getPrincipal();
            Long currentUserId = currentUser.getId();

            List<User> mutualFollows = userFollowService.getMutualFollowing(currentUserId, userId, page, size);
            long totalCount = userFollowService.getMutualFollowingCount(currentUserId, userId);

            // 轉換為包含更多信息的Map格式
            List<Map<String, Object>> userList = mutualFollows.stream()
                .map(user -> {
                    Map<String, Object> userInfo = new HashMap<>();
                    userInfo.put("id", user.getId());
                    userInfo.put("username", user.getUsername());
                    userInfo.put("email", user.getEmail());
                    userInfo.put("realName", user.getRealName());
                    userInfo.put("createdAt", user.getCreatedAt());
                    return userInfo;
                })
                .toList();

            Map<String, Object> result = new HashMap<>();
            result.put("data", userList);
            result.put("count", totalCount);
            result.put("page", page);
            result.put("size", size);

            return ResponseEntity.ok(ApiResponse.success(result));

        } catch (Exception e) {
            log.error("獲取共同關注失敗: userId={}", userId, e);
            return ResponseEntity.badRequest().body(ApiResponse.error("獲取共同關注失敗"));
        }
    }
}
