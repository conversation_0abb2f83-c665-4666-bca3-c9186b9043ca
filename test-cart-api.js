// 購物車和支付 API 測試腳本
// 使用方法: node test-cart-api.js

const BASE_URL = 'http://localhost:8080';

// 測試用戶 token (需要先登錄獲取)
const TEST_TOKEN = 'your-jwt-token-here';

// 測試數據
const TEST_PRODUCT_ID = 1;
const TEST_QUANTITY = 2;
const TEST_ORDER_DATA = {
  receiverName: '張三',
  receiverPhone: '13800138000',
  receiverAddress: '北京市朝陽區測試街道123號',
  remark: '測試訂單'
};

// HTTP 請求函數
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TEST_TOKEN}`,
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json();
    console.log(`${options.method || 'GET'} ${url}`);
    console.log(`狀態: ${response.status}`);
    console.log(`響應:`, data);
    console.log('---');
    
    return { response, data };
  } catch (error) {
    console.error(`請求失敗: ${url}`, error.message);
    return { error };
  }
}

// 測試購物車 API
async function testCartAPI() {
  console.log('=== 測試購物車 API ===');
  
  // 1. 獲取購物車
  await makeRequest(`${BASE_URL}/api/cart`);
  
  // 2. 添加商品到購物車
  await makeRequest(`${BASE_URL}/api/cart/add?productId=${TEST_PRODUCT_ID}&quantity=${TEST_QUANTITY}`, {
    method: 'POST'
  });
  
  // 3. 再次獲取購物車，驗證商品已添加
  const { data: cartData } = await makeRequest(`${BASE_URL}/api/cart`);
  
  if (cartData && cartData.success && cartData.data.cartItems && cartData.data.cartItems.length > 0) {
    const cartItemId = cartData.data.cartItems[0].id;
    
    // 4. 更新商品數量
    await makeRequest(`${BASE_URL}/api/cart/update/${cartItemId}?quantity=3`, {
      method: 'PUT'
    });
    
    // 5. 切換選中狀態
    await makeRequest(`${BASE_URL}/api/cart/toggle-selected/${cartItemId}?selected=0`, {
      method: 'PUT'
    });
    
    await makeRequest(`${BASE_URL}/api/cart/toggle-selected/${cartItemId}?selected=1`, {
      method: 'PUT'
    });
    
    // 6. 獲取選中項目
    await makeRequest(`${BASE_URL}/api/cart/selected`);
  }
  
  // 7. 清空購物車 (可選)
  // await makeRequest(`${BASE_URL}/api/cart/clear`, { method: 'DELETE' });
}

// 測試訂單 API
async function testOrderAPI() {
  console.log('=== 測試訂單 API ===');
  
  // 1. 從購物車創建訂單
  const orderParams = new URLSearchParams(TEST_ORDER_DATA);
  const { data: orderData } = await makeRequest(`${BASE_URL}/api/orders/create-from-cart`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: orderParams
  });
  
  let orderId = null;
  if (orderData && orderData.success && orderData.data) {
    orderId = orderData.data.id;
    console.log(`創建的訂單 ID: ${orderId}`);
    
    // 2. 獲取訂單詳情
    await makeRequest(`${BASE_URL}/api/orders/${orderId}`);
  }
  
  // 3. 獲取訂單列表
  await makeRequest(`${BASE_URL}/api/orders?page=0&size=10`);
  
  // 4. 取消訂單 (如果需要)
  if (orderId) {
    // await makeRequest(`${BASE_URL}/api/orders/${orderId}/cancel`, { method: 'PUT' });
  }
  
  return orderId;
}

// 測試支付 API
async function testPaymentAPI(orderId) {
  if (!orderId) {
    console.log('沒有訂單 ID，跳過支付測試');
    return;
  }
  
  console.log('=== 測試支付 API ===');
  
  // 1. 發起支付寶支付
  await makeRequest(`${BASE_URL}/api/payment/alipay/create?orderId=${orderId}`, {
    method: 'POST'
  });
  
  // 2. 查詢支付狀態
  await makeRequest(`${BASE_URL}/api/payment/status/${orderId}`);
  
  // 3. 管理員確認支付 (測試功能)
  // const orderNumber = `ORD${Date.now()}`;
  // await makeRequest(`${BASE_URL}/api/payment/admin/confirm?orderNumber=${orderNumber}`, {
  //   method: 'POST'
  // });
}

// 測試商品 API (確保有商品可以添加到購物車)
async function testProductAPI() {
  console.log('=== 測試商品 API ===');
  
  // 獲取商品列表
  await makeRequest(`${BASE_URL}/api/products?page=0&size=10`);
  
  // 獲取商品詳情
  await makeRequest(`${BASE_URL}/api/products/${TEST_PRODUCT_ID}`);
}

// 主測試函數
async function runTests() {
  console.log('開始 API 測試...');
  console.log(`基礎 URL: ${BASE_URL}`);
  console.log(`測試 Token: ${TEST_TOKEN.substring(0, 20)}...`);
  console.log('');
  
  try {
    // 測試商品 API
    await testProductAPI();
    
    // 測試購物車 API
    await testCartAPI();
    
    // 測試訂單 API
    const orderId = await testOrderAPI();
    
    // 測試支付 API
    await testPaymentAPI(orderId);
    
    console.log('所有測試完成！');
  } catch (error) {
    console.error('測試過程中出現錯誤:', error);
  }
}

// 檢查環境
if (typeof fetch === 'undefined') {
  console.log('需要 Node.js 18+ 或安裝 node-fetch');
  console.log('安裝命令: npm install node-fetch');
  process.exit(1);
}

// 運行測試
runTests();
