import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

export interface Admin {
  id: number
  username: string
  email: string
  realName?: string
  phoneNumber?: string
  role: 'ADMIN' | 'SUPER_ADMIN'
  enabled: boolean
  lastLoginAt?: string
  createdAt: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface ApiResponse<T> {
  success: boolean
  message: string
  data: T
}

export const useAdminStore = defineStore('admin', () => {
  const admin = ref<Admin | null>(null)
  const token = ref<string | null>(localStorage.getItem('admin_token'))
  
  const isLoggedIn = computed(() => !!token.value && !!admin.value)
  
  // 創建專用的 axios 實例
  const adminAPI = axios.create({
    baseURL: 'http://localhost:8080/api/admin',
    timeout: 10000
  })
  
  // 請求攔截器
  adminAPI.interceptors.request.use(
    (config) => {
      if (token.value) {
        config.headers.Authorization = `Bearer ${token.value}`
      }
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )
  
  // 響應攔截器
  adminAPI.interceptors.response.use(
    (response) => {
      return response.data
    },
    (error) => {
      if (error.response?.status === 401) {
        logout()
      }
      return Promise.reject(error.response?.data || error)
    }
  )
  
  const login = async (loginData: LoginRequest) => {
    try {
      const response: ApiResponse<{ token: string; admin: Admin }> = await adminAPI.post('/auth/login', loginData)
      
      if (response.success) {
        token.value = response.data.token
        admin.value = response.data.admin
        
        localStorage.setItem('admin_token', token.value)
        localStorage.setItem('admin_info', JSON.stringify(admin.value))
        
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (error: any) {
      throw new Error(error.message || '登入失敗')
    }
  }
  
  const logout = () => {
    token.value = null
    admin.value = null
    
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_info')
  }
  
  const getProfile = async () => {
    try {
      const response: ApiResponse<Admin> = await adminAPI.get('/auth/profile')
      
      if (response.success) {
        admin.value = response.data
        localStorage.setItem('admin_info', JSON.stringify(admin.value))
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (error: any) {
      throw new Error(error.message || '獲取管理員信息失敗')
    }
  }
  
  const initializeFromStorage = () => {
    const storedToken = localStorage.getItem('admin_token')
    const storedAdmin = localStorage.getItem('admin_info')
    
    if (storedToken && storedAdmin) {
      token.value = storedToken
      try {
        admin.value = JSON.parse(storedAdmin)
      } catch {
        logout()
      }
    }
  }
  
  // 身份認證相關 API
  const identityAPI = {
    // 獲取待審核列表
    getPendingVerifications: (): Promise<ApiResponse<any[]>> => 
      adminAPI.get('/identity/pending'),
    
    // 審核身份認證
    reviewVerification: (id: number, approved: boolean, comment?: string): Promise<ApiResponse<string>> =>
      adminAPI.post(`/identity/review/${id}`, null, {
        params: { approved, comment }
      }),
    
    // 獲取認證詳情
    getVerificationDetail: (id: number): Promise<ApiResponse<any>> => 
      adminAPI.get(`/identity/${id}`)
  }
  
  return {
    admin,
    token,
    isLoggedIn,
    login,
    logout,
    getProfile,
    initializeFromStorage,
    identityAPI
  }
})
