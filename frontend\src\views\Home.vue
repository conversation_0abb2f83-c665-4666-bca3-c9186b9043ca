<template>
  <div class="home-container">
    <div class="dashboard-content">
      <!-- 歡迎區塊 -->
      <div class="welcome-section">
        <div class="welcome-bg-pattern"></div>
        <div class="welcome-content">
          <div class="welcome-text">
            <div class="greeting-badge">
              <el-icon><Star /></el-icon>
              歡迎回來
            </div>
            <h1 class="welcome-title">
              你好，{{ userStore.user?.realName || userStore.user?.username }}
            </h1>
            <p class="welcome-subtitle">
              在這裡管理您的帳戶信息、身份認證和個人設置
            </p>
          </div>
          <div class="welcome-actions">
            <el-button type="primary" size="large" round @click="router.push('/app/profile')">
              <el-icon><Edit /></el-icon>
              更新資料
            </el-button>
          </div>
        </div>
      </div>

      <!-- 狀態概覽 -->
      <div class="overview-section">
        <div class="section-header">
          <h2 class="section-title">
            <el-icon><DataBoard /></el-icon>
            帳戶狀態
          </h2>
          <p class="section-subtitle">查看您的帳戶驗證和認證狀態</p>
        </div>
        
        <div class="status-grid">
          <div class="status-card email-card" :class="{ 'verified': userStore.user?.emailVerified }">
            <div class="card-header">
              <div class="status-icon email-icon">
                <el-icon><Message /></el-icon>
              </div>
              <div class="status-badge" :class="userStore.user?.emailVerified ? 'success' : 'warning'">
                {{ userStore.user?.emailVerified ? '已驗證' : '待驗證' }}
              </div>
            </div>
            <div class="card-content">
              <h3>郵箱驗證</h3>
              <p>{{ userStore.user?.emailVerified ? '您的郵箱已通過驗證' : '請完成郵箱驗證以確保帳戶安全' }}</p>
            </div>
          </div>

          <div class="status-card identity-card" :class="getIdentityCardClass()">
            <div class="card-header">
              <div class="status-icon identity-icon">
                <el-icon><CreditCard /></el-icon>
              </div>
              <div class="status-badge" :class="getIdentityBadgeClass()">
                {{ userStore.identityStatusText }}
              </div>
            </div>
            <div class="card-content">
              <h3>身份認證</h3>
              <p>{{ getIdentityDescription() }}</p>
            </div>
          </div>

          <div class="status-card role-card">
            <div class="card-header">
              <div class="status-icon role-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="status-badge info">
                {{ userStore.user?.role === 'ADMIN' ? '管理員' : '普通用戶' }}
              </div>
            </div>
            <div class="card-content">
              <h3>用戶角色</h3>
              <p>{{ userStore.user?.role === 'ADMIN' ? '您擁有系統管理權限' : '標準用戶權限' }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 快捷操作 -->
      <div class="actions-section">
        <div class="section-header">
          <h2 class="section-title">
            <el-icon><Operation /></el-icon>
            快捷操作
          </h2>
          <p class="section-subtitle">常用功能快速入口</p>
        </div>
        
        <div class="actions-grid">
          <div class="action-card profile-card" @click="router.push('/app/profile')">
            <div class="action-icon-wrapper">
              <div class="action-icon">
                <el-icon><User /></el-icon>
              </div>
            </div>
            <div class="action-content">
              <h3>個人資料</h3>
              <p>管理您的個人信息和偏好設置</p>
            </div>
            <div class="action-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
          
          <div class="action-card identity-action-card" @click="router.push('/app/identity')">
            <div class="action-icon-wrapper">
              <div class="action-icon">
                <el-icon><CreditCard /></el-icon>
              </div>
            </div>
            <div class="action-content">
              <h3>身份認證</h3>
              <p>提交或查看身份驗證狀態</p>
            </div>
            <div class="action-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
          
          <div class="action-card admin-card" v-if="userStore.isAdmin" @click="router.push('/admin')">
            <div class="action-icon-wrapper">
              <div class="action-icon">
                <el-icon><Setting /></el-icon>
              </div>
            </div>
            <div class="action-content">
              <h3>管理後台</h3>
              <p>系統管理和用戶審核</p>
            </div>
            <div class="action-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
          
          <div class="action-card logout-card" @click="handleLogout">
            <div class="action-icon-wrapper">
              <div class="action-icon">
                <el-icon><SwitchButton /></el-icon>
              </div>
            </div>
            <div class="action-content">
              <h3>安全登出</h3>
              <p>退出當前帳戶</p>
            </div>
            <div class="action-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Message,
  CreditCard,
  User,
  Setting,
  SwitchButton,
  Edit,
  Star,
  DataBoard,
  Operation,
  ArrowRight
} from '@element-plus/icons-vue'
import { useUserStore } from '../stores/user'

const router = useRouter()
const userStore = useUserStore()

onMounted(() => {
  if (userStore.isLoggedIn) {
    userStore.fetchUserProfile()
  }
})

const getIdentityStatusClass = () => {
  if (!userStore.user) return 'status-info'
  
  switch (userStore.user.identityStatus) {
    case 'APPROVED': return 'status-success'
    case 'REJECTED': return 'status-danger'
    case 'PENDING': return 'status-warning'
    default: return 'status-info'
  }
}

const getIdentityCardClass = () => {
  if (!userStore.user) return ''
  
  switch (userStore.user.identityStatus) {
    case 'APPROVED': return 'approved'
    case 'REJECTED': return 'rejected'
    case 'PENDING': return 'pending'
    default: return ''
  }
}

const getIdentityBadgeClass = () => {
  if (!userStore.user) return 'info'
  
  switch (userStore.user.identityStatus) {
    case 'APPROVED': return 'success'
    case 'REJECTED': return 'danger'
    case 'PENDING': return 'warning'
    default: return 'info'
  }
}

const getIdentityDescription = () => {
  if (!userStore.user) return '請提交身份認證資料'
  
  switch (userStore.user.identityStatus) {
    case 'APPROVED': return '您的身份已通過認證'
    case 'REJECTED': return '身份認證被拒絕，請重新提交'
    case 'PENDING': return '身份認證正在審核中'
    default: return '請提交身份認證資料'
  }
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('確定要登出嗎？', '提示', {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    userStore.logout()
    ElMessage.success('已登出')
    router.push('/login')
  } catch {
    // 用戶取消
  }
}
</script>

<style scoped>
/* 全局容器 */
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px 0;
}

.dashboard-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 歡迎區塊 */
.welcome-section {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 32px;
  color: white;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
}

.welcome-content {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 32px;
}

.greeting-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
  backdrop-filter: blur(10px);
}

.welcome-title {
  font-size: 42px;
  font-weight: 700;
  margin: 0 0 12px 0;
  line-height: 1.2;
  background: linear-gradient(45deg, #fff, #e0e7ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 18px;
  opacity: 0.9;
  margin: 0;
  max-width: 500px;
  line-height: 1.6;
}

/* 區塊標題 */
.section-header {
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.section-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
}

/* 狀態概覽 */
.overview-section {
  margin-bottom: 48px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
}

.status-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
  overflow: hidden;
}

.status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #e2e8f0;
}

.status-card.verified::before,
.status-card.approved::before {
  background: #48bb78;
}

.status-card.pending::before {
  background: #ed8936;
}

.status-card.rejected::before {
  background: #f56565;
}

.status-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.status-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.email-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.identity-icon {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
}

.role-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.success {
  background: #f0fff4;
  color: #38a169;
  border: 1px solid #9ae6b4;
}

.status-badge.warning {
  background: #fffaf0;
  color: #dd6b20;
  border: 1px solid #fbd38d;
}

.status-badge.danger {
  background: #fff5f5;
  color: #e53e3e;
  border: 1px solid #feb2b2;
}

.status-badge.info {
  background: #ebf8ff;
  color: #3182ce;
  border: 1px solid #90cdf4;
}

.card-content h3 {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 8px 0;
}

.card-content p {
  font-size: 14px;
  color: #718096;
  margin: 0;
  line-height: 1.5;
}

/* 快捷操作 */
.actions-section {
  margin-bottom: 48px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.action-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.action-icon-wrapper {
  flex-shrink: 0;
}

.action-icon {
  width: 64px;
  height: 64px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.identity-action-card .action-icon {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  box-shadow: 0 8px 20px rgba(240, 147, 251, 0.3);
}

.admin-card .action-icon {
  background: linear-gradient(135deg, #ffecd2, #fcb69f);
  color: #d69e2e;
  box-shadow: 0 8px 20px rgba(252, 182, 159, 0.3);
}

.logout-card .action-icon {
  background: linear-gradient(135deg, #ff9a9e, #fecfef);
  color: #e53e3e;
  box-shadow: 0 8px 20px rgba(255, 154, 158, 0.3);
}

.action-content {
  flex: 1;
}

.action-content h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 6px 0;
}

.action-content p {
  font-size: 14px;
  color: #718096;
  margin: 0;
  line-height: 1.4;
}

.action-arrow {
  color: #cbd5e0;
  font-size: 20px;
  transition: color 0.2s ease;
}

.action-card:hover .action-arrow {
  color: #667eea;
}

/* 響應式設計 */
@media (max-width: 1200px) {
  .dashboard-content {
    max-width: 100%;
    padding: 0 20px;
  }
  
  .status-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
  
  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .home-container {
    padding: 16px 0;
  }
  
  .dashboard-content {
    padding: 0 16px;
  }
  
  .welcome-section {
    padding: 32px 24px;
    margin-bottom: 32px;
  }
  
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .welcome-title {
    font-size: 32px;
  }
  
  .welcome-subtitle {
    font-size: 16px;
  }
  
  .section-title {
    font-size: 24px;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .status-card {
    padding: 24px;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .action-card {
    padding: 20px;
  }
  
  .action-icon {
    width: 56px;
    height: 56px;
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .welcome-section {
    padding: 24px 20px;
  }
  
  .welcome-title {
    font-size: 28px;
  }
  
  .section-title {
    font-size: 22px;
  }
  
  .status-card {
    padding: 20px;
  }
  
  .status-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }
  
  .action-card {
    padding: 16px;
    gap: 16px;
  }
  
  .action-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }
}

/* 大屏幕優化 */
@media (min-width: 1920px) {
  .dashboard-content {
    max-width: 1600px;
  }
  
  .welcome-section {
    padding: 60px;
  }
  
  .welcome-title {
    font-size: 48px;
  }
  
  .welcome-subtitle {
    font-size: 20px;
  }
  
  .section-title {
    font-size: 32px;
  }
  
  .status-card {
    padding: 40px;
  }
  
  .action-card {
    padding: 32px;
  }
}
</style>
