<template>
  <div class="checkout-view">
    <div class="container">
      <h1 class="page-title">確認訂單</h1>
      
      <div class="checkout-content">
        <!-- 收貨地址 -->
        <div class="section address-section">
          <div class="section-header">
            <h2 class="section-title">
              <el-icon><LocationFilled /></el-icon>
              收貨地址
            </h2>
            <el-button type="text" @click="showAddressManager = true">
              <el-icon><Setting /></el-icon>
              管理地址
            </el-button>
          </div>

          <!-- 常用地址選擇 -->
          <div class="saved-addresses" v-if="savedAddresses.length > 0">
            <div class="address-tabs">
              <div
                v-for="address in savedAddresses"
                :key="address.id"
                class="address-tab"
                :class="{ active: selectedAddressId === address.id }"
                @click="selectAddress(address)"
              >
                <div class="address-info">
                  <div class="address-name">{{ address.receiverName }} {{ address.receiverPhone }}</div>
                  <div class="address-detail">{{ address.receiverAddress }}</div>
                </div>
                <div class="address-actions">
                  <el-tag v-if="address.isDefault" type="success" size="small">默認</el-tag>
                  <el-button type="text" size="small" @click.stop="editAddress(address)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
            <el-button link @click="showNewAddressForm = true" class="add-address-btn">
              <el-icon><Plus /></el-icon>
              添加新地址
            </el-button>
          </div>

          <!-- 新地址表單 -->
          <div class="address-form" v-if="showNewAddressForm || savedAddresses.length === 0">
            <div class="form-row">
              <div class="form-group">
                <label>收貨人姓名 *</label>
                <el-input
                  v-model="orderForm.receiverName"
                  placeholder="請輸入收貨人姓名"
                  size="large"
                />
              </div>
              <div class="form-group">
                <label>聯繫電話 *</label>
                <el-input
                  v-model="orderForm.receiverPhone"
                  placeholder="請輸入聯繫電話"
                  size="large"
                />
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>省市區 *</label>
                <el-cascader
                  v-model="selectedRegion"
                  :options="regionOptions"
                  placeholder="請選擇省市區"
                  size="large"
                  @change="handleRegionChange"
                />
              </div>
            </div>
            <div class="form-group">
              <label>詳細地址 *</label>
              <el-input
                type="textarea"
                v-model="orderForm.receiverAddress"
                placeholder="請輸入詳細收貨地址"
                :rows="3"
                size="large"
              />
            </div>
            <div class="form-actions">
              <el-checkbox v-model="saveAsDefault">設為默認地址</el-checkbox>
              <el-button link @click="saveCurrentAddress">
                <el-icon><DocumentAdd /></el-icon>
                保存地址
              </el-button>
            </div>
          </div>

          <!-- 訂單備註 -->
          <div class="form-group remark-group">
            <label>訂單備註</label>
            <el-input
              type="textarea"
              v-model="orderForm.remark"
              placeholder="選填，對本次訂單的說明（建議先和商家溝通確認）"
              :rows="2"
              size="large"
            />
          </div>
        </div>

        <!-- 地址管理對話框 -->
        <el-dialog
          v-model="showAddressManager"
          title="地址管理"
          width="600px"
        >
          <div class="address-manager">
            <div v-for="address in savedAddresses" :key="address.id" class="address-item">
              <div class="address-content">
                <div class="address-header">
                  <span class="receiver-info">{{ address.receiverName }} {{ address.receiverPhone }}</span>
                  <el-tag v-if="address.isDefault" type="success" size="small">默認</el-tag>
                </div>
                <div class="address-text">{{ address.receiverAddress }}</div>
              </div>
              <div class="address-operations">
                <el-button type="text" @click="editAddress(address)">編輯</el-button>
                <el-button type="text" @click="setDefaultAddress(address)" v-if="!address.isDefault">設為默認</el-button>
                <el-button link @click="deleteAddress(address)" class="delete-btn">刪除</el-button>
              </div>
            </div>
          </div>
        </el-dialog>
        
        <!-- 商品清單 -->
        <div class="section products-section">
          <div class="section-header">
            <h2 class="section-title">
              <el-icon><ShoppingBag /></el-icon>
              商品清單
            </h2>
            <span class="item-count">共 {{ selectedItems.length }} 件商品</span>
          </div>
          <div class="product-list">
            <div
              v-for="item in selectedItems"
              :key="item.id"
              class="product-item"
            >
              <div class="product-image">
                <img :src="item.productImageUrl || '/default-product.jpg'" :alt="item.productName" />
              </div>
              <div class="product-info">
                <h3 class="product-name">{{ item.productName }}</h3>
                <div class="product-specs" v-if="item.specs">
                  <span v-for="(value, key) in item.specs" :key="key" class="spec-tag">
                    {{ key }}: {{ value }}
                  </span>
                </div>
                <div class="product-price">
                  <span class="current-price">¥{{ item.price.toFixed(2) }}</span>
                  <span v-if="item.originalPrice && item.originalPrice > item.price" class="original-price">
                    ¥{{ item.originalPrice.toFixed(2) }}
                  </span>
                </div>
              </div>
              <div class="product-quantity">
                <span class="quantity-label">數量</span>
                <span class="quantity-value">{{ item.quantity }}</span>
              </div>
              <div class="product-subtotal">
                <span class="subtotal-label">小計</span>
                <span class="subtotal-value">¥{{ (item.price * item.quantity).toFixed(2) }}</span>
              </div>
            </div>
          </div>

          <!-- 配送方式 -->
          <div class="delivery-section">
            <h3 class="delivery-title">
              <el-icon><Truck /></el-icon>
              配送方式
            </h3>
            <div class="delivery-options">
              <el-radio-group v-model="selectedDeliveryMethod" @change="calculateShipping">
                <el-radio
                  v-for="method in deliveryMethods"
                  :key="method.id"
                  :label="method.id"
                  class="delivery-option"
                >
                  <div class="delivery-info">
                    <div class="delivery-name">{{ method.name }}</div>
                    <div class="delivery-desc">{{ method.description }}</div>
                    <div class="delivery-price">
                      {{ method.price === 0 ? '免費' : `¥${method.price.toFixed(2)}` }}
                    </div>
                  </div>
                </el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
        
        <!-- 支付方式 -->
        <div class="section payment-section">
          <h2 class="section-title">
            <el-icon><CreditCard /></el-icon>
            支付方式
          </h2>
          <div class="payment-methods">
            <el-radio-group v-model="orderForm.paymentMethod" class="payment-group">
              <el-radio value="alipay" class="payment-method">
                <div class="payment-info">
                  <div class="payment-icon">
                    <img src="/alipay-icon.png" alt="支付寶" />
                  </div>
                  <div class="payment-details">
                    <div class="payment-name">支付寶</div>
                    <div class="payment-desc">推薦使用支付寶，安全快捷</div>
                  </div>
                </div>
              </el-radio>
              <el-radio value="wechat" class="payment-method" disabled>
                <div class="payment-info">
                  <div class="payment-icon">
                    <img src="/wechat-icon.png" alt="微信支付" />
                  </div>
                  <div class="payment-details">
                    <div class="payment-name">微信支付</div>
                    <div class="payment-desc">暫未開通，敬請期待</div>
                  </div>
                </div>
              </el-radio>
            </el-radio-group>
          </div>
        </div>

        <!-- 訂單摘要 -->
        <div class="section summary-section">
          <h2 class="section-title">
            <el-icon><DocumentCopy /></el-icon>
            訂單摘要
          </h2>
          <div class="summary-details">
            <div class="summary-row">
              <span>商品總數：</span>
              <span>{{ totalQuantity }} 件</span>
            </div>
            <div class="summary-row">
              <span>商品總價：</span>
              <span>¥{{ itemTotal.toFixed(2) }}</span>
            </div>
            <div class="summary-row">
              <span>運費：</span>
              <span class="shipping-fee">
                {{ shippingFee === 0 ? '免運費' : `¥${shippingFee.toFixed(2)}` }}
              </span>
            </div>
            <div class="summary-row" v-if="discount > 0">
              <span>優惠減免：</span>
              <span class="discount-amount">-¥{{ discount.toFixed(2) }}</span>
            </div>
            <div class="summary-row total-row">
              <span>應付總額：</span>
              <span class="total-amount">¥{{ finalTotal.toFixed(2) }}</span>
            </div>
          </div>

          <!-- 優惠券 -->
          <div class="coupon-section">
            <div class="coupon-header">
              <span>優惠券</span>
              <el-button type="text" @click="showCouponDialog = true">
                <el-icon><Ticket /></el-icon>
                選擇優惠券
              </el-button>
            </div>
            <div class="selected-coupon" v-if="selectedCoupon">
              <div class="coupon-info">
                <span class="coupon-name">{{ selectedCoupon.name }}</span>
                <span class="coupon-discount">-¥{{ selectedCoupon.discount.toFixed(2) }}</span>
              </div>
              <el-button type="text" @click="removeCoupon">移除</el-button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部操作欄 -->
      <div class="checkout-footer">
        <div class="footer-left">
          <el-button @click="goBack" size="large">
            <el-icon><ArrowLeft /></el-icon>
            返回購物車
          </el-button>
        </div>
        <div class="footer-right">
          <div class="footer-summary">
            <div class="summary-line">
              <span class="item-count">共 {{ totalQuantity }} 件商品</span>
            </div>
            <div class="summary-line">
              <span class="total-label">合計：</span>
              <span class="total-amount">¥{{ finalTotal.toFixed(2) }}</span>
            </div>
          </div>
          <el-button
            type="primary"
            size="large"
            @click="submitOrder"
            :disabled="!canSubmit || submitting"
            :loading="submitting"
            class="submit-btn"
          >
            {{ submitting ? '提交中...' : '提交訂單' }}
          </el-button>
        </div>
      </div>

      <!-- 優惠券選擇對話框 -->
      <el-dialog
        v-model="showCouponDialog"
        title="選擇優惠券"
        width="500px"
      >
        <div class="coupon-list">
          <div
            v-for="coupon in availableCoupons"
            :key="coupon.id"
            class="coupon-item"
            :class="{ selected: selectedCoupon?.id === coupon.id }"
            @click="selectCoupon(coupon)"
          >
            <div class="coupon-content">
              <div class="coupon-amount">¥{{ coupon.discount }}</div>
              <div class="coupon-info">
                <div class="coupon-name">{{ coupon.name }}</div>
                <div class="coupon-condition">{{ coupon.condition }}</div>
              </div>
            </div>
            <div class="coupon-status">
              <el-icon v-if="selectedCoupon?.id === coupon.id"><Check /></el-icon>
            </div>
          </div>
        </div>
        <template #footer>
          <el-button @click="showCouponDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmCoupon">確定</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  LocationFilled, Setting, Edit, Plus, DocumentAdd, ShoppingBag,
  Truck, CreditCard, DocumentCopy, Ticket, ArrowLeft, Check
} from '@element-plus/icons-vue'

interface CartItem {
  id: number
  productId: number
  productName: string
  productImageUrl: string
  price: number
  originalPrice?: number
  quantity: number
  selected: boolean
  specs?: Record<string, string>
}

interface OrderForm {
  receiverName: string
  receiverPhone: string
  receiverAddress: string
  remark: string
  paymentMethod: string
}

interface SavedAddress {
  id: number
  receiverName: string
  receiverPhone: string
  receiverAddress: string
  isDefault: boolean
}

interface DeliveryMethod {
  id: string
  name: string
  description: string
  price: number
}

interface Coupon {
  id: number
  name: string
  discount: number
  condition: string
  minAmount: number
}

const router = useRouter()
const selectedItems = ref<CartItem[]>([])
const submitting = ref(false)

// 地址相關
const savedAddresses = ref<SavedAddress[]>([])
const selectedAddressId = ref<number | null>(null)
const showAddressManager = ref(false)
const showNewAddressForm = ref(false)
const saveAsDefault = ref(false)
const selectedRegion = ref<string[]>([])

// 配送相關
const selectedDeliveryMethod = ref('standard')
const deliveryMethods = ref<DeliveryMethod[]>([
  { id: 'standard', name: '標準配送', description: '5-7個工作日', price: 0 },
  { id: 'express', name: '快速配送', description: '2-3個工作日', price: 10 },
  { id: 'same-day', name: '當日達', description: '當日送達（限部分地區）', price: 20 }
])

// 優惠券相關
const showCouponDialog = ref(false)
const selectedCoupon = ref<Coupon | null>(null)
const availableCoupons = ref<Coupon[]>([])

// 地區選項（簡化版，實際項目中應該從API獲取）
const regionOptions = ref([
  {
    value: 'beijing',
    label: '北京市',
    children: [
      { value: 'chaoyang', label: '朝陽區' },
      { value: 'haidian', label: '海淀區' }
    ]
  },
  {
    value: 'shanghai',
    label: '上海市',
    children: [
      { value: 'huangpu', label: '黃浦區' },
      { value: 'pudong', label: '浦東新區' }
    ]
  }
])

const orderForm = ref<OrderForm>({
  receiverName: '',
  receiverPhone: '',
  receiverAddress: '',
  remark: '',
  paymentMethod: 'alipay'
})

// 計算屬性
const totalQuantity = computed(() => 
  selectedItems.value.reduce((total, item) => total + item.quantity, 0)
)

const itemTotal = computed(() =>
  selectedItems.value.reduce((total, item) => total + (item.price * item.quantity), 0)
)

const shippingFee = computed(() => {
  const method = deliveryMethods.value.find(m => m.id === selectedDeliveryMethod.value)
  return method ? method.price : 0
})

const discount = computed(() => {
  return selectedCoupon.value ? selectedCoupon.value.discount : 0
})

const finalTotal = computed(() =>
  Math.max(0, itemTotal.value + shippingFee.value - discount.value)
)

const canSubmit = computed(() =>
  orderForm.value.receiverName.trim() !== '' &&
  orderForm.value.receiverPhone.trim() !== '' &&
  orderForm.value.receiverAddress.trim() !== '' &&
  selectedItems.value.length > 0 &&
  !submitting.value
)

// 方法
const loadSelectedItems = async () => {
  try {
    const response = await fetch('/api/cart/selected', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data) {
        selectedItems.value = result.data
      }
    }
    
    if (selectedItems.value.length === 0) {
      ElMessage.warning('沒有選中的商品')
      router.push('/cart')
    }
  } catch (error) {
    console.error('加載選中商品失敗:', error)
    ElMessage.error('加載商品信息失敗')
    router.push('/cart')
  }
}

const submitOrder = async () => {
  if (!canSubmit.value) {
    ElMessage.warning('請填寫完整的收貨信息')
    return
  }
  
  try {
    submitting.value = true
    
    const response = await fetch('/api/orders/create-from-cart', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: new URLSearchParams({
        receiverName: orderForm.value.receiverName,
        receiverPhone: orderForm.value.receiverPhone,
        receiverAddress: orderForm.value.receiverAddress,
        remark: orderForm.value.remark || ''
      })
    })
    
    const result = await response.json()
    
    if (result.success && result.data) {
      ElMessage.success('訂單創建成功')
      // 跳轉到支付頁面
      router.push(`/app/payment/${result.data.id}`)
    } else {
      ElMessage.error(result.message || '創建訂單失敗')
    }
  } catch (error) {
    console.error('提交訂單失敗:', error)
    ElMessage.error('提交訂單失敗')
  } finally {
    submitting.value = false
  }
}

// 地址管理相關方法
const loadSavedAddresses = async () => {
  try {
    // 這裡應該從API加載用戶保存的地址
    // 模擬數據
    savedAddresses.value = [
      {
        id: 1,
        receiverName: '張三',
        receiverPhone: '13800138000',
        receiverAddress: '北京市朝陽區某某街道123號',
        isDefault: true
      }
    ]

    if (savedAddresses.value.length > 0) {
      const defaultAddress = savedAddresses.value.find(addr => addr.isDefault)
      if (defaultAddress) {
        selectAddress(defaultAddress)
      }
    }
  } catch (error) {
    console.warn('加載地址失敗:', error)
  }
}

const selectAddress = (address: SavedAddress) => {
  selectedAddressId.value = address.id
  orderForm.value.receiverName = address.receiverName
  orderForm.value.receiverPhone = address.receiverPhone
  orderForm.value.receiverAddress = address.receiverAddress
  showNewAddressForm.value = false
}

const editAddress = (address: SavedAddress) => {
  selectAddress(address)
  showNewAddressForm.value = true
  showAddressManager.value = false
}

const saveCurrentAddress = async () => {
  if (!orderForm.value.receiverName || !orderForm.value.receiverPhone || !orderForm.value.receiverAddress) {
    ElMessage.warning('請填寫完整的地址信息')
    return
  }

  try {
    // 這裡應該調用API保存地址
    const newAddress: SavedAddress = {
      id: Date.now(),
      receiverName: orderForm.value.receiverName,
      receiverPhone: orderForm.value.receiverPhone,
      receiverAddress: orderForm.value.receiverAddress,
      isDefault: saveAsDefault.value
    }

    if (saveAsDefault.value) {
      savedAddresses.value.forEach(addr => addr.isDefault = false)
    }

    savedAddresses.value.push(newAddress)
    selectedAddressId.value = newAddress.id
    showNewAddressForm.value = false
    ElMessage.success('地址保存成功')
  } catch (error) {
    ElMessage.error('保存地址失敗')
  }
}

const setDefaultAddress = async (address: SavedAddress) => {
  try {
    savedAddresses.value.forEach(addr => addr.isDefault = false)
    address.isDefault = true
    ElMessage.success('默認地址設置成功')
  } catch (error) {
    ElMessage.error('設置失敗')
  }
}

const deleteAddress = async (address: SavedAddress) => {
  try {
    await ElMessageBox.confirm('確定要刪除這個地址嗎？', '確認刪除', {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const index = savedAddresses.value.findIndex(addr => addr.id === address.id)
    if (index > -1) {
      savedAddresses.value.splice(index, 1)
      if (selectedAddressId.value === address.id) {
        selectedAddressId.value = null
        if (savedAddresses.value.length > 0) {
          selectAddress(savedAddresses.value[0])
        }
      }
      ElMessage.success('地址刪除成功')
    }
  } catch {
    // 用戶取消操作
  }
}

const handleRegionChange = (value: string[]) => {
  // 處理地區選擇變化
  console.log('選擇的地區:', value)
}

// 配送和優惠券相關方法
const calculateShipping = () => {
  // 根據選擇的配送方式計算運費
  // 實際項目中可能需要根據地址和商品重量計算
}

const loadAvailableCoupons = async () => {
  try {
    // 這裡應該從API加載可用優惠券
    availableCoupons.value = [
      { id: 1, name: '新用戶專享', discount: 10, condition: '滿100元可用', minAmount: 100 },
      { id: 2, name: '滿減優惠', discount: 20, condition: '滿200元可用', minAmount: 200 }
    ]
  } catch (error) {
    console.warn('加載優惠券失敗:', error)
  }
}

const selectCoupon = (coupon: Coupon) => {
  if (itemTotal.value >= coupon.minAmount) {
    selectedCoupon.value = coupon
  } else {
    ElMessage.warning(`此優惠券需要滿${coupon.minAmount}元才能使用`)
  }
}

const confirmCoupon = () => {
  showCouponDialog.value = false
  if (selectedCoupon.value) {
    ElMessage.success(`已使用優惠券：${selectedCoupon.value.name}`)
  }
}

const removeCoupon = () => {
  selectedCoupon.value = null
  ElMessage.success('已移除優惠券')
}

const goBack = () => {
  router.push('/cart')
}

onMounted(() => {
  loadSelectedItems()
  loadSavedAddresses()
  loadAvailableCoupons()
})
</script>

<style scoped>
.checkout-view {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 0;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-title {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.checkout-content {
  margin-bottom: 80px;
}

.section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.form-row {
  display: flex;
  gap: 20px;
}

.form-group {
  flex: 1;
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #555;
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}

.product-info {
  flex: 1;
  margin-left: 15px;
}

.product-name {
  font-size: 16px;
  margin-bottom: 5px;
}

.product-price {
  color: #e74c3c;
  font-weight: bold;
}

.product-quantity {
  margin: 0 20px;
  color: #666;
}

.product-subtotal {
  font-weight: bold;
  color: #e74c3c;
}

.payment-methods {
  display: flex;
  gap: 20px;
}

.payment-method {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 2px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.2s;
}

.payment-method:hover {
  border-color: #007bff;
}

.payment-method input[type="radio"] {
  margin-right: 10px;
}

.payment-icon {
  margin-right: 5px;
  font-size: 18px;
}

.summary-details {
  max-width: 400px;
  margin-left: auto;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 5px 0;
}

.total-row {
  border-top: 2px solid #eee;
  padding-top: 15px;
  margin-top: 15px;
  font-size: 18px;
  font-weight: bold;
}

.total-amount {
  color: #e74c3c;
}

.checkout-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #eee;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1000;
}

.footer-summary {
  display: flex;
  align-items: center;
  gap: 20px;
}

.footer-actions {
  display: flex;
  gap: 15px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-lg {
  padding: 12px 24px;
  font-size: 16px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
