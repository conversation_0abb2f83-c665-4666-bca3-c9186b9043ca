package com.example.repository;

import com.example.entity.UserFollow;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserFollowRepository extends JpaRepository<UserFollow, Long> {
    
    /**
     * 查找關注關係
     */
    Optional<UserFollow> findByFollowerIdAndFollowingId(Long followerId, Long followingId);
    
    /**
     * 檢查是否已關注
     */
    boolean existsByFollowerIdAndFollowingId(Long followerId, Long followingId);
    
    /**
     * 查詢用戶關注的所有人（關注列表）
     */
    @Query("SELECT uf FROM UserFollow uf JOIN FETCH uf.following WHERE uf.followerId = :followerId ORDER BY uf.createdAt DESC")
    List<UserFollow> findFollowingByFollowerIdWithUser(@Param("followerId") Long followerId);
    
    /**
     * 查詢關注某用戶的所有人（粉絲列表）
     */
    @Query("SELECT uf FROM UserFollow uf JOIN FETCH uf.follower WHERE uf.followingId = :followingId ORDER BY uf.createdAt DESC")
    List<UserFollow> findFollowersByFollowingIdWithUser(@Param("followingId") Long followingId);
    
    /**
     * 統計用戶關注數量
     */
    long countByFollowerId(Long followerId);
    
    /**
     * 統計用戶粉絲數量
     */
    long countByFollowingId(Long followingId);
    
    /**
     * 查詢用戶關注的用戶ID列表
     */
    @Query("SELECT uf.followingId FROM UserFollow uf WHERE uf.followerId = :followerId")
    List<Long> findFollowingIdsByFollowerId(@Param("followerId") Long followerId);
    
    /**
     * 查詢用戶粉絲的用戶ID列表
     */
    @Query("SELECT uf.followerId FROM UserFollow uf WHERE uf.followingId = :followingId")
    List<Long> findFollowerIdsByFollowingId(@Param("followingId") Long followingId);
    
    /**
     * 查詢兩個用戶的共同關注
     */
    @Query("SELECT uf1.followingId FROM UserFollow uf1 " +
           "WHERE uf1.followerId = :userId1 " +
           "AND uf1.followingId IN (SELECT uf2.followingId FROM UserFollow uf2 WHERE uf2.followerId = :userId2)")
    List<Long> findMutualFollowingIds(@Param("userId1") Long userId1, @Param("userId2") Long userId2);
    
    /**
     * 刪除關注關係
     */
    void deleteByFollowerIdAndFollowingId(Long followerId, Long followingId);
    
    /**
     * 批量查詢關注關係
     */
    @Query("SELECT uf FROM UserFollow uf WHERE uf.followerId = :followerId AND uf.followingId IN :followingIds")
    List<UserFollow> findByFollowerIdAndFollowingIdIn(@Param("followerId") Long followerId, @Param("followingIds") List<Long> followingIds);
}
