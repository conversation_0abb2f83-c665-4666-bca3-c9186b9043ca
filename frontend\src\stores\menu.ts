import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import MenuService, { type MenuDto } from '@/api/menu'

/**
 * 菜單狀態管理
 */
export const useMenuStore = defineStore('menu', () => {
  // 狀態
  const menuTree = ref<MenuDto[]>([])
  const loading = ref(false)
  const error = ref('')
  const collapsed = ref(false)
  const activeMenu = ref('')
  const breadcrumb = ref<MenuDto[]>([])
  
  // 計算屬性
  const flatMenuList = computed(() => {
    return MenuService.flattenMenuTree(menuTree.value)
  })
  
  const hasMenuTree = computed(() => {
    return menuTree.value.length > 0
  })
  
  const isLoading = computed(() => {
    return loading.value
  })
  
  const hasError = computed(() => {
    return !!error.value
  })
  
  // 動作
  const loadMenuTree = async () => {
    loading.value = true
    error.value = ''
    
    try {
      const data = await MenuService.getUserMenuTree()
      menuTree.value = data
      
      console.log('菜單樹加載成功:', data)
      
    } catch (err: any) {
      error.value = err.message || '加載菜單失敗'
      console.error('加載菜單樹失敗:', err)
      
    } finally {
      loading.value = false
    }
  }
  
  const refreshMenuTree = async () => {
    await loadMenuTree()
  }
  
  const setActiveMenu = (path: string) => {
    activeMenu.value = path
    updateBreadcrumb(path)
  }
  
  const updateBreadcrumb = (path: string) => {
    breadcrumb.value = MenuService.getMenuBreadcrumb(menuTree.value, path)
  }
  
  const toggleCollapsed = () => {
    collapsed.value = !collapsed.value
  }
  
  const setCollapsed = (value: boolean) => {
    collapsed.value = value
  }
  
  const findMenuByPath = (path: string): MenuDto | null => {
    return MenuService.findMenuByPath(menuTree.value, path)
  }
  
  const findMenuById = (id: number): MenuDto | null => {
    const findInTree = (menus: MenuDto[]): MenuDto | null => {
      for (const menu of menus) {
        if (menu.id === id) {
          return menu
        }
        
        if (menu.children && menu.children.length > 0) {
          const found = findInTree(menu.children)
          if (found) {
            return found
          }
        }
      }
      return null
    }
    
    return findInTree(menuTree.value)
  }
  
  const getMenuPermissions = (): string[] => {
    const permissions: string[] = []
    
    const extractPermissions = (menus: MenuDto[]) => {
      for (const menu of menus) {
        if (menu.permission) {
          permissions.push(menu.permission)
        }
        
        if (menu.children && menu.children.length > 0) {
          extractPermissions(menu.children)
        }
      }
    }
    
    extractPermissions(menuTree.value)
    return [...new Set(permissions)] // 去重
  }
  
  const hasPermission = (permission: string): boolean => {
    const permissions = getMenuPermissions()
    return permissions.includes(permission)
  }
  
  const clearMenuData = () => {
    menuTree.value = []
    activeMenu.value = ''
    breadcrumb.value = []
    error.value = ''
  }
  
  const getMenuByLevel = (level: number): MenuDto[] => {
    if (level === 0) {
      return menuTree.value
    }
    
    const result: MenuDto[] = []
    
    const findByLevel = (menus: MenuDto[], currentLevel: number) => {
      if (currentLevel === level) {
        result.push(...menus)
        return
      }
      
      for (const menu of menus) {
        if (menu.children && menu.children.length > 0) {
          findByLevel(menu.children, currentLevel + 1)
        }
      }
    }
    
    findByLevel(menuTree.value, 0)
    return result
  }
  
  const getMenuStats = () => {
    let totalMenus = 0
    let enabledMenus = 0
    let disabledMenus = 0
    let maxDepth = 0
    
    const calculateStats = (menus: MenuDto[], depth: number = 0) => {
      maxDepth = Math.max(maxDepth, depth)
      
      for (const menu of menus) {
        totalMenus++
        
        if (menu.enabled) {
          enabledMenus++
        } else {
          disabledMenus++
        }
        
        if (menu.children && menu.children.length > 0) {
          calculateStats(menu.children, depth + 1)
        }
      }
    }
    
    calculateStats(menuTree.value)
    
    return {
      totalMenus,
      enabledMenus,
      disabledMenus,
      maxDepth
    }
  }
  
  return {
    // 狀態
    menuTree,
    loading,
    error,
    collapsed,
    activeMenu,
    breadcrumb,
    
    // 計算屬性
    flatMenuList,
    hasMenuTree,
    isLoading,
    hasError,
    
    // 動作
    loadMenuTree,
    refreshMenuTree,
    setActiveMenu,
    updateBreadcrumb,
    toggleCollapsed,
    setCollapsed,
    findMenuByPath,
    findMenuById,
    getMenuPermissions,
    hasPermission,
    clearMenuData,
    getMenuByLevel,
    getMenuStats
  }
})
