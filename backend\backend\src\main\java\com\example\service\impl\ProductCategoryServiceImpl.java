package com.example.service.impl;

import com.example.dto.ApiResponse;
import com.example.dto.PagedResponse;
import com.example.dto.ProductCategoryDTO;
import com.example.entity.ProductCategory;
import com.example.repository.ProductCategoryRepository;
import com.example.repository.ProductRepository;
import com.example.service.ProductCategoryService;
import com.example.util.ProductRedisUtil;
import com.example.util.ProductRedisKeyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 商品分類服務實現類
 * 實現基於Redis的緩存策略和延遲雙刪機制
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@Service
public class ProductCategoryServiceImpl implements ProductCategoryService {
    
    @Autowired
    private ProductCategoryRepository categoryRepository;
    
    @Autowired
    private ProductRepository productRepository;
    
    @Autowired
    private ProductRedisUtil redisUtil;
    
    @Autowired
    private ScheduledExecutorService threadPool;
    
    @Override
    public ApiResponse<List<ProductCategoryDTO>> getCategoryTree() {
        try {
            // 1. 先從Redis獲取緩存
            String cacheKey = ProductRedisKeyUtil.categoryTreeKey();
            Object cachedTree = redisUtil.safeGetObject(cacheKey);
            
            if (cachedTree != null) {
                log.debug("從緩存獲取分類樹");
                @SuppressWarnings("unchecked")
                List<ProductCategoryDTO> cachedDTOs = (List<ProductCategoryDTO>) cachedTree;
                return ApiResponse.success(cachedDTOs);
            }
            
            // 2. 緩存未命中，從數據庫查詢
            log.debug("緩存未命中，從數據庫查詢分類樹");
            List<ProductCategory> allCategories = categoryRepository.findByStatusOrderByLevelAndSort(
                    ProductCategory.Status.ENABLED);
            
            // 3. 構建樹形結構（使用DTO）
            List<ProductCategoryDTO> categoryTreeDTO = buildCategoryTreeDTO(allCategories);
            
            // 4. 保存到緩存（緩存DTO而不是實體）
            if (categoryTreeDTO.isEmpty()) {
                // 緩存空結果，防止緩存穿透
                redisUtil.cacheEmptyResult(cacheKey);
            } else {
                redisUtil.setObject(cacheKey, categoryTreeDTO, ProductRedisUtil.ExpireTime.CATEGORY_TREE);
            }
            
            // 5. 直接返回DTO
            return ApiResponse.success(categoryTreeDTO);
            
        } catch (Exception e) {
            log.error("獲取分類樹失敗", e);
            return ApiResponse.error("獲取分類樹失敗: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<List<ProductCategory>> getRootCategories() {
        try {
            List<ProductCategory> rootCategories = categoryRepository.findRootCategoriesByStatus(
                    ProductCategory.Status.ENABLED);
            return ApiResponse.success(rootCategories);
        } catch (Exception e) {
            log.error("獲取根分類失敗", e);
            return ApiResponse.error("獲取根分類失敗: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<List<ProductCategory>> getChildCategories(Long parentId) {
        try {
            // 使用緩存
            String cacheKey = ProductRedisKeyUtil.categoryChildrenKey(parentId);
            Object cachedChildren = redisUtil.safeGetObject(cacheKey);
            
            if (cachedChildren != null) {
                return ApiResponse.success((List<ProductCategory>) cachedChildren);
            }
            
            List<ProductCategory> children = categoryRepository.findByParentIdAndStatus(
                    parentId, ProductCategory.Status.ENABLED);
            
            // 緩存結果
            if (children.isEmpty()) {
                redisUtil.cacheEmptyResult(cacheKey);
            } else {
                redisUtil.setObject(cacheKey, children, ProductRedisUtil.ExpireTime.CATEGORY_TREE);
            }
            
            return ApiResponse.success(children);
        } catch (Exception e) {
            log.error("獲取子分類失敗", e);
            return ApiResponse.error("獲取子分類失敗: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public ApiResponse<ProductCategory> createCategory(ProductCategory category, Long createdBy) {
        try {
            // 1. 數據驗證
            if (categoryRepository.existsByNameAndParentId(category.getName(), category.getParentId())) {
                return ApiResponse.error("分類名稱已存在");
            }
            
            // 2. 第一次刪除緩存
            clearCategoryCache();
            
            // 3. 設置分類屬性
            category.setCreatedBy(createdBy);
            
            // 4. 處理父分類
            if (category.getParentId() != 0) {
                Optional<ProductCategory> parentOpt = categoryRepository.findById(category.getParentId());
                if (parentOpt.isEmpty()) {
                    return ApiResponse.error("父分類不存在");
                }
                
                ProductCategory parent = parentOpt.get();
                if (parent.getStatus() != ProductCategory.Status.ENABLED) {
                    return ApiResponse.error("父分類已禁用");
                }
                
                // 設置層級
                category.setLevel(parent.getLevel() + 1);
                
                // 更新父分類為非葉子節點
                if (parent.getIsLeaf() == ProductCategory.LeafFlag.IS_LEAF) {
                    parent.setIsLeaf(ProductCategory.LeafFlag.NOT_LEAF);
                    categoryRepository.save(parent);
                }
            }
            
            // 5. 設置排序號
            Integer maxSort = categoryRepository.findMaxSortOrderByParentId(category.getParentId());
            category.setSortOrder(maxSort + 1);
            
            // 6. 保存分類
            ProductCategory savedCategory = categoryRepository.save(category);
            
            // 7. 延遲雙刪
            scheduleDelayedCacheClear();
            
            return ApiResponse.success(savedCategory);
            
        } catch (Exception e) {
            log.error("創建分類失敗", e);
            return ApiResponse.error("創建分類失敗: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public ApiResponse<ProductCategory> updateCategory(Long id, ProductCategory category, Long updatedBy) {
        try {
            // 1. 檢查分類是否存在
            Optional<ProductCategory> existingOpt = categoryRepository.findById(id);
            if (existingOpt.isEmpty()) {
                return ApiResponse.error("分類不存在");
            }
            
            ProductCategory existing = existingOpt.get();
            
            // 2. 檢查名稱重複
            if (categoryRepository.existsByNameAndParentIdAndIdNot(
                    category.getName(), existing.getParentId(), id)) {
                return ApiResponse.error("分類名稱已存在");
            }
            
            // 3. 第一次刪除緩存
            clearCategoryCache();
            
            // 4. 更新屬性
            existing.setName(category.getName());
            existing.setDescription(category.getDescription());
            existing.setIconUrl(category.getIconUrl());
            existing.setSortOrder(category.getSortOrder());
            
            // 5. 保存更新
            ProductCategory updatedCategory = categoryRepository.save(existing);
            
            // 6. 延遲雙刪
            scheduleDelayedCacheClear();
            
            return ApiResponse.success(updatedCategory);
            
        } catch (Exception e) {
            log.error("更新分類失敗", e);
            return ApiResponse.error("更新分類失敗: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<ProductCategory> getCategoryById(Long id) {
        try {
            Optional<ProductCategory> categoryOpt = categoryRepository.findById(id);
            if (categoryOpt.isEmpty()) {
                return ApiResponse.error("分類不存在");
            }
            return ApiResponse.success(categoryOpt.get());
        } catch (Exception e) {
            log.error("獲取分類詳情失敗", e);
            return ApiResponse.error("獲取分類詳情失敗: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<String> getCategoryPath(Long categoryId) {
        try {
            // 使用緩存
            String cacheKey = ProductRedisKeyUtil.categoryPathKey(categoryId);
            Object cachedPath = redisUtil.safeGetObject(cacheKey);
            
            if (cachedPath != null) {
                return ApiResponse.success((String) cachedPath);
            }
            
            Optional<String> pathOpt = categoryRepository.findCategoryPath(categoryId);
            String path = pathOpt.orElse("");
            
            // 緩存結果
            redisUtil.setObject(cacheKey, path, ProductRedisUtil.ExpireTime.CATEGORY_TREE);
            
            return ApiResponse.success(path);
        } catch (Exception e) {
            log.error("獲取分類路徑失敗", e);
            return ApiResponse.error("獲取分類路徑失敗: " + e.getMessage());
        }
    }
    
    /**
     * 構建分類樹形結構（使用DTO）
     */
    private List<ProductCategoryDTO> buildCategoryTreeDTO(List<ProductCategory> allCategories) {
        // 轉換為DTO
        List<ProductCategoryDTO> allCategoryDTOs = allCategories.stream()
                .map(ProductCategoryDTO::fromEntity)
                .collect(Collectors.toList());
        
        // 按父ID分組
        Map<Long, List<ProductCategoryDTO>> categoryMap = allCategoryDTOs.stream()
                .collect(Collectors.groupingBy(ProductCategoryDTO::getParentId));
        
        // 獲取根分類
        List<ProductCategoryDTO> rootCategories = categoryMap.getOrDefault(0L, new ArrayList<>());
        
        // 遞歸構建樹
        for (ProductCategoryDTO root : rootCategories) {
            buildChildrenDTO(root, categoryMap);
        }
        
        return rootCategories;
    }
    
    /**
     * 遞歸構建子分類（DTO版本）
     */
    private void buildChildrenDTO(ProductCategoryDTO parent, Map<Long, List<ProductCategoryDTO>> categoryMap) {
        List<ProductCategoryDTO> children = categoryMap.getOrDefault(parent.getId(), new ArrayList<>());
        parent.setChildren(children);
        
        for (ProductCategoryDTO child : children) {
            buildChildrenDTO(child, categoryMap);
        }
    }
    
    
    /**
     * 清除分類相關緩存
     */
    private void clearCategoryCache() {
        try {
            redisUtil.deletePattern(ProductRedisKeyUtil.allCategoryPattern());
            log.debug("清除分類緩存");
        } catch (Exception e) {
            log.error("清除分類緩存失敗", e);
        }
    }
    
    /**
     * 延遲雙刪 - 基於學習的延遲雙刪策略
     */
    private void scheduleDelayedCacheClear() {
        threadPool.schedule(() -> {
            try {
                clearCategoryCache();
                log.debug("執行延遲雙刪");
            } catch (Exception e) {
                log.error("延遲雙刪失敗", e);
            }
        }, 10, TimeUnit.SECONDS);
    }
    
    // 其他方法實現...
    @Override
    public ApiResponse<String> deleteCategory(Long id, Long deletedBy) {
        // TODO: 實現刪除邏輯
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> batchDeleteCategories(List<Long> ids, Long deletedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> toggleCategoryStatus(Long id, Integer status, Long updatedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> updateCategorySort(Long id, Integer sortOrder, Long updatedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> moveCategory(Long categoryId, Long newParentId, Long updatedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<PagedResponse<ProductCategory>> getCategoriesWithFilters(String name, Long parentId, Integer status, Pageable pageable) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<List<ProductCategory>> getLeafCategories() {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<Long> getProductCountByCategory(Long categoryId) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<Boolean> checkCategoryNameExists(String name, Long parentId, Long excludeId) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<Object> getCategoryStatistics() {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> refreshCategoryCache() {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> rebuildCategoryTree() {
        return ApiResponse.error("功能開發中");
    }

    @Override
    public List<Long> getCategoryAndDescendantIds(Long categoryId) {
        List<Long> result = new ArrayList<>();
        result.add(categoryId);

        // 遞歸獲取所有子分類ID
        collectDescendantIds(categoryId, result);

        return result;
    }

    /**
     * 遞歸收集所有子分類ID
     */
    private void collectDescendantIds(Long parentId, List<Long> result) {
        List<ProductCategory> children = categoryRepository.findByParentIdAndStatus(
                parentId, ProductCategory.Status.ENABLED);

        for (ProductCategory child : children) {
            result.add(child.getId());
            // 遞歸收集子分類的子分類
            collectDescendantIds(child.getId(), result);
        }
    }
}
