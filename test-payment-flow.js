const BASE_URL = 'http://localhost:8080';

// 測試用戶登入並獲取token
async function login() {
  try {
    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'how',
        password: 'howhowhowtogo'
      })
    });
    
    const result = await response.json();
    console.log('登入結果:', result.success ? '成功' : '失敗');
    
    if (result.success && result.data && result.data.accessToken) {
      return result.data.accessToken;
    }
    
    throw new Error('登入失敗');
  } catch (error) {
    console.error('登入錯誤:', error);
    return null;
  }
}

// 測試獲取訂單詳情
async function getOrderDetail(token, orderId) {
  try {
    console.log(`\n=== 獲取訂單詳情 (ID: ${orderId}) ===`);
    
    const response = await fetch(`${BASE_URL}/api/orders/${orderId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const result = await response.json();
    
    if (result.success && result.data) {
      console.log('✅ 訂單詳情獲取成功');
      console.log('訂單號:', result.data.orderNumber);
      console.log('總金額:', result.data.totalAmount);
      console.log('狀態:', result.data.status === 0 ? '待付款' : '其他狀態');
      console.log('訂單項目數量:', result.data.orderItems ? result.data.orderItems.length : 0);
      console.log('支付記錄:', result.data.payment ? '存在' : '不存在');
      return result.data;
    } else {
      console.log('❌ 訂單詳情獲取失敗:', result.message);
      return null;
    }
  } catch (error) {
    console.error('❌ 請求錯誤:', error);
    return null;
  }
}

// 測試發起支付寶支付
async function testAlipayPayment(token, orderId) {
  try {
    console.log(`\n=== 測試發起支付寶支付 (訂單ID: ${orderId}) ===`);
    
    const response = await fetch(`${BASE_URL}/api/payment/alipay/create?orderId=${orderId}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('支付請求狀態碼:', response.status);
    
    const result = await response.json();
    
    if (result.success && result.data) {
      console.log('✅ 支付寶支付請求成功');
      console.log('支付表單長度:', result.data.length);
      console.log('支付表單預覽:', result.data.substring(0, 200) + '...');
      
      // 模擬前端處理支付表單
      console.log('\n📝 模擬前端處理支付表單:');
      console.log('1. 創建隱藏表單');
      console.log('2. 設置表單內容為支付寶返回的HTML');
      console.log('3. 提交表單到新窗口');
      console.log('4. 用戶在支付寶頁面完成支付');
      console.log('5. 支付完成後跳轉回系統');
      
      return true;
    } else {
      console.log('❌ 支付寶支付請求失敗:', result.message);
      return false;
    }
  } catch (error) {
    console.error('❌ 支付請求錯誤:', error);
    return false;
  }
}

// 測試查詢支付狀態
async function testPaymentStatus(token, orderId) {
  try {
    console.log(`\n=== 測試查詢支付狀態 (訂單ID: ${orderId}) ===`);
    
    const response = await fetch(`${BASE_URL}/api/payment/status/${orderId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const result = await response.json();
    
    if (result.success && result.data) {
      console.log('✅ 支付狀態查詢成功');
      console.log('支付方式:', result.data.paymentMethod === 1 ? '支付寶' : '其他');
      console.log('支付狀態:', result.data.paymentStatus === 0 ? '待支付' : result.data.paymentStatus === 1 ? '已支付' : '其他');
      console.log('支付金額:', result.data.paymentAmount);
      return result.data;
    } else {
      console.log('❌ 支付狀態查詢失敗:', result.message);
      return null;
    }
  } catch (error) {
    console.error('❌ 支付狀態查詢錯誤:', error);
    return null;
  }
}

// 主測試函數
async function main() {
  console.log('🚀 開始測試支付流程...\n');
  
  // 1. 登入獲取token
  const token = await login();
  if (!token) {
    console.log('❌ 無法獲取token，測試終止');
    return;
  }
  
  console.log('✅ 登入成功，token已獲取');
  
  // 2. 測試訂單ID為2的支付流程
  const orderId = 2;
  
  // 3. 獲取訂單詳情
  const order = await getOrderDetail(token, orderId);
  if (!order) {
    console.log('❌ 無法獲取訂單詳情，測試終止');
    return;
  }
  
  // 4. 檢查訂單狀態
  if (order.status !== 0) {
    console.log('⚠️ 訂單狀態不是待付款，無法測試支付');
    return;
  }
  
  // 5. 測試發起支付
  const paymentResult = await testAlipayPayment(token, orderId);
  if (!paymentResult) {
    console.log('❌ 支付發起失敗');
    return;
  }
  
  // 6. 測試查詢支付狀態
  await testPaymentStatus(token, orderId);
  
  console.log('\n🎉 支付流程測試完成！');
  console.log('\n📋 測試總結:');
  console.log('✅ 用戶登入 - 正常');
  console.log('✅ 訂單詳情獲取 - 正常');
  console.log('✅ 支付寶支付發起 - 正常');
  console.log('✅ 支付狀態查詢 - 正常');
  console.log('\n💡 前端支付按鈕應該能正常工作了！');
}

// 執行測試
main().catch(console.error);
