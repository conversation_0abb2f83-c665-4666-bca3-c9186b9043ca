# 郵件配置指南

## 配置文件位置
`backend/backend/src/main/resources/application.yml`

## 支持的郵件服務商

### 1. Gmail 配置
```yaml
spring:
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: your-app-password  # 需要使用應用專用密碼
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
```

**Gmail 設置步驟：**
1. 開啟兩步驟驗證
2. 生成應用專用密碼
3. 使用應用專用密碼而不是帳戶密碼

### 2. QQ郵箱配置
```yaml
spring:
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-qq-auth-code  # QQ郵箱授權碼
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
```

**QQ郵箱設置步驟：**
1. 登入 QQ 郵箱
2. 設置 → 帳戶 → POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服務
3. 開啟 SMTP 服務
4. 獲取授權碼

### 3. 163郵箱配置
```yaml
spring:
  mail:
    host: smtp.163.com
    port: 25
    username: <EMAIL>
    password: your-163-auth-code  # 163郵箱授權碼
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
```

### 4. Outlook/Hotmail 配置
```yaml
spring:
  mail:
    host: smtp-mail.outlook.com
    port: 587
    username: <EMAIL>
    password: your-outlook-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
```

## 測試郵件發送

### 1. 啟動後端服務
```bash
cd backend/backend
mvn spring-boot:run
```

### 2. 使用測試接口
```bash
# 測試發送郵件到 <EMAIL>
curl -X POST "http://localhost:8080/api/test/send-email?email=<EMAIL>"

# 測試系統狀態
curl -X GET "http://localhost:8080/api/test/status"
```

### 3. 通過前端測試
1. 訪問註冊頁面：http://localhost:5173/register
2. 輸入郵箱：<EMAIL>
3. 點擊發送驗證碼

## 常見問題

### 1. 郵件發送失敗
- 檢查郵箱服務商設置
- 確認用戶名密碼正確
- 檢查網絡連接
- 查看後端日誌

### 2. Gmail 認證失敗
- 確保開啟兩步驟驗證
- 使用應用專用密碼
- 檢查帳戶安全設置

### 3. QQ/163 郵箱認證失敗
- 確保開啟 SMTP 服務
- 使用授權碼而不是登入密碼
- 檢查服務商的安全設置

## 日誌查看

後端啟動後，郵件發送的詳細日誌會在控制台顯示：
```
INFO  - 開始測試發送郵件到: <EMAIL>
INFO  - 驗證碼郵件發送成功: <EMAIL>
```

## 頻率限制測試

系統實現了郵件發送頻率限制：
- 5分鐘內最多發送 2 次
- 一天內最多發送 5 次

可以通過多次調用測試接口來驗證頻率限制功能。
