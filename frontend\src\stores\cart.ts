import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { cartAPI, type Cart, type CartItem } from '../api/cart'
import { ElMessage } from 'element-plus'

export const useCartStore = defineStore('cart', () => {
  // 狀態
  const cart = ref<Cart | null>(null)
  const cartItems = ref<CartItem[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 計算屬性
  const cartCount = computed(() => {
    return cartItems.value.reduce((total, item) => total + item.quantity, 0)
  })

  const selectedItems = computed(() => {
    return cartItems.value.filter(item => item.selected)
  })

  const selectedCount = computed(() => {
    return selectedItems.value.reduce((total, item) => total + item.quantity, 0)
  })

  const totalAmount = computed(() => {
    return selectedItems.value.reduce((total, item) => {
      return total + (item.price * item.quantity)
    }, 0)
  })

  const hasItems = computed(() => cartItems.value.length > 0)

  // 獲取購物車
  const fetchCart = async () => {
    try {
      loading.value = true
      error.value = null
      
      const response = await cartAPI.getCart()
      if (response.success && response.data) {
        cart.value = response.data
        cartItems.value = response.data.cartItems || []
      } else {
        throw new Error(response.message || '獲取購物車失敗')
      }
    } catch (err: any) {
      error.value = err.message
      console.error('獲取購物車失敗:', err)
    } finally {
      loading.value = false
    }
  }

  // 添加商品到購物車
  const addToCart = async (productId: number, quantity: number = 1) => {
    try {
      const response = await cartAPI.addToCart(productId, quantity)
      if (response.success) {
        ElMessage.success('已加入購物車')
        await fetchCart() // 重新獲取購物車數據
        return true
      } else {
        ElMessage.error(response.message || '加入購物車失敗')
        return false
      }
    } catch (err: any) {
      ElMessage.error('加入購物車失敗')
      console.error('加入購物車失敗:', err)
      return false
    }
  }

  // 更新購物車項目數量
  const updateCartItem = async (cartItemId: number, quantity: number) => {
    try {
      const response = await cartAPI.updateCartItem(cartItemId, quantity)
      if (response.success) {
        await fetchCart() // 重新獲取購物車數據
        return true
      } else {
        ElMessage.error(response.message || '更新失敗')
        return false
      }
    } catch (err: any) {
      ElMessage.error('更新失敗')
      console.error('更新購物車項目失敗:', err)
      return false
    }
  }

  // 從購物車移除商品
  const removeFromCart = async (cartItemId: number) => {
    try {
      const response = await cartAPI.removeFromCart(cartItemId)
      if (response.success) {
        ElMessage.success('已移除商品')
        await fetchCart() // 重新獲取購物車數據
        return true
      } else {
        ElMessage.error(response.message || '移除失敗')
        return false
      }
    } catch (err: any) {
      ElMessage.error('移除失敗')
      console.error('移除購物車項目失敗:', err)
      return false
    }
  }

  // 清空購物車
  const clearCart = async () => {
    try {
      const response = await cartAPI.clearCart()
      if (response.success) {
        ElMessage.success('購物車已清空')
        cartItems.value = []
        cart.value = null
        return true
      } else {
        ElMessage.error(response.message || '清空失敗')
        return false
      }
    } catch (err: any) {
      ElMessage.error('清空失敗')
      console.error('清空購物車失敗:', err)
      return false
    }
  }

  // 切換商品選中狀態
  const toggleSelected = async (cartItemId: number) => {
    try {
      const response = await cartAPI.toggleSelected(cartItemId)
      if (response.success) {
        await fetchCart() // 重新獲取購物車數據
        return true
      } else {
        ElMessage.error(response.message || '操作失敗')
        return false
      }
    } catch (err: any) {
      ElMessage.error('操作失敗')
      console.error('切換選中狀態失敗:', err)
      return false
    }
  }

  // 獲取選中的購物車項目
  const getSelectedItems = async () => {
    try {
      const response = await cartAPI.getSelectedItems()
      if (response.success && response.data) {
        return response.data
      } else {
        throw new Error(response.message || '獲取選中項目失敗')
      }
    } catch (err: any) {
      console.error('獲取選中項目失敗:', err)
      return []
    }
  }

  // 重置狀態
  const resetCart = () => {
    cart.value = null
    cartItems.value = []
    loading.value = false
    error.value = null
  }

  return {
    // 狀態
    cart,
    cartItems,
    loading,
    error,
    
    // 計算屬性
    cartCount,
    selectedItems,
    selectedCount,
    totalAmount,
    hasItems,
    
    // 方法
    fetchCart,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    toggleSelected,
    getSelectedItems,
    resetCart
  }
})
