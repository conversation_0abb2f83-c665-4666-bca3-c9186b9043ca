<template>
  <div class="payment-view">
    <div class="container">
      <h1 class="page-title">訂單支付</h1>
      
      <div v-if="loading" class="loading">
        <el-skeleton :rows="5" animated />
      </div>
      
      <div v-else-if="order" class="payment-content">
        <!-- 支付進度 -->
        <div class="payment-progress">
          <el-steps :active="currentStep" finish-status="success" align-center>
            <el-step title="確認訂單" />
            <el-step title="選擇支付" />
            <el-step title="支付完成" />
          </el-steps>
        </div>

        <!-- 訂單信息 -->
        <div class="section order-section">
          <div class="section-header">
            <h2 class="section-title">
              <el-icon><DocumentCopy /></el-icon>
              訂單信息
            </h2>
            <el-button link @click="copyOrderNumber">
              <el-icon><CopyDocument /></el-icon>
              複製訂單號
            </el-button>
          </div>
          <div class="order-card">
            <div class="order-header">
              <div class="order-number">
                <span class="label">訂單號：</span>
                <span class="value">{{ order.orderNumber }}</span>
              </div>
              <div class="order-amount">
                <span class="amount-label">應付金額</span>
                <span class="amount-value">¥{{ order.totalAmount }}</span>
              </div>
            </div>

            <el-divider />

            <div class="order-details">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="創建時間">
                  {{ formatDate(order.createdAt) }}
                </el-descriptions-item>
                <el-descriptions-item label="訂單狀態">
                  <el-tag :type="getStatusType(order.status)">
                    {{ getStatusText(order.status) }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="收貨人">
                  {{ order.receiverName }} {{ order.receiverPhone }}
                </el-descriptions-item>
                <el-descriptions-item label="收貨地址" :span="2">
                  {{ order.receiverAddress }}
                </el-descriptions-item>
                <el-descriptions-item label="訂單備註" :span="2" v-if="order.remark">
                  {{ order.remark }}
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 商品列表 -->
            <div class="order-items" v-if="order.orderItems && order.orderItems.length > 0">
              <h4>商品清單</h4>
              <div class="item-list">
                <div v-for="item in order.orderItems" :key="item.id" class="order-item">
                  <div class="item-image">
                    <img :src="item.productImageUrl || '/default-product.jpg'" :alt="item.productName" />
                  </div>
                  <div class="item-info">
                    <div class="item-name">{{ item.productName }}</div>
                    <div class="item-price">¥{{ item.price }} × {{ item.quantity }}</div>
                  </div>
                  <div class="item-total">
                    ¥{{ (item.price * item.quantity).toFixed(2) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 支付狀態 -->
        <div class="section status-section">
          <h2 class="section-title">
            <el-icon><Clock /></el-icon>
            支付狀態
          </h2>
          <div class="payment-status-card" :class="statusClass">
            <div class="status-content">
              <div class="status-icon">
                <el-icon v-if="order.status === 0"><Clock /></el-icon>
                <el-icon v-else-if="order.status === 1"><SuccessFilled /></el-icon>
                <el-icon v-else-if="order.status === 2"><WarningFilled /></el-icon>
                <el-icon v-else><InfoFilled /></el-icon>
              </div>
              <div class="status-text">
                <h3>{{ statusTitle }}</h3>
                <p>{{ statusDescription }}</p>
                <div v-if="order.status === 0 && paymentTimeLeft > 0" class="countdown">
                  <el-icon><Timer /></el-icon>
                  剩餘支付時間：{{ formatTime(paymentTimeLeft) }}
                </div>
              </div>
            </div>
            <div v-if="order.status === 0" class="status-actions">
              <el-button link @click="refreshStatus" :loading="refreshing">
                <el-icon><Refresh /></el-icon>
                刷新狀態
              </el-button>
            </div>
          </div>
        </div>

        <!-- 支付方式 -->
        <div v-if="order.status === 0" class="section payment-section">
          <h2 class="section-title">
            <el-icon><CreditCard /></el-icon>
            選擇支付方式
          </h2>
          <div class="payment-methods">
            <div class="payment-method-card selected">
              <div class="method-content">
                <div class="method-icon">
                  <img src="/placeholder.svg" alt="支付寶" />
                </div>
                <div class="method-info">
                  <h3>支付寶</h3>
                  <p>安全快捷的在線支付</p>
                  <div class="method-features">
                    <span class="feature">
                      <el-icon><Lock /></el-icon>
                      安全保障
                    </span>
                    <span class="feature">
                      <el-icon><Lightning /></el-icon>
                      快速到賬
                    </span>
                  </div>
                </div>
              </div>
              <div class="method-status">
                <el-icon><Check /></el-icon>
              </div>
            </div>
          </div>

          <!-- 支付提示 -->
          <div class="payment-tips">
            <el-alert
              title="支付提示"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <ul>
                  <li>請在30分鐘內完成支付，超時訂單將自動取消</li>
                  <li>支付完成後，系統將自動確認訂單狀態</li>
                  <li>如遇支付問題，請聯繫客服協助處理</li>
                </ul>
              </template>
            </el-alert>
          </div>
        </div>
        
        <!-- 操作按鈕 -->
        <div class="section actions-section">
          <div class="payment-actions">
            <!-- 待支付狀態 -->
            <template v-if="order.status === 0">
              <el-button
                type="primary"
                size="large"
                @click="startPayment"
                :loading="paying"
                class="pay-btn"
              >
                <el-icon><CreditCard /></el-icon>
                {{ paying ? '正在跳轉支付寶...' : '立即支付' }}
              </el-button>

              <el-button
                size="large"
                @click="cancelOrder"
                :loading="cancelling"
                class="cancel-btn"
              >
                <el-icon><Close /></el-icon>
                取消訂單
              </el-button>
            </template>

            <!-- 其他狀態 -->
            <template v-else>
              <el-button
                type="primary"
                size="large"
                @click="goToOrders"
              >
                <el-icon><List /></el-icon>
                查看我的訂單
              </el-button>

              <el-button
                v-if="order.status === 1"
                size="large"
                @click="goToProducts"
              >
                <el-icon><ShoppingBag /></el-icon>
                繼續購物
              </el-button>
            </template>
          </div>

          <!-- 客服聯繫 -->
          <div class="customer-service">
            <el-button link @click="contactService">
              <el-icon><Service /></el-icon>
              遇到問題？聯繫客服
            </el-button>
          </div>
        </div>

        <!-- 支付成功動畫 -->
        <div v-if="showSuccessAnimation" class="success-animation">
          <div class="animation-content">
            <div class="success-icon">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <h3>支付成功！</h3>
            <p>您的訂單已確認，我們將盡快為您發貨</p>
          </div>
        </div>
        
        <!-- 支付說明 -->
        <div class="section notice-section">
          <h2 class="section-title">支付說明</h2>
          <div class="payment-notice">
            <ul>
              <li>請在10分鐘內完成支付，超時訂單將自動取消</li>
              <li>支付成功後，系統將自動確認訂單</li>
              <li>如遇支付問題，請聯繫客服</li>
              <li>本系統使用支付寶沙箱環境，僅供測試使用</li>
            </ul>
          </div>
        </div>
      </div>
      
      <div v-else class="error-state">
        <div class="error-icon">❌</div>
        <h2>訂單不存在</h2>
        <p>請檢查訂單號是否正確</p>
        <button class="btn btn-primary" @click="goToOrders">查看我的訂單</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DocumentCopy, CopyDocument, Clock, SuccessFilled, WarningFilled,
  InfoFilled, Timer, Refresh, CreditCard, Lock, Lightning, Check,
  Close, List, ShoppingBag, Service
} from '@element-plus/icons-vue'

interface Order {
  id: number
  orderNumber: string
  totalAmount: number
  status: number
  receiverName: string
  receiverPhone: string
  receiverAddress: string
  remark?: string
  createdAt: string
  paidAt?: string
  orderItems?: OrderItem[]
}

interface OrderItem {
  id: number
  productName: string
  productImageUrl: string
  price: number
  quantity: number
}

const route = useRoute()
const router = useRouter()

const order = ref<Order | null>(null)
const loading = ref(true)
const paying = ref(false)
const cancelling = ref(false)
const refreshing = ref(false)
const showSuccessAnimation = ref(false)
const statusCheckInterval = ref<number | null>(null)
const paymentTimeLeft = ref(1800) // 30分鐘倒計時
const countdownInterval = ref<number | null>(null)

// 計算屬性
const currentStep = computed(() => {
  if (!order.value) return 0

  switch (order.value.status) {
    case 0: return 1 // 選擇支付
    case 1: return 2 // 支付完成
    default: return 0
  }
})

const statusClass = computed(() => {
  if (!order.value) return ''

  switch (order.value.status) {
    case 0: return 'pending'
    case 1: return 'paid'
    case 2: return 'shipped'
    case 3: return 'completed'
    case -1: return 'cancelled'
    default: return ''
  }
})

const statusIcon = computed(() => {
  if (!order.value) return ''
  
  switch (order.value.status) {
    case 0: return '⏳'
    case 1: return '✅'
    case 2: return '🚚'
    case 3: return '🎉'
    case -1: return '❌'
    default: return '❓'
  }
})

const statusTitle = computed(() => {
  if (!order.value) return ''
  
  switch (order.value.status) {
    case 0: return '待付款'
    case 1: return '已付款'
    case 2: return '已發貨'
    case 3: return '已完成'
    case -1: return '已取消'
    default: return '未知狀態'
  }
})

const statusDescription = computed(() => {
  if (!order.value) return ''
  
  switch (order.value.status) {
    case 0: return '請盡快完成支付，超時訂單將自動取消'
    case 1: return '支付成功，商家將盡快為您發貨'
    case 2: return '商品已發貨，請注意查收'
    case 3: return '交易已完成，感謝您的購買'
    case -1: return '訂單已取消'
    default: return ''
  }
})

// 方法
const loadOrder = async () => {
  try {
    loading.value = true
    const orderId = route.params.orderId
    
    const response = await fetch(`/api/orders/${orderId}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data) {
        order.value = result.data
        
        // 如果是待付款狀態，開始定時檢查支付狀態
        if (order.value.status === 0) {
          startStatusCheck()
        }
      }
    } else {
      ElMessage.error('加載訂單失敗')
    }
  } catch (error) {
    console.error('加載訂單失敗:', error)
    ElMessage.error('加載訂單失敗')
  } finally {
    loading.value = false
  }
}

const startPayment = async () => {
  if (!order.value) return
  
  try {
    paying.value = true
    
    const response = await fetch(`/api/payment/alipay/create?orderId=${order.value.id}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    const result = await response.json()
    
    if (result.success && result.data) {
      // 直接將支付寶返回的 HTML 表單寫入新窗口並自動提交
      const newWindow = window.open('', '_blank')
      if (newWindow) {
        newWindow.document.write(result.data)
        newWindow.document.close()
      } else {
        // 如果彈窗被阻止，則在當前頁面創建表單
        const tempDiv = document.createElement('div')
        tempDiv.innerHTML = result.data
        const form = tempDiv.querySelector('form')
        if (form) {
          form.target = '_blank'
          document.body.appendChild(form)
          form.submit()
          document.body.removeChild(form)
        }
      }
      
      ElMessage.success('正在跳轉到支付寶...')
      
      // 開始檢查支付狀態
      startStatusCheck()
    } else {
      ElMessage.error(result.message || '發起支付失敗')
    }
  } catch (error) {
    console.error('發起支付失敗:', error)
    ElMessage.error('發起支付失敗')
  } finally {
    paying.value = false
  }
}





const startStatusCheck = () => {
  if (statusCheckInterval.value) {
    clearInterval(statusCheckInterval.value)
  }
  
  statusCheckInterval.value = window.setInterval(async () => {
    if (order.value && order.value.status === 0) {
      await loadOrder()
    } else {
      stopStatusCheck()
    }
  }, 3000) // 每3秒檢查一次
}

const stopStatusCheck = () => {
  if (statusCheckInterval.value) {
    clearInterval(statusCheckInterval.value)
    statusCheckInterval.value = null
  }
}

// 新增方法
const copyOrderNumber = async () => {
  if (!order.value) return

  try {
    await navigator.clipboard.writeText(order.value.orderNumber)
    ElMessage.success('訂單號已複製到剪貼板')
  } catch (error) {
    ElMessage.error('複製失敗，請手動複製')
  }
}

const getStatusType = (status: number) => {
  switch (status) {
    case 0: return 'warning'
    case 1: return 'success'
    case 2: return 'info'
    case 3: return 'success'
    case -1: return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status: number) => {
  switch (status) {
    case 0: return '待付款'
    case 1: return '已付款'
    case 2: return '已發貨'
    case 3: return '已完成'
    case -1: return '已取消'
    default: return '未知'
  }
}

const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

const startCountdown = () => {
  if (countdownInterval.value) {
    clearInterval(countdownInterval.value)
  }

  countdownInterval.value = window.setInterval(() => {
    if (paymentTimeLeft.value > 0) {
      paymentTimeLeft.value--
    } else {
      clearInterval(countdownInterval.value!)
      ElMessage.warning('支付超時，訂單已自動取消')
      loadOrder()
    }
  }, 1000)
}

const contactService = () => {
  ElMessage.info('客服功能開發中，請稍後再試')
}

const goToOrders = () => {
  router.push('/app/orders')
}

const goToProducts = () => {
  router.push('/app/products')
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 重寫取消訂單方法
const cancelOrder = async () => {
  if (!order.value) return

  try {
    cancelling.value = true

    await ElMessageBox.confirm('確定要取消這個訂單嗎？', '確認取消', {
      confirmButtonText: '確定取消',
      cancelButtonText: '繼續支付',
      type: 'warning'
    })

    const response = await fetch(`/api/orders/${order.value.id}/cancel`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    if (response.ok) {
      ElMessage.success('訂單已取消')
      await loadOrder()
      stopCountdown()
    } else {
      ElMessage.error('取消訂單失敗')
    }
  } catch (error) {
    // 用戶取消操作
  } finally {
    cancelling.value = false
  }
}

// 重寫刷新狀態方法
const refreshStatus = async () => {
  refreshing.value = true
  try {
    await loadOrder()
    ElMessage.success('狀態已刷新')
  } finally {
    refreshing.value = false
  }
}

const stopCountdown = () => {
  if (countdownInterval.value) {
    clearInterval(countdownInterval.value)
    countdownInterval.value = null
  }
}

onMounted(() => {
  loadOrder()
  if (order.value?.status === 0) {
    startCountdown()
  }
})

onUnmounted(() => {
  stopStatusCheck()
  stopCountdown()
})
</script>

<style scoped>
.payment-view {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-title {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.loading {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
  padding: 8px 0;
}

.label {
  width: 100px;
  color: #666;
  font-weight: bold;
}

.value {
  flex: 1;
  color: #333;
}

.amount {
  color: #e74c3c;
  font-size: 18px;
  font-weight: bold;
}

.payment-status {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  border: 2px solid;
}

.payment-status.pending {
  border-color: #ffc107;
  background-color: #fff8e1;
}

.payment-status.paid {
  border-color: #28a745;
  background-color: #e8f5e8;
}

.payment-status.cancelled {
  border-color: #dc3545;
  background-color: #ffeaea;
}

.status-icon {
  font-size: 48px;
  margin-right: 20px;
}

.status-text h3 {
  margin-bottom: 5px;
  font-size: 20px;
}

.payment-method {
  display: flex;
  align-items: center;
  padding: 20px;
  border: 2px solid #007bff;
  border-radius: 8px;
  background-color: #f0f9ff;
}

.method-icon {
  font-size: 32px;
  margin-right: 15px;
}

.method-info h3 {
  margin-bottom: 5px;
  color: #007bff;
}

.payment-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.payment-notice ul {
  list-style: none;
  padding: 0;
}

.payment-notice li {
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.payment-notice li:before {
  content: "• ";
  color: #007bff;
  font-weight: bold;
  margin-right: 8px;
}

.error-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
}

.error-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  font-size: 14px;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-outline {
  background-color: transparent;
  color: #007bff;
  border: 1px solid #007bff;
}

.btn-lg {
  padding: 12px 24px;
  font-size: 16px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
