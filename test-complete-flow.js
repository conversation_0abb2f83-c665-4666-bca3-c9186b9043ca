/**
 * 完整流程測試腳本
 * 測試購物車圖示顯示和完整購物流程
 */

const axios = require('axios');

// 測試配置
const BASE_URL = 'http://localhost:8080';
const FRONTEND_URL = 'http://localhost:5173';
const TEST_USER = {
  username: 'how',
  password: '12345'
};

let authToken = '';

// 測試結果統計
const testResults = {
  passed: 0,
  failed: 0,
  total: 0
};

// 測試工具函數
function logTest(testName, passed, message = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${testName}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}: ${message}`);
  }
}

// 1. 登錄測試
async function testLogin() {
  try {
    console.log('\n🔐 測試用戶登錄...');
    const response = await axios.post(`${BASE_URL}/api/auth/login`, {
      username: TEST_USER.username,
      password: TEST_USER.password
    });

    if (response.data.success) {
      authToken = response.data.data.token;
      logTest('用戶登錄', true);
      return true;
    } else {
      logTest('用戶登錄', false, response.data.message);
      return false;
    }
  } catch (error) {
    logTest('用戶登錄', false, error.message);
    return false;
  }
}

// 2. 購物車API測試
async function testCartAPI() {
  console.log('\n🛒 測試購物車API...');
  
  try {
    // 獲取購物車
    const cartResponse = await axios.get(`${BASE_URL}/api/cart`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    logTest('獲取購物車API', cartResponse.data.success);
    
    if (cartResponse.data.success) {
      const initialCount = cartResponse.data.data.cartItems ? 
        cartResponse.data.data.cartItems.reduce((total, item) => total + item.quantity, 0) : 0;
      
      console.log(`   初始購物車數量: ${initialCount}`);
      
      // 添加商品到購物車
      try {
        const addResponse = await axios.post(`${BASE_URL}/api/cart/add`, null, {
          headers: { 'Authorization': `Bearer ${authToken}` },
          params: { productId: 1, quantity: 2 }
        });
        
        logTest('添加商品到購物車API', addResponse.data.success);
        
        if (addResponse.data.success) {
          // 再次獲取購物車驗證數量更新
          const updatedCartResponse = await axios.get(`${BASE_URL}/api/cart`, {
            headers: { 'Authorization': `Bearer ${authToken}` }
          });
          
          if (updatedCartResponse.data.success) {
            const newCount = updatedCartResponse.data.data.cartItems ? 
              updatedCartResponse.data.data.cartItems.reduce((total, item) => total + item.quantity, 0) : 0;
            
            console.log(`   更新後購物車數量: ${newCount}`);
            logTest('購物車數量更新', newCount > initialCount);
          }
        }
      } catch (error) {
        logTest('添加商品到購物車API', false, error.message);
      }
    }
  } catch (error) {
    logTest('獲取購物車API', false, error.message);
  }
}

// 3. 商品API測試
async function testProductAPI() {
  console.log('\n📦 測試商品API...');
  
  try {
    const response = await axios.get(`${BASE_URL}/api/products?page=0&size=5`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    logTest('獲取商品列表API', response.data.success);
    
    if (response.data.success && response.data.data.content) {
      console.log(`   商品數量: ${response.data.data.content.length}`);
      
      // 測試商品詳情API
      if (response.data.data.content.length > 0) {
        const productId = response.data.data.content[0].id;
        try {
          const detailResponse = await axios.get(`${BASE_URL}/api/products/${productId}`, {
            headers: { 'Authorization': `Bearer ${authToken}` }
          });
          logTest('獲取商品詳情API', detailResponse.data.success);
        } catch (error) {
          logTest('獲取商品詳情API', false, error.message);
        }
      }
    }
  } catch (error) {
    logTest('獲取商品列表API', false, error.message);
  }
}

// 4. 菜單API測試
async function testMenuAPI() {
  console.log('\n📋 測試菜單API...');

  try {
    const response = await axios.get(`${BASE_URL}/api/menu/tree`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    logTest('獲取菜單樹API', response.data.success);

    if (response.data.success) {
      console.log(`   菜單項數量: ${response.data.data.length}`);
    }
  } catch (error) {
    logTest('獲取菜單樹API', false, error.message);
  }
}

// 5. 支付流程測試
async function testPaymentFlow() {
  console.log('\n💳 測試支付流程...');

  try {
    // 創建測試訂單
    console.log('   創建測試訂單...');
    const orderResponse = await axios.post(`${BASE_URL}/api/orders/create`, {
      receiverName: '測試用戶',
      receiverPhone: '13800138000',
      receiverAddress: '測試地址',
      cartItemIds: [] // 空購物車項目，僅用於測試
    }, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    if (orderResponse.data.success) {
      const orderId = orderResponse.data.data.id;
      console.log(`   訂單創建成功，ID: ${orderId}`);
      logTest('創建訂單API', true);

      // 測試支付寶支付創建
      try {
        const paymentResponse = await axios.post(`${BASE_URL}/api/payment/alipay/create?orderId=${orderId}`, {}, {
          headers: { 'Authorization': `Bearer ${authToken}` }
        });

        logTest('創建支付寶支付API', paymentResponse.data.success);

        if (paymentResponse.data.success) {
          console.log('   支付寶支付表單創建成功');
        }
      } catch (error) {
        logTest('創建支付寶支付API', false, error.message);
      }

      // 測試訂單查詢
      try {
        const orderDetailResponse = await axios.get(`${BASE_URL}/api/orders/${orderId}`, {
          headers: { 'Authorization': `Bearer ${authToken}` }
        });

        logTest('查詢訂單詳情API', orderDetailResponse.data.success);
      } catch (error) {
        logTest('查詢訂單詳情API', false, error.message);
      }

    } else {
      logTest('創建訂單API', false, orderResponse.data.message);
    }
  } catch (error) {
    logTest('創建訂單API', false, error.message);
  }
}

// 6. 前端文件檢查
async function testFrontendFiles() {
  console.log('\n📁 檢查前端關鍵文件...');
  
  const fs = require('fs');
  const path = require('path');
  
  const criticalFiles = [
    'frontend/src/stores/cart.ts',
    'frontend/src/components/layout/MainLayout.vue',
    'frontend/src/components/QuickActions.vue',
    'frontend/src/views/CartView.vue',
    'frontend/src/views/ProductsView.vue',
    'frontend/src/views/ProductDetailView.vue'
  ];
  
  criticalFiles.forEach(filePath => {
    const exists = fs.existsSync(filePath);
    logTest(`文件存在: ${path.basename(filePath)}`, exists);
  });
}

// 7. 生成測試報告
function generateTestReport() {
  console.log('\n📊 測試結果總結');
  console.log('='.repeat(50));
  console.log(`總測試數: ${testResults.total}`);
  console.log(`通過: ${testResults.passed} ✅`);
  console.log(`失敗: ${testResults.failed} ❌`);
  console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.failed === 0) {
    console.log('\n🎉 所有測試通過！系統準備就緒');
    console.log('\n📝 前端測試步驟:');
    console.log(`1. 確保前端服務運行: ${FRONTEND_URL}`);
    console.log('2. 登錄系統 (用戶名: how, 密碼: 12345)');
    console.log('3. 檢查右下角購物車圖示是否顯示');
    console.log('4. 測試購物車功能和數量更新');
    console.log('5. 驗證樣式效果和動畫');
    console.log('6. 測試完整支付流程:');
    console.log('   - 添加商品到購物車');
    console.log('   - 進入購物車頁面結算');
    console.log('   - 填寫收貨信息創建訂單');
    console.log('   - 驗證跳轉到支付頁面 (不是home頁面)');
    console.log('   - 測試支付頁面的各個按鈕跳轉');
    console.log('7. 測試訂單列表的支付功能');
  } else {
    console.log('\n⚠️  部分測試失敗，請檢查相關功能');
  }
}

// 主測試函數
async function runCompleteTest() {
  console.log('🚀 開始完整流程測試');
  console.log('='.repeat(50));
  
  // 執行所有測試
  const loginSuccess = await testLogin();
  
  if (loginSuccess) {
    await testCartAPI();
    await testProductAPI();
    await testMenuAPI();
    await testPaymentFlow();
  }
  
  testFrontendFiles();
  
  // 生成報告
  generateTestReport();
}

// 運行測試
runCompleteTest().catch(error => {
  console.error('❌ 測試運行出錯:', error);
  process.exit(1);
});
