<template>
  <el-button
    :type="isFavorited ? 'danger' : 'primary'"
    :icon="isFavorited ? 'StarFilled' : 'Star'"
    :loading="loading"
    :disabled="disabled"
    @click="handleToggleFavorite"
    :size="size"
    :plain="!isFavorited"
    class="favorite-button"
  >
    <template v-if="showText">
      {{ isFavorited ? '取消收藏' : '收藏' }}
    </template>
    <span v-if="showCount && favoriteCount > 0" class="favorite-count">
      {{ formatCount(favoriteCount) }}
    </span>
  </el-button>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useFavoriteStore } from '@/stores/favorite'

interface Props {
  itemId: number
  showText?: boolean
  showCount?: boolean
  size?: 'large' | 'default' | 'small'
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showText: true,
  showCount: true,
  size: 'default',
  disabled: false
})

const emit = defineEmits<{
  favoriteChanged: [itemId: number, isFavorited: boolean]
}>()

const favoriteStore = useFavoriteStore()
const loading = ref(false)

// 计算属性
const isFavorited = computed(() => {
  return favoriteStore.favoriteStatus.get(props.itemId) || false
})

const favoriteCount = computed(() => {
  const stats = favoriteStore.favoriteStats.get(props.itemId)
  return stats?.favoriteCount || 0
})

// 格式化数字显示
const formatCount = (count: number): string => {
  if (count >= 1000000) {
    return (count / 1000000).toFixed(1) + 'M'
  } else if (count >= 1000) {
    return (count / 1000).toFixed(1) + 'K'
  }
  return count.toString()
}

// 切换收藏状态
const handleToggleFavorite = async () => {
  if (loading.value || props.disabled) return

  loading.value = true
  
  try {
    let success = false
    
    if (isFavorited.value) {
      success = await favoriteStore.removeFavorite(props.itemId)
      if (success) {
        ElMessage.success('取消收藏成功')
        emit('favoriteChanged', props.itemId, false)
      } else {
        ElMessage.error(favoriteStore.error || '取消收藏失败')
      }
    } else {
      success = await favoriteStore.addFavorite(props.itemId)
      if (success) {
        ElMessage.success('收藏成功')
        emit('favoriteChanged', props.itemId, true)
      } else {
        ElMessage.error(favoriteStore.error || '收藏失败')
      }
    }
  } catch (error) {
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 初始化时加载收藏状态和统计信息
onMounted(async () => {
  await Promise.all([
    favoriteStore.checkFavoriteStatus(props.itemId),
    favoriteStore.loadFavoriteStats(props.itemId)
  ])
})
</script>

<style scoped>
.favorite-button {
  transition: all 0.2s ease;
  position: relative;
}

.favorite-button:hover {
  transform: translateY(-1px);
}

.favorite-count {
  margin-left: 4px;
  font-size: 12px;
  opacity: 0.8;
}

.favorite-button.el-button--primary.is-plain {
  color: #409eff;
  border-color: #409eff;
}

.favorite-button.el-button--primary.is-plain:hover {
  background-color: #409eff;
  color: white;
}

.favorite-button.el-button--danger {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  border-color: #ff6b6b;
}

.favorite-button.el-button--danger:hover {
  background: linear-gradient(135deg, #ff5252, #e53935);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}
</style>