<template>
  <el-button
    :type="isFollowing ? 'default' : 'primary'"
    :loading="loading"
    @click="handleFollowClick"
    size="small"
  >
    {{ isFollowing ? '已關注' : '關注' }}
  </el-button>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { followAPI } from '../api'
import { ElMessage } from 'element-plus'

interface Props {
  userId: number
  initialFollowStatus?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  initialFollowStatus: false
})

const emit = defineEmits<{
  followChanged: [isFollowing: boolean]
}>()

const isFollowing = ref(props.initialFollowStatus)
const loading = ref(false)

// 檢查關注狀態
const checkFollowStatus = async () => {
  try {
    const response = await followAPI.getFollowStatus(props.userId)
    if (response.success) {
      isFollowing.value = response.data.isFollowing
    }
  } catch (error) {
    console.error('檢查關注狀態失敗:', error)
  }
}

// 處理關注/取消關注
const handleFollowClick = async () => {
  loading.value = true
  
  try {
    let response
    if (isFollowing.value) {
      response = await followAPI.unfollowUser(props.userId)
    } else {
      response = await followAPI.followUser(props.userId)
    }
    
    if (response.success) {
      isFollowing.value = !isFollowing.value
      emit('followChanged', isFollowing.value)
      ElMessage.success(response.message || (isFollowing.value ? '關注成功' : '取消關注成功'))
    } else {
      ElMessage.error(response.message || '操作失敗')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '操作失敗')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  if (!props.initialFollowStatus) {
    checkFollowStatus()
  }
})
</script>
