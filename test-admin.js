const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  try {
    console.log('🚀 開始測試管理員後台功能...');
    
    // 1. 訪問管理員後台
    console.log('📍 訪問管理員後台...');
    await page.goto('http://localhost:5173/admin-dashboard');
    await page.waitForTimeout(2000);
    
    // 2. 檢查是否已登入
    const isLoggedIn = await page.locator('text=歡迎，系統管理員').isVisible();
    console.log('✅ 管理員登入狀態:', isLoggedIn);
    
    // 3. 檢查統計數據
    const pendingCount = await page.locator('text=待審核').locator('..').locator('h3').textContent();
    console.log('📊 待審核數量:', pendingCount);
    
    // 4. 點擊身份認證審核菜單
    console.log('🔍 點擊身份認證審核菜單...');
    await page.click('text=身份認證審核');
    await page.waitForTimeout(2000);
    
    // 5. 檢查待審核列表
    const tableRows = await page.locator('table tbody tr').count();
    console.log('📋 待審核列表行數:', tableRows);
    
    // 6. 檢查是否有數據
    const hasData = await page.locator('text=No Data').isVisible();
    console.log('📝 列表是否有數據:', !hasData);
    
    if (!hasData && tableRows > 0) {
      // 7. 獲取第一行數據
      const firstRowData = await page.locator('table tbody tr').first().allTextContents();
      console.log('📄 第一行數據:', firstRowData);
      
      // 8. 測試查看詳情功能
      console.log('👁️ 測試查看詳情功能...');
      await page.click('table tbody tr:first-child button:has-text("查看詳情")');
      await page.waitForTimeout(1000);
      
      const dialogVisible = await page.locator('.el-dialog').isVisible();
      console.log('💬 詳情對話框是否顯示:', dialogVisible);
      
      if (dialogVisible) {
        // 關閉對話框
        await page.click('.el-dialog .el-dialog__close');
        await page.waitForTimeout(500);
      }
    }
    
    console.log('✅ 測試完成！');
    
  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  } finally {
    await browser.close();
  }
})();
