package com.example.controller;

import com.example.dto.ApiResponse;
import com.example.dto.PagedResponse;
import com.example.entity.Product;
import com.example.service.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品控制器
 * 提供商品的CRUD操作、搜索、統計等功能
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("/api/products")
@Tag(name = "商品管理", description = "商品的增刪改查、搜索、統計等功能")
public class ProductController {
    
    @Autowired
    private ProductService productService;

    @Autowired
    private com.example.util.ProductRedisUtil productRedisUtil;
    
    /**
     * 分頁查詢商品（公開接口，帶緩存）
     */
    @GetMapping
    @Operation(summary = "分頁查詢商品", description = "分頁查詢所有上架商品，帶Redis緩存優化")
    public ApiResponse<PagedResponse<Product>> getProducts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.info("分頁查詢商品: page={}, size={}", page, size);
        Pageable pageable = PageRequest.of(page, size);
        return productService.getProducts(pageable);
    }
    
    /**
     * 根據分類查詢商品（公開接口，帶緩存）
     */
    @GetMapping("/category/{categoryId}")
    @Operation(summary = "按分類查詢商品", description = "根據分類ID查詢商品，帶Redis緩存優化")
    @Parameter(name = "categoryId", description = "分類ID", required = true)
    public ApiResponse<PagedResponse<Product>> getProductsByCategory(
            @PathVariable Long categoryId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.info("按分類查詢商品: categoryId={}, page={}, size={}", categoryId, page, size);
        Pageable pageable = PageRequest.of(page, size);
        return productService.getProductsByCategory(categoryId, pageable);
    }
    
    /**
     * 獲取商品詳情（公開接口，帶緩存）
     */
    @GetMapping("/{id}")
    @Operation(summary = "獲取商品詳情", description = "根據ID獲取商品詳細信息，帶Redis緩存優化")
    @Parameter(name = "id", description = "商品ID", required = true)
    public ApiResponse<Product> getProductById(@PathVariable Long id) {
        log.info("獲取商品詳情: id={}", id);
        
        // 增加瀏覽次數
        productService.increaseViewCount(id);
        
        return productService.getProductById(id);
    }
    
    /**
     * 關鍵詞搜索商品（公開接口，帶緩存）
     */
    @GetMapping("/search")
    @Operation(summary = "搜索商品", description = "根據關鍵詞搜索商品，支持名稱、描述、品牌搜索")
    public ApiResponse<PagedResponse<Product>> searchProducts(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.info("搜索商品: keyword={}, page={}, size={}", keyword, page, size);
        Pageable pageable = PageRequest.of(page, size);
        return productService.searchProductsByKeyword(keyword, pageable);
    }
    
    /**
     * 多條件查詢商品
     */
    @GetMapping("/filter")
    @Operation(summary = "多條件查詢商品", description = "支持多種條件組合查詢商品，包含排序功能")
    public ApiResponse<PagedResponse<Product>> searchProductsWithFilters(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) BigDecimal minPrice,
            @RequestParam(required = false) BigDecimal maxPrice,
            @RequestParam(required = false) String brand,
            @RequestParam(required = false) Integer isRecommended,
            @RequestParam(required = false) Integer isHot,
            @RequestParam(required = false) String sortBy,
            @RequestParam(required = false) String sortDirection,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        log.info("多條件查詢商品: name={}, categoryId={}, status={}, minPrice={}, maxPrice={}, brand={}, isRecommended={}, isHot={}, sortBy={}, sortDirection={}, page={}, size={}",
                name, categoryId, status, minPrice, maxPrice, brand, isRecommended, isHot, sortBy, sortDirection, page, size);

        // 創建排序對象
        Pageable pageable;
        if (sortBy != null && sortDirection != null) {
            Sort.Direction direction = "desc".equalsIgnoreCase(sortDirection) ? Sort.Direction.DESC : Sort.Direction.ASC;
            String sortField = getSortField(sortBy);
            Sort sort = Sort.by(direction, sortField);
            pageable = PageRequest.of(page, size, sort);
        } else {
            // 默認排序：按sortOrder升序，然後按創建時間降序
            Sort sort = Sort.by(Sort.Direction.ASC, "sortOrder").and(Sort.by(Sort.Direction.DESC, "createdAt"));
            pageable = PageRequest.of(page, size, sort);
        }

        return productService.searchProducts(name, categoryId, status, minPrice, maxPrice, brand, isRecommended, isHot, pageable);
    }
    
    /**
     * 獲取推薦商品（公開接口，帶緩存）
     */
    @GetMapping("/recommended")
    @Operation(summary = "獲取推薦商品", description = "獲取推薦商品列表，帶Redis緩存優化")
    public ApiResponse<PagedResponse<Product>> getRecommendedProducts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.info("獲取推薦商品: page={}, size={}", page, size);
        Pageable pageable = PageRequest.of(page, size);
        return productService.getRecommendedProducts(pageable);
    }
    
    /**
     * 獲取熱門商品（公開接口，帶緩存）
     */
    @GetMapping("/hot")
    @Operation(summary = "獲取熱門商品", description = "獲取熱門商品列表，帶Redis緩存優化")
    public ApiResponse<PagedResponse<Product>> getHotProducts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.info("獲取熱門商品: page={}, size={}", page, size);
        Pageable pageable = PageRequest.of(page, size);
        return productService.getHotProducts(pageable);
    }
    
    /**
     * 獲取最新商品（公開接口）
     */
    @GetMapping("/latest")
    @Operation(summary = "獲取最新商品", description = "獲取最新上架的商品列表")
    public ApiResponse<PagedResponse<Product>> getLatestProducts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.info("獲取最新商品: page={}, size={}", page, size);
        Pageable pageable = PageRequest.of(page, size);
        return productService.getLatestProducts(pageable);
    }
    
    /**
     * 根據銷量排序查詢商品
     */
    @GetMapping("/best-selling")
    @Operation(summary = "獲取熱銷商品", description = "根據銷量排序獲取商品列表")
    public ApiResponse<PagedResponse<Product>> getBestSellingProducts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.info("獲取熱銷商品: page={}, size={}", page, size);
        Pageable pageable = PageRequest.of(page, size);
        return productService.getProductsBySoldCount(pageable);
    }
    
    /**
     * 根據價格排序查詢商品
     */
    @GetMapping("/price-sorted")
    @Operation(summary = "按價格排序商品", description = "根據價格升序或降序獲取商品列表")
    public ApiResponse<PagedResponse<Product>> getProductsByPrice(
            @RequestParam(defaultValue = "true") boolean ascending,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.info("按價格排序商品: ascending={}, page={}, size={}", ascending, page, size);
        Pageable pageable = PageRequest.of(page, size);
        return productService.getProductsByPrice(ascending, pageable);
    }
    
    /**
     * 獲取相關商品
     */
    @GetMapping("/{id}/related")
    @Operation(summary = "獲取相關商品", description = "獲取與指定商品相關的商品列表（同分類）")
    @Parameter(name = "id", description = "商品ID", required = true)
    public ApiResponse<List<Product>> getRelatedProducts(
            @PathVariable Long id,
            @RequestParam(defaultValue = "10") int limit) {
        
        log.info("獲取相關商品: productId={}, limit={}", id, limit);
        return productService.getRelatedProducts(id, limit);
    }
    
    /**
     * 創建商品（需要認證）
     */
    @PostMapping
    @Operation(summary = "創建商品", description = "創建新商品，需要用戶認證")
    public ApiResponse<Product> createProduct(
            @Valid @RequestBody Product product,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("創建商品: name={}, categoryId={}, price={}, userId={}", 
                product.getName(), product.getCategoryId(), product.getPrice(), userId);
        
        return productService.createProduct(product, userId);
    }
    
    /**
     * 更新商品（需要認證）
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新商品", description = "更新商品信息，需要用戶認證")
    @Parameter(name = "id", description = "商品ID", required = true)
    public ApiResponse<Product> updateProduct(
            @PathVariable Long id,
            @Valid @RequestBody Product product,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("更新商品: id={}, name={}, userId={}", id, product.getName(), userId);
        
        return productService.updateProduct(id, product, userId);
    }
    
    /**
     * 刪除商品（需要認證）
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "刪除商品", description = "軟刪除商品，需要用戶認證")
    @Parameter(name = "id", description = "商品ID", required = true)
    public ApiResponse<String> deleteProduct(
            @PathVariable Long id,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("刪除商品: id={}, userId={}", id, userId);
        
        return productService.deleteProduct(id, userId);
    }
    
    /**
     * 批量刪除商品（需要認證）
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量刪除商品", description = "批量軟刪除商品，需要用戶認證")
    public ApiResponse<String> batchDeleteProducts(
            @RequestBody List<Long> ids,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("批量刪除商品: ids={}, userId={}", ids, userId);
        
        return productService.batchDeleteProducts(ids, userId);
    }
    
    /**
     * 上架/下架商品（需要認證）
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "切換商品狀態", description = "上架或下架商品，需要用戶認證")
    @Parameter(name = "id", description = "商品ID", required = true)
    public ApiResponse<String> toggleProductStatus(
            @PathVariable Long id,
            @RequestParam Integer status,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("切換商品狀態: id={}, status={}, userId={}", id, status, userId);
        
        return productService.toggleProductStatus(id, status, userId);
    }
    
    /**
     * 設置推薦商品（需要認證）
     */
    @PutMapping("/{id}/recommended")
    @Operation(summary = "設置推薦商品", description = "設置或取消商品推薦，需要用戶認證")
    @Parameter(name = "id", description = "商品ID", required = true)
    public ApiResponse<String> setRecommended(
            @PathVariable Long id,
            @RequestParam Integer isRecommended,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("設置推薦商品: id={}, isRecommended={}, userId={}", id, isRecommended, userId);
        
        return productService.setRecommended(id, isRecommended, userId);
    }
    
    /**
     * 設置熱門商品（需要認證）
     */
    @PutMapping("/{id}/hot")
    @Operation(summary = "設置熱門商品", description = "設置或取消商品熱門標識，需要用戶認證")
    @Parameter(name = "id", description = "商品ID", required = true)
    public ApiResponse<String> setHot(
            @PathVariable Long id,
            @RequestParam Integer isHot,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("設置熱門商品: id={}, isHot={}, userId={}", id, isHot, userId);
        
        return productService.setHot(id, isHot, userId);
    }
    
    /**
     * 獲取商品瀏覽次數
     */
    @GetMapping("/{id}/view-count")
    @Operation(summary = "獲取瀏覽次數", description = "獲取商品的瀏覽次數統計")
    @Parameter(name = "id", description = "商品ID", required = true)
    public ApiResponse<Long> getViewCount(@PathVariable Long id) {
        log.info("獲取商品瀏覽次數: id={}", id);
        return productService.getViewCount(id);
    }
    
    /**
     * 檢查商品名稱是否存在
     */
    @GetMapping("/check-name")
    @Operation(summary = "檢查名稱重複", description = "檢查商品名稱是否已存在")
    public ApiResponse<Boolean> checkProductNameExists(
            @RequestParam String name,
            @RequestParam(required = false) Long excludeId) {
        
        log.info("檢查商品名稱: name={}, excludeId={}", name, excludeId);
        return productService.checkProductNameExists(name, excludeId);
    }
    
    /**
     * 獲取商品統計信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "獲取商品統計", description = "獲取商品的統計信息")
    public ApiResponse<Object> getProductStatistics() {
        log.info("獲取商品統計信息");
        return productService.getProductStatistics();
    }
    
    /**
     * 統計各狀態商品數量
     */
    @GetMapping("/status-statistics")
    @Operation(summary = "獲取狀態統計", description = "統計各狀態商品的數量")
    public ApiResponse<Object> getProductStatusStatistics() {
        log.info("獲取商品狀態統計");
        return productService.getProductStatusStatistics();
    }
    
    /**
     * 清理商品緩存（管理員功能）
     */
    @PostMapping("/cache/clear")
    @Operation(summary = "清理商品緩存", description = "清理所有商品相關的Redis緩存")
    public ApiResponse<String> clearProductCache() {
        try {
            log.info("管理員請求清理商品緩存");
            productRedisUtil.clearAllProductCache();
            return ApiResponse.success("商品緩存清理成功");
        } catch (Exception e) {
            log.error("清理商品緩存失敗", e);
            return ApiResponse.error("清理商品緩存失敗: " + e.getMessage());
        }
    }

    /**
     * 從Authentication中獲取用戶ID
     */
    private Long getUserId(Authentication authentication) {
        if (authentication != null && authentication.getPrincipal() instanceof org.springframework.security.core.userdetails.UserDetails) {
            // 這裡需要根據實際的UserDetails實現來獲取用戶ID
            // 假設UserDetails實現類有getId()方法
            return 1L; // 臨時返回，實際應該從authentication中獲取
        }
        return null;
    }

    /**
     * 將前端排序字段映射到數據庫字段
     */
    private String getSortField(String sortBy) {
        switch (sortBy) {
            case "price":
                return "price";
            case "sales":
                return "soldCount";
            case "name":
                return "name";
            case "createdAt":
                return "createdAt";
            default:
                return "sortOrder";
        }
    }
}
