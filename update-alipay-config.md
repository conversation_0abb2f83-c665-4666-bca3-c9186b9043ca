# 支付寶沙箱配置更新指南

## 當前配置狀態
- ✅ 支付寶 SDK 已集成
- ✅ 基本配置已完成
- ⚠️ 需要更新 ngrok 公網地址

## 需要更新的配置

### 1. 後端配置文件
文件：`backend/backend/src/main/resources/application.yml`

需要將以下配置中的回調地址更新為您的 ngrok 公網地址：

```yaml
alipay:
  config:
    notify-url: https://YOUR_NGROK_URL.ngrok.io/api/payment/alipay/callback
    return-url: https://YOUR_NGROK_URL.ngrok.io/payment/success
```

### 2. 前端配置
文件：`frontend/src/views/PaymentView.vue`

可能需要更新返回地址配置。

## 測試步驟

### 1. 獲取 ngrok 地址
請提供您的 ngrok 公網地址，格式類似：
```
https://abc123.ngrok.io
```

### 2. 更新配置
將上述地址替換到配置文件中。

### 3. 重啟後端服務
更新配置後需要重啟 SpringBoot 應用。

### 4. 測試支付流程
1. 登錄系統
2. 創建訂單
3. 進入支付頁面
4. 點擊"立即支付"
5. 跳轉到支付寶沙箱
6. 使用測試賬號完成支付
7. 驗證回調處理

## 支付寶沙箱測試賬號
- **買家賬號**: <EMAIL>
- **登錄密碼**: 111111
- **支付密碼**: 111111

## 常見問題

### 1. 支付按鈕無法點擊
- 檢查訂單狀態是否為待付款
- 檢查前端控制台是否有 JavaScript 錯誤
- 檢查網絡請求是否正常

### 2. 支付寶頁面無法打開
- 檢查 ngrok 地址是否正確
- 檢查後端服務是否正常運行
- 檢查支付寶配置是否正確

### 3. 回調處理失敗
- 檢查 ngrok 是否正常運行
- 檢查回調地址是否可以公網訪問
- 檢查後端日誌中的錯誤信息

## 下一步
請提供您的 ngrok 公網地址，我將立即更新配置並測試支付功能。
