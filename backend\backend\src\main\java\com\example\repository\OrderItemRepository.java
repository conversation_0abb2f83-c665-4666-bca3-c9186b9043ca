package com.example.repository;

import com.example.entity.OrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 訂單項目Repository
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Repository
public interface OrderItemRepository extends JpaRepository<OrderItem, Long> {
    
    /**
     * 根據訂單ID查找所有項目
     */
    List<OrderItem> findByOrderIdOrderByCreatedAtAsc(Long orderId);
    
    /**
     * 根據訂單ID查找所有項目（預加載商品信息）
     */
    @Query("SELECT oi FROM OrderItem oi LEFT JOIN FETCH oi.product WHERE oi.orderId = :orderId ORDER BY oi.createdAt ASC")
    List<OrderItem> findByOrderIdWithProduct(@Param("orderId") Long orderId);
    
    /**
     * 根據商品ID查找所有訂單項目
     */
    List<OrderItem> findByProductIdOrderByCreatedAtDesc(Long productId);
    
    /**
     * 統計訂單中的商品數量
     */
    @Query("SELECT SUM(oi.quantity) FROM OrderItem oi WHERE oi.orderId = :orderId")
    Integer sumQuantityByOrderId(@Param("orderId") Long orderId);
    
    /**
     * 計算訂單的總金額
     */
    @Query("SELECT SUM(oi.subtotal) FROM OrderItem oi WHERE oi.orderId = :orderId")
    java.math.BigDecimal sumSubtotalByOrderId(@Param("orderId") Long orderId);
    
    /**
     * 統計商品的銷售數量
     */
    @Query("SELECT SUM(oi.quantity) FROM OrderItem oi JOIN oi.order o WHERE oi.productId = :productId AND o.status IN (1, 2, 3)")
    Integer sumSoldQuantityByProductId(@Param("productId") Long productId);
    
    /**
     * 統計商品的銷售金額
     */
    @Query("SELECT SUM(oi.subtotal) FROM OrderItem oi JOIN oi.order o WHERE oi.productId = :productId AND o.status IN (1, 2, 3)")
    java.math.BigDecimal sumSoldAmountByProductId(@Param("productId") Long productId);
    
    /**
     * 查詢熱銷商品統計
     */
    @Query("SELECT oi.productId, oi.productName, SUM(oi.quantity) as totalQuantity " +
           "FROM OrderItem oi JOIN oi.order o " +
           "WHERE o.status IN (1, 2, 3) " +
           "GROUP BY oi.productId, oi.productName " +
           "ORDER BY totalQuantity DESC")
    List<Object[]> findHotSellingProducts();
    
    /**
     * 根據訂單ID刪除所有項目
     */
    void deleteByOrderId(Long orderId);
    
    /**
     * 統計訂單中的商品種類數量
     */
    long countByOrderId(Long orderId);
    
    /**
     * 檢查訂單中是否包含指定商品
     */
    boolean existsByOrderIdAndProductId(Long orderId, Long productId);
}
