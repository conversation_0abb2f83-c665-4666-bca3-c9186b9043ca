import { test, expect } from '@playwright/test';

test.describe('前端組件渲染測試（無需後端）', () => {
  
  test('登錄頁面基本渲染測試', async ({ page }) => {
    await page.goto('/login');
    
    // 檢查頁面標題
    await expect(page.locator('h1')).toContainText('歡迎回來');
    
    // 檢查登錄表單元素
    await expect(page.locator('input[type="email"], input[placeholder*="用戶名"], textbox[name*="用戶名"]')).toBeVisible();
    await expect(page.locator('input[type="password"], textbox[name*="密碼"]')).toBeVisible();
    await expect(page.locator('button[type="submit"], button:has-text("登入")')).toBeVisible();
    
    // 檢查註冊鏈接
    await expect(page.locator('a[href="/register"], a:has-text("立即註冊")')).toBeVisible();
  });

  test('註冊頁面基本渲染測試', async ({ page }) => {
    await page.goto('/register');
    
    // 檢查頁面是否正確加載
    await page.waitForLoadState('networkidle');
    
    // 檢查是否有註冊相關的元素
    const hasRegisterForm = await page.locator('form, .register-form, input[type="email"]').count() > 0;
    const hasRegisterButton = await page.locator('button:has-text("註冊"), button:has-text("注册"), button[type="submit"]').count() > 0;
    
    expect(hasRegisterForm || hasRegisterButton).toBeTruthy();
  });

  test('商品頁面路由測試', async ({ page }) => {
    // 測試商品列表頁面路由
    await page.goto('/products');
    
    // 等待頁面加載
    await page.waitForLoadState('networkidle');
    
    // 檢查URL是否正確
    expect(page.url()).toContain('/products');
    
    // 檢查頁面標題
    await expect(page).toHaveTitle(/Vite \+ Vue \+ TS/);
  });

  test('商品詳情頁面路由測試', async ({ page }) => {
    // 測試商品詳情頁面路由
    await page.goto('/app/products/1');

    // 等待頁面加載
    await page.waitForLoadState('networkidle');

    // 檢查URL是否正確
    expect(page.url()).toContain('/app/products/1');
  });

  test('響應式設計基本測試', async ({ page }) => {
    // 測試桌面視圖
    await page.setViewportSize({ width: 1280, height: 720 });
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    // 檢查桌面佈局
    const desktopLayout = await page.locator('body').boundingBox();
    expect(desktopLayout?.width).toBeGreaterThan(1000);
    
    // 測試移動端視圖
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // 檢查移動端佈局
    const mobileLayout = await page.locator('body').boundingBox();
    expect(mobileLayout?.width).toBeLessThan(400);
    
    // 恢復桌面視圖
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('頁面性能基本測試', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // 檢查頁面加載時間是否在合理範圍內（10秒內）
    expect(loadTime).toBeLessThan(10000);
    
    console.log(`登錄頁面加載時間: ${loadTime}ms`);
  });

  test('JavaScript 錯誤檢測', async ({ page }) => {
    const errors: string[] = [];
    
    // 監聽 JavaScript 錯誤
    page.on('pageerror', (error) => {
      errors.push(error.message);
    });
    
    // 監聽控制台錯誤
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    // 檢查是否有 JavaScript 錯誤
    if (errors.length > 0) {
      console.log('JavaScript 錯誤:', errors);
    }
    
    // 允許一些非關鍵錯誤，但不應該有太多
    expect(errors.length).toBeLessThan(5);
  });

  test('基本導航測試', async ({ page }) => {
    // 從登錄頁面開始
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    // 點擊註冊鏈接
    const registerLink = page.locator('a[href="/register"], a:has-text("立即註冊")');
    if (await registerLink.isVisible()) {
      await registerLink.click();
      await page.waitForLoadState('networkidle');
      expect(page.url()).toContain('/register');
    }
    
    // 測試瀏覽器後退
    await page.goBack();
    await page.waitForLoadState('networkidle');
    expect(page.url()).toContain('/login');
  });

  test('表單基本交互測試', async ({ page }) => {
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    // 測試用戶名輸入
    const usernameInput = page.locator('input[type="email"], textbox[name*="用戶名"]').first();
    if (await usernameInput.isVisible()) {
      await usernameInput.fill('<EMAIL>');
      const value = await usernameInput.inputValue();
      expect(value).toBe('<EMAIL>');
    }
    
    // 測試密碼輸入
    const passwordInput = page.locator('input[type="password"], textbox[name*="密碼"]').first();
    if (await passwordInput.isVisible()) {
      await passwordInput.fill('testpassword');
      const value = await passwordInput.inputValue();
      expect(value).toBe('testpassword');
    }
    
    // 測試記住我複選框
    const rememberCheckbox = page.locator('input[type="checkbox"], checkbox');
    if (await rememberCheckbox.isVisible()) {
      await rememberCheckbox.check();
      expect(await rememberCheckbox.isChecked()).toBe(true);
    }
  });

  test('CSS 樣式基本檢查', async ({ page }) => {
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    // 檢查主要容器是否有基本樣式
    const mainContainer = page.locator('body > div, .app, #app').first();
    if (await mainContainer.isVisible()) {
      const styles = await mainContainer.evaluate((el) => {
        const computed = window.getComputedStyle(el);
        return {
          display: computed.display,
          position: computed.position,
          width: computed.width,
          height: computed.height
        };
      });
      
      // 檢查基本樣式是否應用
      expect(styles.display).not.toBe('none');
      expect(styles.width).not.toBe('0px');
    }
  });

  test('圖片資源加載測試', async ({ page }) => {
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    // 檢查頁面中的圖片
    const images = page.locator('img');
    const imageCount = await images.count();
    
    if (imageCount > 0) {
      // 檢查第一張圖片是否加載成功
      const firstImage = images.first();
      const naturalWidth = await firstImage.evaluate((img: HTMLImageElement) => img.naturalWidth);
      
      // 如果圖片存在，應該有寬度
      if (await firstImage.isVisible()) {
        expect(naturalWidth).toBeGreaterThan(0);
      }
    }
  });

  test('本地存儲功能測試', async ({ page }) => {
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    // 測試本地存儲功能
    await page.evaluate(() => {
      localStorage.setItem('test-key', 'test-value');
    });
    
    const storedValue = await page.evaluate(() => {
      return localStorage.getItem('test-key');
    });
    
    expect(storedValue).toBe('test-value');
    
    // 清理測試數據
    await page.evaluate(() => {
      localStorage.removeItem('test-key');
    });
  });

  test('頁面元數據檢查', async ({ page }) => {
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    // 檢查頁面標題
    const title = await page.title();
    expect(title).toBeTruthy();
    expect(title.length).toBeGreaterThan(0);
    
    // 檢查字符編碼
    const charset = await page.evaluate(() => {
      const metaCharset = document.querySelector('meta[charset]');
      return metaCharset ? metaCharset.getAttribute('charset') : null;
    });
    
    expect(charset).toBeTruthy();
  });

  test('無障礙性基本檢查', async ({ page }) => {
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    // 檢查是否有適當的標題結構
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').count();
    expect(headings).toBeGreaterThan(0);
    
    // 檢查表單標籤
    const labels = await page.locator('label').count();
    const inputs = await page.locator('input').count();
    
    // 如果有輸入框，應該有相應的標籤或aria-label
    if (inputs > 0) {
      const inputsWithLabels = await page.locator('input[aria-label], input[aria-labelledby], label input').count();
      expect(inputsWithLabels).toBeGreaterThan(0);
    }
  });
});
