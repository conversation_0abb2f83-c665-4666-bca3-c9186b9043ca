package com.example.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 商品Redis緩存工具類
 * 基於學習的Redis緩存實現模式
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Component
@Slf4j
public class ProductRedisUtil {
    
    @Autowired
    @Qualifier("redisObjectTemplate")
    private RedisTemplate<String, Object> redisObjectTemplate;
    
    /**
     * 緩存過期時間常量（秒）
     */
    public static class ExpireTime {
        public static final long CATEGORY_TREE = 60 * 60 * 24 * 7;      // 分類樹：7天
        public static final long PRODUCT_DETAIL = 60 * 60 * 2;          // 商品詳情：2小時
        public static final long HOT_PRODUCTS = 60 * 60;                // 熱門商品：1小時
        public static final long PRODUCT_LIST = 60 * 30;                // 商品列表：30分鐘
        public static final long SEARCH_RESULT = 60 * 15;               // 搜索結果：15分鐘
        public static final long EMPTY_RESULT = 60 * 5;                 // 空結果：5分鐘（防止緩存穿透）
    }
    
    /**
     * 保存對象數據（永不過期）
     */
    public void setObject(String key, Object value) {
        try {
            redisObjectTemplate.opsForValue().set(key, value);
        } catch (Exception e) {
            throw new RuntimeException("Redis保存對象失敗: " + e.getMessage(), e);
        }
    }
    
    /**
     * 保存對象數據（指定過期時間）
     */
    public void setObject(String key, Object value, long seconds) {
        try {
            redisObjectTemplate.opsForValue().set(key, value, seconds, TimeUnit.SECONDS);
            log.debug("Redis保存對象成功: key={}, seconds={}", key, seconds);
        } catch (Exception e) {
            log.error("Redis保存對象失敗: key={}, error={}", key, e.getMessage(), e);
            throw new RuntimeException("Redis保存對象失敗: " + e.getMessage(), e);
        }
    }
    
    /**
     * 獲取對象數據
     */
    public Object getObject(String key) {
        try {
            return redisObjectTemplate.opsForValue().get(key);
        } catch (Exception e) {
            throw new RuntimeException("Redis獲取對象失敗: " + e.getMessage(), e);
        }
    }
    
    /**
     * 刪除緩存
     */
    public void deleteObject(String key) {
        try {
            redisObjectTemplate.delete(key);
        } catch (Exception e) {
            throw new RuntimeException("Redis刪除對象失敗: " + e.getMessage(), e);
        }
    }
    
    /**
     * 檢查key是否存在
     */
    public boolean hasKey(String key) {
        try {
            return Boolean.TRUE.equals(redisObjectTemplate.hasKey(key));
        } catch (Exception e) {
            throw new RuntimeException("Redis檢查key失敗: " + e.getMessage(), e);
        }
    }
    
    /**
     * 設置key的過期時間
     */
    public void expire(String key, long seconds) {
        try {
            redisObjectTemplate.expire(key, seconds, TimeUnit.SECONDS);
        } catch (Exception e) {
            throw new RuntimeException("Redis設置過期時間失敗: " + e.getMessage(), e);
        }
    }
    
    /**
     * 獲取key的剩餘過期時間
     */
    public long getExpire(String key) {
        try {
            Long expire = redisObjectTemplate.getExpire(key, TimeUnit.SECONDS);
            return expire != null ? expire : -1;
        } catch (Exception e) {
            throw new RuntimeException("Redis獲取過期時間失敗: " + e.getMessage(), e);
        }
    }
    
    /**
     * 批量刪除緩存（支持通配符）
     */
    public void deletePattern(String pattern) {
        try {
            var keys = redisObjectTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisObjectTemplate.delete(keys);
            }
        } catch (Exception e) {
            throw new RuntimeException("Redis批量刪除失敗: " + e.getMessage(), e);
        }
    }
    
    /**
     * 緩存空結果（防止緩存穿透）
     */
    public void cacheEmptyResult(String key) {
        setObject(key, "", ExpireTime.EMPTY_RESULT);
    }
    
    /**
     * 檢查是否為空結果緩存
     */
    public boolean isEmptyResult(Object value) {
        return value != null && "".equals(value.toString());
    }
    
    /**
     * 安全獲取對象（處理空結果）
     */
    public Object safeGetObject(String key) {
        try {
            Object value = getObject(key);
            if (isEmptyResult(value)) {
                return null;
            }
            return value;
        } catch (Exception e) {
            log.warn("Redis獲取對象失敗，刪除可能損壞的緩存: key={}, error={}", key, e.getMessage());
            // 刪除可能損壞的緩存
            try {
                deleteObject(key);
            } catch (Exception deleteEx) {
                log.error("刪除損壞緩存失敗: key={}", key, deleteEx);
            }
            return null;
        }
    }
    
    /**
     * 原子性遞增
     */
    public long increment(String key) {
        try {
            Long result = redisObjectTemplate.opsForValue().increment(key);
            return result != null ? result : 0;
        } catch (Exception e) {
            throw new RuntimeException("Redis遞增失敗: " + e.getMessage(), e);
        }
    }

    /**
     * 清理所有商品相關緩存
     */
    public void clearAllProductCache() {
        try {
            log.info("開始清理所有商品相關緩存");

            // 清理商品列表緩存
            deletePattern("product:list:*");

            // 清理商品詳情緩存
            deletePattern("product:detail:*");

            // 清理熱門商品緩存
            deletePattern("product:hot:*");

            // 清理推薦商品緩存
            deletePattern("product:recommended:*");

            // 清理搜索結果緩存
            deletePattern("product:search:*");

            // 清理分類商品緩存
            deletePattern("product:category:*");

            log.info("商品相關緩存清理完成");
        } catch (Exception e) {
            log.error("清理商品緩存失敗", e);
        }
    }
    
    /**
     * 原子性遞增（指定步長）
     */
    public long increment(String key, long delta) {
        try {
            Long result = redisObjectTemplate.opsForValue().increment(key, delta);
            return result != null ? result : 0;
        } catch (Exception e) {
            throw new RuntimeException("Redis遞增失敗: " + e.getMessage(), e);
        }
    }
    
    /**
     * 原子性遞減
     */
    public long decrement(String key) {
        try {
            Long result = redisObjectTemplate.opsForValue().decrement(key);
            return result != null ? result : 0;
        } catch (Exception e) {
            throw new RuntimeException("Redis遞減失敗: " + e.getMessage(), e);
        }
    }
}
