package com.example.controller;

import com.example.dto.ApiResponse;
import com.example.entity.Admin;
import com.example.service.AdminService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/test")
@CrossOrigin(origins = "*")
@Slf4j
public class AdminTestController {
    
    @Autowired
    private AdminService adminService;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    /**
     * 測試管理員密碼
     */
    @PostMapping("/password")
    public ApiResponse<Map<String, Object>> testPassword(@RequestParam String username, @RequestParam String password) {
        try {
            Optional<Admin> adminOpt = adminService.findByUsername(username);
            
            Map<String, Object> result = new HashMap<>();
            
            if (adminOpt.isPresent()) {
                Admin admin = adminOpt.get();
                boolean matches = passwordEncoder.matches(password, admin.getPassword());
                
                result.put("found", true);
                result.put("username", admin.getUsername());
                result.put("enabled", admin.isEnabled());
                result.put("role", admin.getRole());
                result.put("passwordMatches", matches);
                result.put("storedPasswordHash", admin.getPassword());
                
                log.info("管理員密碼測試: username={}, matches={}", username, matches);
                
            } else {
                result.put("found", false);
                log.warn("管理員不存在: {}", username);
            }
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("管理員密碼測試失敗", e);
            return ApiResponse.error("測試失敗: " + e.getMessage());
        }
    }
    
    /**
     * 生成密碼哈希
     */
    @GetMapping("/hash")
    public ApiResponse<String> generateHash(@RequestParam String password) {
        try {
            String hash = passwordEncoder.encode(password);
            log.info("生成密碼哈希: {}", hash);
            return ApiResponse.success(hash);
        } catch (Exception e) {
            log.error("生成密碼哈希失敗", e);
            return ApiResponse.error("生成失敗: " + e.getMessage());
        }
    }

    /**
     * 測試管理員認證狀態（需要管理員權限）
     */
    @GetMapping("/admin-auth-status")
    public ApiResponse<Map<String, Object>> getAdminAuthStatus(Authentication authentication) {
        try {
            Map<String, Object> result = new HashMap<>();

            if (authentication != null) {
                result.put("authenticated", true);
                result.put("principal", authentication.getPrincipal().getClass().getSimpleName());
                result.put("authorities", authentication.getAuthorities().toString());
                result.put("name", authentication.getName());

                if (authentication.getPrincipal() instanceof Admin) {
                    Admin admin = (Admin) authentication.getPrincipal();
                    result.put("adminId", admin.getId());
                    result.put("adminRole", admin.getRole());
                    result.put("adminEnabled", admin.isEnabled());
                }
            } else {
                result.put("authenticated", false);
            }

            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("獲取管理員認證狀態失敗", e);
            return ApiResponse.error("獲取失敗: " + e.getMessage());
        }
    }

    /**
     * 測試管理員認證狀態（公開端點）
     */
    @GetMapping("/auth-status")
    public ApiResponse<Map<String, Object>> getAuthStatus(Authentication authentication) {
        try {
            Map<String, Object> result = new HashMap<>();

            if (authentication != null) {
                result.put("authenticated", true);
                result.put("principal", authentication.getPrincipal().getClass().getSimpleName());
                result.put("authorities", authentication.getAuthorities().toString());
                result.put("name", authentication.getName());

                if (authentication.getPrincipal() instanceof Admin) {
                    Admin admin = (Admin) authentication.getPrincipal();
                    result.put("adminId", admin.getId());
                    result.put("adminRole", admin.getRole());
                    result.put("adminEnabled", admin.isEnabled());
                }
            } else {
                result.put("authenticated", false);
            }

            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("獲取認證狀態失敗", e);
            return ApiResponse.error("獲取失敗: " + e.getMessage());
        }
    }
}
