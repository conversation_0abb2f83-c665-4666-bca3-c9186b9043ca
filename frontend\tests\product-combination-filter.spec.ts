import { test, expect, Page } from '@playwright/test'

/**
 * 商品組合篩選功能專項測試
 * 重點測試排序和篩選的組合使用
 */

// 測試配置
const BASE_URL = 'http://localhost:5173'
const PRODUCTS_URL = `${BASE_URL}/products`

// 等待時間配置
const WAIT_TIME = {
  SHORT: 1000,
  MEDIUM: 2000,
  LONG: 3000
}

test.describe('商品組合篩選功能測試', () => {
  
  test.beforeEach(async ({ page }) => {
    // 每個測試前都導航到商品頁面
    await page.goto(PRODUCTS_URL)
    await page.waitForLoadState('networkidle')
    
    // 等待商品列表加載
    await page.waitForSelector('.product-list', { timeout: 10000 })
    await page.waitForTimeout(WAIT_TIME.SHORT)
  })

  test('價格升序 + 推薦商品組合篩選測試', async ({ page }) => {
    console.log('測試價格升序 + 推薦商品組合篩選...')
    
    // 1. 先選擇推薦商品篩選
    const recommendedTag = page.locator('.el-check-tag:has-text("推薦商品")')
    await recommendedTag.click()
    await page.waitForTimeout(WAIT_TIME.SHORT)
    
    // 檢查推薦標籤是否被選中
    await expect(recommendedTag).toHaveClass(/is-checked/)
    
    // 2. 再選擇價格升序排序
    await page.click('button:has-text("價格 ↑")')
    await page.waitForTimeout(WAIT_TIME.MEDIUM)
    
    // 檢查排序按鈕狀態
    const priceAscBtn = page.locator('button:has-text("價格 ↑")')
    await expect(priceAscBtn).toHaveClass(/el-button--primary/)
    
    // 檢查推薦標籤仍然被選中
    await expect(recommendedTag).toHaveClass(/is-checked/)
    
    // 3. 驗證結果
    const productCards = page.locator('.product-card')
    if (await productCards.count() > 0) {
      console.log('組合篩選成功，找到商品')
      
      // 檢查商品是否有推薦標籤
      const recommendedBadges = page.locator('.product-tags .el-tag:has-text("推薦")')
      const badgeCount = await recommendedBadges.count()
      console.log(`找到 ${badgeCount} 個推薦商品標籤`)
      
      // 檢查價格排序（如果有多個商品）
      const count = Math.min(3, await productCards.count())
      if (count > 1) {
        const prices: number[] = []
        for (let i = 0; i < count; i++) {
          const priceText = await productCards.nth(i).locator('.current-price').textContent()
          const price = parseFloat(priceText?.replace('¥', '') || '0')
          prices.push(price)
          console.log(`推薦商品 ${i + 1} 價格: ¥${price}`)
        }
        
        // 驗證價格是升序排列
        for (let i = 1; i < prices.length; i++) {
          expect(prices[i]).toBeGreaterThanOrEqual(prices[i - 1])
        }
      }
    } else {
      console.log('沒有找到符合條件的商品')
    }
  })

  test('價格降序 + 熱門商品組合篩選測試', async ({ page }) => {
    console.log('測試價格降序 + 熱門商品組合篩選...')
    
    // 1. 先選擇熱門商品篩選
    const hotTag = page.locator('.el-check-tag:has-text("熱門商品")')
    await hotTag.click()
    await page.waitForTimeout(WAIT_TIME.SHORT)
    
    // 檢查熱門標籤是否被選中
    await expect(hotTag).toHaveClass(/is-checked/)
    
    // 2. 再選擇價格降序排序
    await page.click('button:has-text("價格 ↓")')
    await page.waitForTimeout(WAIT_TIME.MEDIUM)
    
    // 檢查排序按鈕狀態
    const priceDescBtn = page.locator('button:has-text("價格 ↓")')
    await expect(priceDescBtn).toHaveClass(/el-button--primary/)
    
    // 檢查熱門標籤仍然被選中
    await expect(hotTag).toHaveClass(/is-checked/)
    
    // 3. 驗證結果
    const productCards = page.locator('.product-card')
    if (await productCards.count() > 0) {
      console.log('組合篩選成功，找到商品')
      
      // 檢查商品是否有熱門標籤
      const hotBadges = page.locator('.product-tags .el-tag:has-text("熱門")')
      const badgeCount = await hotBadges.count()
      console.log(`找到 ${badgeCount} 個熱門商品標籤`)
      
      // 檢查價格排序（如果有多個商品）
      const count = Math.min(3, await productCards.count())
      if (count > 1) {
        const prices: number[] = []
        for (let i = 0; i < count; i++) {
          const priceText = await productCards.nth(i).locator('.current-price').textContent()
          const price = parseFloat(priceText?.replace('¥', '') || '0')
          prices.push(price)
          console.log(`熱門商品 ${i + 1} 價格: ¥${price}`)
        }
        
        // 驗證價格是降序排列
        for (let i = 1; i < prices.length; i++) {
          expect(prices[i]).toBeLessThanOrEqual(prices[i - 1])
        }
      }
    } else {
      console.log('沒有找到符合條件的商品')
    }
  })

  test('銷量排序 + 推薦商品組合篩選測試', async ({ page }) => {
    console.log('測試銷量排序 + 推薦商品組合篩選...')
    
    // 1. 先選擇推薦商品篩選
    const recommendedTag = page.locator('.el-check-tag:has-text("推薦商品")')
    await recommendedTag.click()
    await page.waitForTimeout(WAIT_TIME.SHORT)
    
    // 2. 再選擇銷量排序
    await page.click('button:has-text("銷量優先")')
    await page.waitForTimeout(WAIT_TIME.MEDIUM)
    
    // 檢查狀態
    await expect(recommendedTag).toHaveClass(/is-checked/)
    const salesBtn = page.locator('button:has-text("銷量優先")')
    await expect(salesBtn).toHaveClass(/el-button--primary/)
    
    // 3. 驗證結果
    const productCards = page.locator('.product-card')
    if (await productCards.count() > 0) {
      console.log('銷量排序 + 推薦商品組合篩選成功')
      
      // 記錄商品信息
      const count = Math.min(3, await productCards.count())
      for (let i = 0; i < count; i++) {
        const productName = await productCards.nth(i).locator('.product-name').textContent()
        console.log(`推薦商品 ${i + 1}: ${productName}`)
      }
    }
  })

  test('搜索 + 價格排序組合測試', async ({ page }) => {
    console.log('測試搜索 + 價格排序組合...')
    
    // 1. 先輸入搜索關鍵詞
    const searchInput = page.locator('.search-box input')
    await searchInput.fill('iPhone')
    await page.click('.search-box button:has-text("搜索")')
    await page.waitForTimeout(WAIT_TIME.SHORT)
    
    // 2. 再選擇價格升序排序
    await page.click('button:has-text("價格 ↑")')
    await page.waitForTimeout(WAIT_TIME.MEDIUM)
    
    // 檢查排序按鈕狀態
    const priceAscBtn = page.locator('button:has-text("價格 ↑")')
    await expect(priceAscBtn).toHaveClass(/el-button--primary/)
    
    // 3. 驗證結果
    const productCards = page.locator('.product-card')
    if (await productCards.count() > 0) {
      console.log('搜索 + 價格排序組合成功')
      
      // 檢查搜索結果是否包含關鍵詞
      const firstProductName = await productCards.first().locator('.product-name').textContent()
      console.log('搜索結果第一個商品:', firstProductName)
      
      if (firstProductName) {
        expect(firstProductName.toLowerCase()).toContain('iphone')
      }
    }
  })

  test('多重篩選條件組合測試', async ({ page }) => {
    console.log('測試多重篩選條件組合...')
    
    // 1. 選擇推薦商品
    const recommendedTag = page.locator('.el-check-tag:has-text("推薦商品")')
    await recommendedTag.click()
    await page.waitForTimeout(WAIT_TIME.SHORT)
    
    // 2. 選擇熱門商品
    const hotTag = page.locator('.el-check-tag:has-text("熱門商品")')
    await hotTag.click()
    await page.waitForTimeout(WAIT_TIME.SHORT)
    
    // 3. 選擇價格排序
    await page.click('button:has-text("價格 ↑")')
    await page.waitForTimeout(WAIT_TIME.MEDIUM)
    
    // 檢查所有條件都生效
    await expect(recommendedTag).toHaveClass(/is-checked/)
    await expect(hotTag).toHaveClass(/is-checked/)
    const priceAscBtn = page.locator('button:has-text("價格 ↑")')
    await expect(priceAscBtn).toHaveClass(/el-button--primary/)
    
    // 4. 驗證結果
    const productCards = page.locator('.product-card')
    const productCount = await productCards.count()
    console.log(`多重篩選找到 ${productCount} 個商品`)
    
    if (productCount > 0) {
      // 檢查商品是否同時有推薦和熱門標籤
      const recommendedBadges = page.locator('.product-tags .el-tag:has-text("推薦")')
      const hotBadges = page.locator('.product-tags .el-tag:has-text("熱門")')
      
      console.log(`推薦標籤數量: ${await recommendedBadges.count()}`)
      console.log(`熱門標籤數量: ${await hotBadges.count()}`)
    }
  })

  test('篩選條件重置測試', async ({ page }) => {
    console.log('測試篩選條件重置...')
    
    // 1. 設置多個篩選條件
    const recommendedTag = page.locator('.el-check-tag:has-text("推薦商品")')
    await recommendedTag.click()
    await page.click('button:has-text("價格 ↑")')
    await page.waitForTimeout(WAIT_TIME.SHORT)
    
    // 2. 檢查條件已設置
    await expect(recommendedTag).toHaveClass(/is-checked/)
    const priceAscBtn = page.locator('button:has-text("價格 ↑")')
    await expect(priceAscBtn).toHaveClass(/el-button--primary/)
    
    // 3. 點擊清除篩選（如果存在）
    const clearBtn = page.locator('button:has-text("清除篩選")')
    if (await clearBtn.isVisible()) {
      await clearBtn.click()
      await page.waitForTimeout(WAIT_TIME.SHORT)
      
      // 檢查是否回到默認狀態
      const defaultSortBtn = page.locator('button:has-text("默認排序")')
      await expect(defaultSortBtn).toHaveClass(/el-button--primary/)
      await expect(recommendedTag).not.toHaveClass(/is-checked/)
    } else {
      // 手動重置：點擊默認排序和取消推薦篩選
      await page.click('button:has-text("默認排序")')
      await recommendedTag.click()
      await page.waitForTimeout(WAIT_TIME.SHORT)
      
      console.log('手動重置篩選條件完成')
    }
  })
})
