package com.example.repository;

import com.example.entity.ProductCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 商品分類數據訪問層
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Repository
public interface ProductCategoryRepository extends JpaRepository<ProductCategory, Long> {
    
    /**
     * 根據狀態查詢所有分類（用於構建樹形結構）
     * 按層級和排序號排序
     */
    @Query("SELECT pc FROM ProductCategory pc WHERE pc.status = :status ORDER BY pc.level ASC, pc.sortOrder ASC, pc.id ASC")
    List<ProductCategory> findByStatusOrderByLevelAndSort(@Param("status") Integer status);
    
    /**
     * 查詢根分類列表
     */
    @Query("SELECT pc FROM ProductCategory pc WHERE pc.parentId = 0 AND pc.status = :status ORDER BY pc.sortOrder ASC, pc.id ASC")
    List<ProductCategory> findRootCategoriesByStatus(@Param("status") Integer status);
    
    /**
     * 根據父ID查詢子分類
     */
    @Query("SELECT pc FROM ProductCategory pc WHERE pc.parentId = :parentId AND pc.status = :status ORDER BY pc.sortOrder ASC, pc.id ASC")
    List<ProductCategory> findByParentIdAndStatus(@Param("parentId") Long parentId, @Param("status") Integer status);
    
    /**
     * 查詢指定分類的所有子分類（包括子子分類）
     */
    @Query(value = "WITH RECURSIVE category_tree AS (" +
            "  SELECT id, name, parent_id, level FROM product_categories WHERE id = :categoryId " +
            "  UNION ALL " +
            "  SELECT pc.id, pc.name, pc.parent_id, pc.level " +
            "  FROM product_categories pc " +
            "  INNER JOIN category_tree ct ON pc.parent_id = ct.id " +
            ") " +
            "SELECT * FROM category_tree WHERE id != :categoryId", 
            nativeQuery = true)
    List<ProductCategory> findAllChildrenByCategoryId(@Param("categoryId") Long categoryId);
    
    /**
     * 檢查分類名稱是否存在（同一父分類下）
     */
    @Query("SELECT COUNT(pc) > 0 FROM ProductCategory pc WHERE pc.name = :name AND pc.parentId = :parentId AND pc.id != :excludeId")
    boolean existsByNameAndParentIdAndIdNot(@Param("name") String name, @Param("parentId") Long parentId, @Param("excludeId") Long excludeId);
    
    /**
     * 檢查分類名稱是否存在（新增時使用）
     */
    boolean existsByNameAndParentId(String name, Long parentId);
    
    /**
     * 根據層級查詢分類
     */
    List<ProductCategory> findByLevelAndStatusOrderBySortOrderAsc(Integer level, Integer status);
    
    /**
     * 查詢葉子分類（可以添加商品的分類）
     */
    @Query("SELECT pc FROM ProductCategory pc WHERE pc.isLeaf = 1 AND pc.status = :status ORDER BY pc.level ASC, pc.sortOrder ASC")
    List<ProductCategory> findLeafCategoriesByStatus(@Param("status") Integer status);
    
    /**
     * 統計子分類數量
     */
    @Query("SELECT COUNT(pc) FROM ProductCategory pc WHERE pc.parentId = :parentId AND pc.status = :status")
    long countByParentIdAndStatus(@Param("parentId") Long parentId, @Param("status") Integer status);
    
    /**
     * 查詢分類路徑（從根到當前分類）
     */
    @Query(value = "WITH RECURSIVE category_path AS (" +
            "  SELECT id, name, parent_id, level, CAST(name AS CHAR(1000)) as path " +
            "  FROM product_categories WHERE id = :categoryId " +
            "  UNION ALL " +
            "  SELECT pc.id, pc.name, pc.parent_id, pc.level, " +
            "         CONCAT(pc.name, ' > ', cp.path) as path " +
            "  FROM product_categories pc " +
            "  INNER JOIN category_path cp ON pc.id = cp.parent_id " +
            ") " +
            "SELECT path FROM category_path WHERE parent_id = 0", 
            nativeQuery = true)
    Optional<String> findCategoryPath(@Param("categoryId") Long categoryId);
    
    /**
     * 分頁查詢分類（管理後台使用）
     */
    @Query("SELECT pc FROM ProductCategory pc WHERE " +
           "(:name IS NULL OR pc.name LIKE %:name%) AND " +
           "(:parentId IS NULL OR pc.parentId = :parentId) AND " +
           "(:status IS NULL OR pc.status = :status) " +
           "ORDER BY pc.level ASC, pc.sortOrder ASC, pc.id ASC")
    Page<ProductCategory> findCategoriesWithFilters(
            @Param("name") String name,
            @Param("parentId") Long parentId,
            @Param("status") Integer status,
            Pageable pageable);
    
    /**
     * 查詢最大排序號
     */
    @Query("SELECT COALESCE(MAX(pc.sortOrder), 0) FROM ProductCategory pc WHERE pc.parentId = :parentId")
    Integer findMaxSortOrderByParentId(@Param("parentId") Long parentId);
    
    /**
     * 批量更新父分類的葉子節點狀態
     */
    @Query("UPDATE ProductCategory pc SET pc.isLeaf = :isLeaf WHERE pc.id = :categoryId")
    void updateLeafStatus(@Param("categoryId") Long categoryId, @Param("isLeaf") Integer isLeaf);
}
