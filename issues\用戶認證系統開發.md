# 用戶認證系統開發計劃

## 項目概述
Vue3 + TypeScript + SpringBoot + Redis 用戶認證系統
- 登入/註冊功能
- 郵件驗證（5分鐘最多2次，一天最多5次）
- 身份認證（上傳身份證正反面，人工審核）
- 用戶信息管理

## 技術棧
- 前端：Vue3 + TypeScript + Vite + Vue Router + Element Plus + Pinia
- 後端：SpringBoot 3.2.12 + JPA + MySQL + Redis + Spring Security + JWT
- 郵件：Spring Boot Starter Mail

## 實施步驟

### 後端開發
1. **依賴配置與基礎設置**
   - 添加 Redis、Mail、文件上傳依賴
   - 配置 application.yml
   - 創建項目結構

2. **數據庫設計與實體類**
   - User 實體
   - EmailVerification 實體
   - IdentityVerification 實體
   - Repository 接口

3. **核心業務服務**
   - UserService
   - EmailService
   - IdentityVerificationService
   - RedisService

4. **API 控制器**
   - AuthController
   - UserController
   - IdentityController
   - FileController

5. **安全配置**
   - Spring Security
   - JWT Token
   - 跨域配置

### 前端開發
6. **前端基礎設置**
   - 安裝依賴
   - 配置路由
   - API 攔截器

7. **頁面組件**
   - 登入頁面
   - 註冊頁面
   - 首頁
   - 身份認證頁面
   - 用戶信息頁面

8. **狀態管理**
   - Pinia 配置
   - 用戶狀態管理

### 測試與集成
9. **功能測試**
10. **系統集成**
