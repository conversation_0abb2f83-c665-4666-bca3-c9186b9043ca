# 产品概述

这是一个使用 Vue3 + TypeScript 前端和 SpringBoot 后端构建的全面用户认证系统。该系统提供了完整的用户注册、登录、电子邮件验证和身份验证功能。

## 核心功能

- **用户认证**：基于 JWT 令牌的身份验证注册/登录
- **电子邮件验证**：受速率限制的电子邮件验证（每 5 分钟最多 2 次，每天最多 5 次）
- **身份验证**：上传身份证照片供管理员人工审核
- **管理面板**：用于审核和批准身份验证的管理员界面
- **社交功能**：用户关注系统，包括关注/取消关注、关注者列表和用户推荐
- **文件上传**：具有 10MB 限制的图片上传功能

## 目标用户

- 需要安全认证和身份验证的终端用户
- 管理用户验证流程的管理员
- 以此认证基础进行开发的开发人员

## 业务逻辑

- 注册时电子邮件域名仅限于 @d1y.me
- 多阶段验证流程（电子邮件 → 身份证明文件 → 管理员审批）
- 基于 Redis 的缓存以提升性能和限制速率
- 基于 JWT 的无状态身份验证