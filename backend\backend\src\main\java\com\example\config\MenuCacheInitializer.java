package com.example.config;

import com.example.service.MenuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 菜單緩存初始化器
 * 在應用啟動時預熱菜單緩存
 */
@Component
@Slf4j
public class MenuCacheInitializer implements ApplicationRunner {
    
    @Autowired
    private MenuService menuService;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            log.info("開始初始化菜單緩存...");
            
            // 預熱菜單緩存
            menuService.warmUpMenuCache();
            
            log.info("菜單緩存初始化完成");
            
        } catch (Exception e) {
            log.error("菜單緩存初始化失敗", e);
            // 不拋出異常，避免影響應用啟動
        }
    }
}
