package com.example.dto;

import com.example.entity.Favorite;
import com.example.entity.FavoriteItem;
import com.example.enums.ItemType;

import java.time.LocalDateTime;

/**
 * 收藏数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public class FavoriteDto {
    
    private Long id;
    private Long userId;
    private Long itemId;
    private String itemTitle;
    private String itemDescription;
    private ItemType itemType;
    private String contentUrl;
    private String thumbnailUrl;
    private LocalDateTime createdAt;
    
    // 构造函数
    public FavoriteDto() {
    }
    
    /**
     * 从实体类转换为DTO
     * 
     * @param favorite 收藏实体
     * @param item 内容实体
     */
    public FavoriteDto(Favorite favorite, FavoriteItem item) {
        this.id = favorite.getId();
        this.userId = favorite.getUserId();
        this.itemId = favorite.getItemId();
        this.itemType = favorite.getItemType();
        this.createdAt = favorite.getCreatedAt();
        
        if (item != null) {
            this.itemTitle = item.getTitle();
            this.itemDescription = item.getDescription();
            this.contentUrl = item.getContentUrl();
            this.thumbnailUrl = item.getThumbnailUrl();
        }
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Long getItemId() {
        return itemId;
    }
    
    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }
    
    public String getItemTitle() {
        return itemTitle;
    }
    
    public void setItemTitle(String itemTitle) {
        this.itemTitle = itemTitle;
    }
    
    public String getItemDescription() {
        return itemDescription;
    }
    
    public void setItemDescription(String itemDescription) {
        this.itemDescription = itemDescription;
    }
    
    public ItemType getItemType() {
        return itemType;
    }
    
    public void setItemType(ItemType itemType) {
        this.itemType = itemType;
    }
    
    public String getContentUrl() {
        return contentUrl;
    }
    
    public void setContentUrl(String contentUrl) {
        this.contentUrl = contentUrl;
    }
    
    public String getThumbnailUrl() {
        return thumbnailUrl;
    }
    
    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    @Override
    public String toString() {
        return "FavoriteDto{" +
                "id=" + id +
                ", userId=" + userId +
                ", itemId=" + itemId +
                ", itemTitle='" + itemTitle + '\'' +
                ", itemType=" + itemType +
                ", createdAt=" + createdAt +
                '}';
    }
}
