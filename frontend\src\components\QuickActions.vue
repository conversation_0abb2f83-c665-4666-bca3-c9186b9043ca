<template>
  <div class="quick-actions">
    <!-- 快捷操作按鈕 -->
    <div class="action-buttons">
      <el-tooltip content="返回頂部" placement="left">
        <el-button 
          v-show="showBackToTop"
          type="primary" 
          :icon="ArrowUp"
          circle
          size="large"
          class="back-to-top"
          @click="scrollToTop"
        />
      </el-tooltip>

      <el-tooltip content="購物車" placement="left">
        <el-button 
          type="primary" 
          :icon="ShoppingCart"
          circle
          size="large"
          class="cart-button"
          @click="goToCart"
        >
          <el-badge :value="cartCount" :hidden="cartCount === 0" />
        </el-button>
      </el-tooltip>

      <el-tooltip content="我的收藏" placement="left">
        <el-button 
          type="primary" 
          :icon="Star"
          circle
          size="large"
          class="favorites-button"
          @click="goToFavorites"
        />
      </el-tooltip>

      <el-tooltip content="客服諮詢" placement="left">
        <el-button 
          type="primary" 
          :icon="Service"
          circle
          size="large"
          class="service-button"
          @click="openService"
        />
      </el-tooltip>
    </div>

    <!-- 快捷搜索 -->
    <transition name="search-slide">
      <div v-if="showQuickSearch" class="quick-search">
        <el-input
          v-model="searchKeyword"
          placeholder="快速搜索商品..."
          :prefix-icon="Search"
          size="large"
          @keyup.enter="performSearch"
          @blur="hideQuickSearch"
          ref="searchInputRef"
        >
          <template #append>
            <el-button :icon="Search" @click="performSearch" />
          </template>
        </el-input>
      </div>
    </transition>

    <!-- 搜索觸發按鈕 -->
    <el-tooltip content="快速搜索" placement="left">
      <el-button 
        type="primary" 
        :icon="Search"
        circle
        size="large"
        class="search-trigger"
        @click="toggleQuickSearch"
      />
    </el-tooltip>

    <!-- 快捷鍵提示 -->
    <div v-if="showKeyboardShortcuts" class="keyboard-shortcuts">
      <div class="shortcuts-header">
        <h4>快捷鍵</h4>
        <el-button link @click="hideKeyboardShortcuts">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      <div class="shortcuts-list">
        <div class="shortcut-item">
          <kbd>Ctrl</kbd> + <kbd>K</kbd> - 快速搜索
        </div>
        <div class="shortcut-item">
          <kbd>Ctrl</kbd> + <kbd>B</kbd> - 購物車
        </div>
        <div class="shortcut-item">
          <kbd>Ctrl</kbd> + <kbd>H</kbd> - 返回首頁
        </div>
        <div class="shortcut-item">
          <kbd>Esc</kbd> - 關閉彈窗
        </div>
      </div>
    </div>

    <!-- 快捷鍵觸發按鈕 -->
    <el-tooltip content="快捷鍵說明" placement="left">
      <el-button 
        type="primary" 
        :icon="Key"
        circle
        size="large"
        class="keyboard-button"
        @click="toggleKeyboardShortcuts"
      />
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowUp, ShoppingCart, Star, Service, Search,
  Close, Key
} from '@element-plus/icons-vue'

// Props
interface Props {
  cartCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  cartCount: 0
})

// 響應式數據
const router = useRouter()
const showBackToTop = ref(false)
const showQuickSearch = ref(false)
const showKeyboardShortcuts = ref(false)
const searchKeyword = ref('')
const searchInputRef = ref()

// 方法
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

const goToCart = () => {
  router.push('/app/cart')
}

const goToFavorites = () => {
  router.push('/app/my-favorites')
}

const openService = () => {
  ElMessage.info('客服功能開發中，敬請期待')
}

const toggleQuickSearch = async () => {
  showQuickSearch.value = !showQuickSearch.value
  if (showQuickSearch.value) {
    await nextTick()
    searchInputRef.value?.focus()
  }
}

const hideQuickSearch = () => {
  setTimeout(() => {
    showQuickSearch.value = false
  }, 200)
}

const performSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push(`/app/products/search?search=${encodeURIComponent(searchKeyword.value)}`)
    showQuickSearch.value = false
    searchKeyword.value = ''
  }
}

const toggleKeyboardShortcuts = () => {
  showKeyboardShortcuts.value = !showKeyboardShortcuts.value
}

const hideKeyboardShortcuts = () => {
  showKeyboardShortcuts.value = false
}

// 滾動監聽
const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

// 鍵盤快捷鍵
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl + K: 快速搜索
  if (event.ctrlKey && event.key === 'k') {
    event.preventDefault()
    toggleQuickSearch()
  }
  
  // Ctrl + B: 購物車
  if (event.ctrlKey && event.key === 'b') {
    event.preventDefault()
    goToCart()
  }
  
  // Ctrl + H: 返回首頁
  if (event.ctrlKey && event.key === 'h') {
    event.preventDefault()
    router.push('/')
  }
  
  // Esc: 關閉彈窗
  if (event.key === 'Escape') {
    showQuickSearch.value = false
    showKeyboardShortcuts.value = false
  }
}

// 生命週期
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  window.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
  window.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.quick-actions {
  position: fixed;
  right: 24px;
  bottom: 24px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.back-to-top,
.cart-button,
.favorites-button,
.service-button,
.search-trigger,
.keyboard-button {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  font-size: 20px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.back-to-top:hover,
.cart-button:hover,
.favorites-button:hover,
.service-button:hover,
.search-trigger:hover,
.keyboard-button:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.back-to-top:active,
.cart-button:active,
.favorites-button:active,
.service-button:active,
.search-trigger:active,
.keyboard-button:active {
  transform: translateY(-2px) scale(1.02);
}

.cart-button {
  position: relative;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  box-shadow: 0 4px 20px rgba(255, 107, 107, 0.3);
}

.cart-button:hover {
  background: linear-gradient(135deg, #ff5252 0%, #d63031 100%);
  box-shadow: 0 8px 30px rgba(255, 107, 107, 0.4);
}

.cart-button .el-badge {
  position: absolute;
  top: -8px;
  right: -8px;
}

.cart-button .el-badge :deep(.el-badge__content) {
  background: #ff4757;
  border: 2px solid white;
  font-size: 12px;
  font-weight: 600;
  min-width: 20px;
  height: 20px;
  line-height: 16px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 按鈕進入動畫 */
.back-to-top,
.cart-button,
.favorites-button,
.service-button,
.search-trigger,
.keyboard-button {
  animation: buttonSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
}

.back-to-top { animation-delay: 0.1s; }
.cart-button { animation-delay: 0.2s; }
.favorites-button { animation-delay: 0.3s; }
.service-button { animation-delay: 0.4s; }
.search-trigger { animation-delay: 0.5s; }
.keyboard-button { animation-delay: 0.6s; }

@keyframes buttonSlideIn {
  from {
    opacity: 0;
    transform: translateX(20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.quick-search {
  position: absolute;
  right: 0;
  bottom: 80px;
  width: 320px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px;
}

.keyboard-shortcuts {
  position: absolute;
  right: 0;
  bottom: 80px;
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px;
}

.shortcuts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.shortcuts-header h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.shortcut-item {
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
}

kbd {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 11px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 600;
  color: #495057;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 動畫 */
.search-slide-enter-active,
.search-slide-leave-active {
  transition: all 0.3s ease;
}

.search-slide-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.search-slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .quick-actions {
    right: 16px;
    bottom: 16px;
    gap: 12px;
  }

  .action-buttons {
    padding: 12px;
    gap: 12px;
  }

  .back-to-top,
  .cart-button,
  .favorites-button,
  .service-button,
  .search-trigger,
  .keyboard-button {
    width: 48px;
    height: 48px;
    font-size: 18px;
    border-radius: 14px;
  }

  .quick-search {
    width: 280px;
    padding: 16px;
    bottom: 70px;
  }

  .keyboard-shortcuts {
    width: 240px;
    padding: 16px;
    bottom: 70px;
  }
}

@media (max-width: 480px) {
  .quick-actions {
    right: 12px;
    bottom: 12px;
  }

  .action-buttons {
    padding: 10px;
    gap: 10px;
  }

  .back-to-top,
  .cart-button,
  .favorites-button,
  .service-button,
  .search-trigger,
  .keyboard-button {
    width: 44px;
    height: 44px;
    font-size: 16px;
    border-radius: 12px;
  }

  .quick-search {
    width: calc(100vw - 40px);
    max-width: 280px;
    right: -12px;
  }

  .keyboard-shortcuts {
    width: calc(100vw - 40px);
    max-width: 240px;
    right: -12px;
  }
}
</style>
