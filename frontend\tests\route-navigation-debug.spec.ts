import { test, expect } from '@playwright/test';

test.describe('路由導航調試測試', () => {
  test.beforeEach(async ({ page }) => {
    // 監聽控制台日誌
    page.on('console', msg => {
      if (msg.type() === 'log' || msg.type() === 'error') {
        console.log(`[${msg.type().toUpperCase()}] ${msg.text()}`);
      }
    });

    // 監聽頁面錯誤
    page.on('pageerror', error => {
      console.error('頁面錯誤:', error.message);
    });

    // 導航到登錄頁面並登錄
    await page.goto('http://localhost:5173/login');
    await page.waitForLoadState('networkidle');
    
    // 填寫登錄信息
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', '12345');
    await page.click('button[type="submit"]');
    
    // 等待登錄成功並跳轉到主頁
    await page.waitForURL('**/app/home');
    await page.waitForLoadState('networkidle');
  });

  test('測試商品搜索頁面導航', async ({ page }) => {
    console.log('=== 開始測試商品搜索頁面導航 ===');
    
    // 等待菜單加載
    await page.waitForSelector('.menu-tree', { timeout: 10000 });
    
    // 點擊商品搜索菜單項
    console.log('點擊商品搜索菜單項...');
    await page.click('text=商品搜索');
    
    // 等待路由跳轉完成
    await page.waitForURL('**/app/products/search', { timeout: 5000 });
    console.log('路由跳轉完成，當前URL:', page.url());
    
    // 檢查頁面是否正確加載
    await expect(page.locator('h1')).toContainText('商品搜索');
    
    // 等待商品列表加載
    await page.waitForSelector('.product-grid', { timeout: 10000 });
    
    // 檢查是否停留在正確的頁面（不會跳轉回首頁）
    await page.waitForTimeout(2000); // 等待2秒確保沒有自動跳轉
    expect(page.url()).toContain('/app/products/search');
    
    console.log('✅ 商品搜索頁面導航測試通過');
  });

  test('測試熱門商品頁面導航', async ({ page }) => {
    console.log('=== 開始測試熱門商品頁面導航 ===');
    
    // 等待菜單加載
    await page.waitForSelector('.menu-tree', { timeout: 10000 });
    
    // 點擊熱門商品菜單項
    console.log('點擊熱門商品菜單項...');
    await page.click('text=熱門商品');
    
    // 等待路由跳轉完成
    await page.waitForURL('**/app/products/hot', { timeout: 5000 });
    console.log('路由跳轉完成，當前URL:', page.url());
    
    // 檢查頁面是否正確加載
    await expect(page.locator('h1')).toContainText('熱門商品');
    
    // 等待商品列表加載
    await page.waitForSelector('.product-grid', { timeout: 10000 });
    
    // 檢查是否停留在正確的頁面（不會跳轉回首頁）
    await page.waitForTimeout(2000); // 等待2秒確保沒有自動跳轉
    expect(page.url()).toContain('/app/products/hot');
    
    console.log('✅ 熱門商品頁面導航測試通過');
  });

  test('測試商品詳情頁面導航', async ({ page }) => {
    console.log('=== 開始測試商品詳情頁面導航 ===');
    
    // 先導航到商品列表頁面
    await page.goto('http://localhost:5173/app/products/search');
    await page.waitForLoadState('networkidle');
    
    // 等待商品列表加載
    await page.waitForSelector('.product-grid', { timeout: 10000 });
    
    // 檢查是否有商品卡片
    const productCards = await page.locator('.product-card').count();
    if (productCards > 0) {
      console.log(`找到 ${productCards} 個商品，點擊第一個商品...`);
      
      // 點擊第一個商品卡片
      await page.click('.product-card:first-child');
      
      // 等待跳轉到商品詳情頁
      await page.waitForURL('**/app/products/*', { timeout: 5000 });
      console.log('跳轉到商品詳情頁，當前URL:', page.url());
      
      // 檢查是否成功加載商品詳情頁面
      await page.waitForSelector('.product-detail', { timeout: 10000 });
      
      // 檢查是否停留在正確的頁面
      await page.waitForTimeout(2000);
      expect(page.url()).toMatch(/\/app\/products\/\d+/);
      
      console.log('✅ 商品詳情頁面導航測試通過');
    } else {
      console.log('⚠️ 沒有找到商品，跳過商品詳情測試');
    }
  });

  test('測試推薦商品頁面導航', async ({ page }) => {
    console.log('=== 開始測試推薦商品頁面導航 ===');
    
    // 等待菜單加載
    await page.waitForSelector('.menu-tree', { timeout: 10000 });
    
    // 點擊推薦商品菜單項
    console.log('點擊推薦商品菜單項...');
    await page.click('text=推薦商品');
    
    // 等待路由跳轉完成
    await page.waitForURL('**/app/products/recommended', { timeout: 5000 });
    console.log('路由跳轉完成，當前URL:', page.url());
    
    // 檢查頁面是否正確加載
    await expect(page.locator('h1')).toContainText('推薦商品');
    
    // 等待商品列表加載
    await page.waitForSelector('.product-grid', { timeout: 10000 });
    
    // 檢查是否停留在正確的頁面（不會跳轉回首頁）
    await page.waitForTimeout(2000); // 等待2秒確保沒有自動跳轉
    expect(page.url()).toContain('/app/products/recommended');
    
    console.log('✅ 推薦商品頁面導航測試通過');
  });
});
