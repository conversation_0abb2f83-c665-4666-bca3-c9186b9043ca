/**
 * 错误处理工具
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */

// 错误类型定义
export interface ErrorInfo {
  code: string
  message: string
  details?: any
}

// 错误码映射
const ERROR_MESSAGES: Record<string, string> = {
  // 收藏相关错误
  'FAVORITE_NOT_FOUND': '收藏记录不存在',
  'DUPLICATE_FAVORITE': '已经收藏过该内容',
  'RATE_LIMIT_EXCEEDED': '操作过于频繁，请稍后再试',
  'UNAUTHORIZED_FAVORITE': '无权限进行此操作',
  'ITEM_NOT_FOUND': '内容不存在',
  'FAVORITE_ERROR': '收藏操作失败',
  
  // 网络相关错误
  'NETWORK_ERROR': '网络连接失败，请检查网络设置',
  'TIMEOUT_ERROR': '请求超时，请稍后重试',
  'SERVER_ERROR': '服务器内部错误，请稍后重试',
  
  // 认证相关错误
  'UNAUTHORIZED': '请先登录',
  'FORBIDDEN': '权限不足',
  'TOKEN_EXPIRED': '登录已过期，请重新登录',
  
  // 通用错误
  'UNKNOWN_ERROR': '未知错误，请稍后重试',
  'VALIDATION_ERROR': '输入数据不合法'
}

/**
 * 解析错误信息
 */
export function parseError(error: any): ErrorInfo {
  // 如果是字符串错误
  if (typeof error === 'string') {
    return {
      code: 'UNKNOWN_ERROR',
      message: error
    }
  }
  
  // 如果是API响应错误
  if (error?.response?.data) {
    const errorData = error.response.data
    
    // 检查是否有错误码
    if (errorData.data?.errorCode) {
      const errorCode = errorData.data.errorCode
      return {
        code: errorCode,
        message: ERROR_MESSAGES[errorCode] || errorData.message || '操作失败',
        details: errorData.data
      }
    }
    
    // 检查HTTP状态码
    const status = error.response.status
    switch (status) {
      case 401:
        return {
          code: 'UNAUTHORIZED',
          message: ERROR_MESSAGES['UNAUTHORIZED']
        }
      case 403:
        return {
          code: 'FORBIDDEN',
          message: ERROR_MESSAGES['FORBIDDEN']
        }
      case 404:
        return {
          code: 'NOT_FOUND',
          message: errorData.message || '资源不存在'
        }
      case 409:
        return {
          code: 'CONFLICT',
          message: errorData.message || '数据冲突'
        }
      case 429:
        return {
          code: 'RATE_LIMIT_EXCEEDED',
          message: ERROR_MESSAGES['RATE_LIMIT_EXCEEDED']
        }
      case 500:
        return {
          code: 'SERVER_ERROR',
          message: ERROR_MESSAGES['SERVER_ERROR']
        }
      default:
        return {
          code: 'UNKNOWN_ERROR',
          message: errorData.message || ERROR_MESSAGES['UNKNOWN_ERROR']
        }
    }
  }
  
  // 网络错误
  if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('Network Error')) {
    return {
      code: 'NETWORK_ERROR',
      message: ERROR_MESSAGES['NETWORK_ERROR']
    }
  }
  
  // 超时错误
  if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
    return {
      code: 'TIMEOUT_ERROR',
      message: ERROR_MESSAGES['TIMEOUT_ERROR']
    }
  }
  
  // 默认错误
  return {
    code: 'UNKNOWN_ERROR',
    message: error?.message || ERROR_MESSAGES['UNKNOWN_ERROR']
  }
}

/**
 * 获取用户友好的错误消息
 */
export function getFriendlyErrorMessage(error: any): string {
  const errorInfo = parseError(error)
  return errorInfo.message
}

/**
 * 检查是否为特定类型的错误
 */
export function isErrorType(error: any, errorCode: string): boolean {
  const errorInfo = parseError(error)
  return errorInfo.code === errorCode
}

/**
 * 检查是否为网络相关错误
 */
export function isNetworkError(error: any): boolean {
  return isErrorType(error, 'NETWORK_ERROR') || isErrorType(error, 'TIMEOUT_ERROR')
}

/**
 * 检查是否为认证相关错误
 */
export function isAuthError(error: any): boolean {
  return isErrorType(error, 'UNAUTHORIZED') || 
         isErrorType(error, 'FORBIDDEN') || 
         isErrorType(error, 'TOKEN_EXPIRED')
}

/**
 * 检查是否为收藏相关错误
 */
export function isFavoriteError(error: any): boolean {
  const errorInfo = parseError(error)
  return errorInfo.code.startsWith('FAVORITE_') || 
         ['DUPLICATE_FAVORITE', 'RATE_LIMIT_EXCEEDED', 'ITEM_NOT_FOUND'].includes(errorInfo.code)
}

/**
 * 错误重试判断
 */
export function shouldRetry(error: any): boolean {
  const errorInfo = parseError(error)
  
  // 这些错误可以重试
  const retryableErrors = [
    'NETWORK_ERROR',
    'TIMEOUT_ERROR',
    'SERVER_ERROR'
  ]
  
  return retryableErrors.includes(errorInfo.code)
}

/**
 * 获取重试延迟时间（毫秒）
 */
export function getRetryDelay(attemptCount: number): number {
  // 指数退避算法：1s, 2s, 4s, 8s, 16s
  return Math.min(1000 * Math.pow(2, attemptCount - 1), 16000)
}
