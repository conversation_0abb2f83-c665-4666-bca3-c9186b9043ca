{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:product": "playwright test product-management.spec.ts", "test:api": "playwright test product-api.spec.ts", "test:sorting": "playwright test tests/product-sorting-filtering.spec.ts", "test:sorting:headed": "playwright test tests/product-sorting-filtering.spec.ts --headed", "test:sorting:debug": "playwright test tests/product-sorting-filtering.spec.ts --debug", "test:combination": "playwright test tests/product-combination-filter.spec.ts", "test:combination:headed": "playwright test tests/product-combination-filter.spec.ts --headed", "test:combination:debug": "playwright test tests/product-combination-filter.spec.ts --debug"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "element-plus": "^2.4.4", "pinia": "^2.1.7", "vue": "^3.5.17", "vue-router": "^4.2.5"}, "devDependencies": {"@playwright/test": "^1.54.1", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.3", "vue-tsc": "^2.2.12"}}