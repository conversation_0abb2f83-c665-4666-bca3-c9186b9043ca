package com.example.repository;

import com.example.entity.Cart;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 購物車Repository
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Repository
public interface CartRepository extends JpaRepository<Cart, Long> {
    
    /**
     * 根據用戶ID查找活躍的購物車
     */
    Optional<Cart> findByUserIdAndStatus(Long userId, Integer status);
    
    /**
     * 根據用戶ID查找活躍的購物車（預加載購物車項目）
     */
    @Query("SELECT c FROM Cart c LEFT JOIN FETCH c.cartItems WHERE c.userId = :userId AND c.status = :status")
    Optional<Cart> findByUserIdAndStatusWithItems(@Param("userId") Long userId, @Param("status") Integer status);
    
    /**
     * 根據用戶ID查找所有購物車
     */
    @Query("SELECT c FROM Cart c WHERE c.userId = :userId ORDER BY c.updatedAt DESC")
    java.util.List<Cart> findByUserIdOrderByUpdatedAtDesc(@Param("userId") Long userId);
    
    /**
     * 檢查用戶是否有活躍的購物車
     */
    boolean existsByUserIdAndStatus(Long userId, Integer status);
    
    /**
     * 統計用戶的購物車數量
     */
    long countByUserId(Long userId);
    
    /**
     * 刪除用戶的所有購物車
     */
    void deleteByUserId(Long userId);
}
