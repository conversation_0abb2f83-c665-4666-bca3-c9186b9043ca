// 清除菜單緩存腳本
const redis = require('redis');

async function clearMenuCache() {
  const client = redis.createClient({
    host: 'localhost',
    port: 6379
  });

  try {
    await client.connect();
    console.log('已連接到Redis');

    // 清除所有菜單相關緩存
    const keys = await client.keys('menu:*');
    console.log('找到菜單緩存鍵:', keys);

    if (keys.length > 0) {
      await client.del(keys);
      console.log(`已清除 ${keys.length} 個菜單緩存鍵`);
    } else {
      console.log('沒有找到菜單緩存鍵');
    }

    // 特別清除用戶菜單緩存
    const userMenuKeys = await client.keys('menu:user:*');
    if (userMenuKeys.length > 0) {
      await client.del(userMenuKeys);
      console.log(`已清除 ${userMenuKeys.length} 個用戶菜單緩存鍵`);
    }

    console.log('菜單緩存清除完成');
  } catch (error) {
    console.error('清除菜單緩存失敗:', error);
  } finally {
    await client.quit();
  }
}

clearMenuCache();
