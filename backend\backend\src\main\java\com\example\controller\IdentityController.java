package com.example.controller;

import com.example.dto.ApiResponse;
import com.example.dto.IdentityVerificationRequest;
import com.example.entity.IdentityVerification;
import com.example.entity.User;
import com.example.service.IdentityVerificationService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/identity")
@CrossOrigin(origins = "*")
@Slf4j
public class IdentityController {
    
    @Autowired
    private IdentityVerificationService identityVerificationService;
    
    /**
     * 提交身份認證
     */
    @PostMapping("/submit")
    public ResponseEntity<ApiResponse<Map<String, Object>>> submitIdentityVerification(
            @Valid @ModelAttribute IdentityVerificationRequest request,
            @RequestParam(value = "frontImage", required = false) MultipartFile frontImage,
            @RequestParam(value = "backImage", required = false) MultipartFile backImage,
            @RequestParam(value = "useExistingFrontImage", required = false, defaultValue = "false") boolean useExistingFrontImage,
            @RequestParam(value = "useExistingBackImage", required = false, defaultValue = "false") boolean useExistingBackImage,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            
            IdentityVerification verification = identityVerificationService.submitIdentityVerification(
                user, request, frontImage, backImage, useExistingFrontImage, useExistingBackImage
            );
            
            Map<String, Object> data = new HashMap<>();
            data.put("id", verification.getId());
            data.put("status", verification.getStatus());
            data.put("submittedAt", verification.getSubmittedAt());
            
            return ResponseEntity.ok(ApiResponse.success("身份認證申請提交成功", data));
            
        } catch (Exception e) {
            log.error("身份認證申請提交失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 獲取用戶的身份認證記錄
     */
    @GetMapping("/my-verifications")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getMyVerifications(Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            List<IdentityVerification> verifications = identityVerificationService.getUserVerifications(user);
            
            List<Map<String, Object>> data = verifications.stream()
                .map(this::convertToMap)
                .toList();
            
            return ResponseEntity.ok(ApiResponse.success(data));
            
        } catch (Exception e) {
            log.error("獲取身份認證記錄失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("獲取認證記錄失敗"));
        }
    }
    
    /**
     * 獲取待審核的身份認證列表（管理員）
     */
    @GetMapping("/pending")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getPendingVerifications() {
        try {
            List<IdentityVerification> verifications = identityVerificationService.getPendingVerifications();
            
            List<Map<String, Object>> data = verifications.stream()
                .map(this::convertToMapWithUserInfo)
                .toList();
            
            return ResponseEntity.ok(ApiResponse.success(data));
            
        } catch (Exception e) {
            log.error("獲取待審核列表失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("獲取待審核列表失敗"));
        }
    }
    
    /**
     * 審核身份認證（管理員）
     */
    @PostMapping("/review/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> reviewVerification(
            @PathVariable Long id,
            @RequestParam boolean approved,
            @RequestParam(required = false) String comment,
            Authentication authentication) {
        try {
            User reviewer = (User) authentication.getPrincipal();
            
            identityVerificationService.reviewIdentityVerification(
                id, approved, comment, reviewer.getUsername()
            );
            
            String message = approved ? "身份認證審核通過" : "身份認證審核拒絕";
            return ResponseEntity.ok(ApiResponse.success(message));
            
        } catch (Exception e) {
            log.error("身份認證審核失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 獲取身份認證詳情（管理員）
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getVerificationDetail(@PathVariable Long id) {
        try {
            IdentityVerification verification = identityVerificationService.getVerificationById(id)
                .orElseThrow(() -> new RuntimeException("身份認證記錄不存在"));
            
            Map<String, Object> data = convertToMapWithUserInfo(verification);
            
            return ResponseEntity.ok(ApiResponse.success(data));
            
        } catch (Exception e) {
            log.error("獲取身份認證詳情失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 轉換為Map（不包含用戶信息）
     */
    private Map<String, Object> convertToMap(IdentityVerification verification) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", verification.getId());
        map.put("realName", verification.getRealName());
        map.put("idCardNumber", maskIdCardNumber(verification.getIdCardNumber()));
        map.put("status", verification.getStatus());
        map.put("submittedAt", verification.getSubmittedAt());
        map.put("reviewedAt", verification.getReviewedAt());
        map.put("reviewedBy", verification.getReviewedBy());
        map.put("reviewComment", verification.getReviewComment());
        return map;
    }
    
    /**
     * 轉換為Map（包含用戶信息）
     */
    private Map<String, Object> convertToMapWithUserInfo(IdentityVerification verification) {
        Map<String, Object> map = convertToMap(verification);
        map.put("userId", verification.getUser().getId());
        map.put("username", verification.getUser().getUsername());
        map.put("email", verification.getUser().getEmail());
        map.put("idCardFrontUrl", verification.getIdCardFrontUrl());
        map.put("idCardBackUrl", verification.getIdCardBackUrl());
        map.put("idCardNumber", verification.getIdCardNumber()); // 管理員可以看到完整身份證號
        return map;
    }
    
    /**
     * 遮罩身份證號碼
     */
    private String maskIdCardNumber(String idCardNumber) {
        if (idCardNumber == null || idCardNumber.length() < 8) {
            return idCardNumber;
        }
        return idCardNumber.substring(0, 4) + "****" + idCardNumber.substring(idCardNumber.length() - 4);
    }
}
