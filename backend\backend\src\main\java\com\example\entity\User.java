package com.example.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

@Entity
@Table(name = "users")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class User implements UserDetails {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false)
    private String username;
    
    @Column(unique = true, nullable = false)
    private String email;
    
    @Column(nullable = false)
    private String password;
    
    @Column(name = "real_name")
    private String realName;
    
    @Column(name = "phone_number")
    private String phoneNumber;
    
    @Column(name = "id_card_number")
    private String idCardNumber;
    
    @Column(name = "email_verified")
    private Boolean emailVerified = false;
    
    @Column(name = "identity_verified")
    private Boolean identityVerified = false;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "identity_status")
    private IdentityStatus identityStatus = IdentityStatus.NOT_SUBMITTED;
    
    @Column(name = "id_card_front_url")
    private String idCardFrontUrl;
    
    @Column(name = "id_card_back_url")
    private String idCardBackUrl;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "enabled")
    private Boolean enabled = true;
    
    @Enumerated(EnumType.STRING)
    private Role role = Role.USER;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // UserDetails 接口實現
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return List.of(new SimpleGrantedAuthority("ROLE_" + role.name()));
    }
    
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }
    
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }
    
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }
    
    @Override
    public boolean isEnabled() {
        return enabled;
    }
    
    public enum Role {
        USER
    }
    
    public enum IdentityStatus {
        NOT_SUBMITTED,    // 未提交
        PENDING,          // 待審核
        APPROVED,         // 已通過
        REJECTED          // 已拒絕
    }
}
