# 購物車和支付寶支付功能啟動指南

## 🚀 快速啟動

### 1. 環境檢查

確保以下服務正在運行：
- **MySQL**: 端口 3306
- **Redis**: 端口 6379  
- **Java**: JDK 17+
- **Node.js**: 16+

### 2. 數據庫準備

```bash
# 連接到 MySQL
mysql -u root -p

# 使用項目數據庫
USE java_springboot_redis_mail_login_test_250708;

# 運行數據庫測試腳本（可選）
source test-database.sql;
```

### 3. 啟動後端服務

```bash
# 進入後端目錄
cd backend/backend

# 啟動 Spring Boot 應用
mvn spring-boot:run

# 或者使用 IDE 運行 Application.java
```

後端服務將在 **http://localhost:8080** 啟動

### 4. 啟動前端服務

```bash
# 進入前端目錄
cd frontend

# 安裝依賴（如果還沒安裝）
npm install

# 啟動開發服務器
npm run dev
```

前端服務將在 **http://localhost:3000** 啟動

### 5. 配置 ngrok（支付寶回調）

```bash
# 安裝 ngrok（如果還沒安裝）
# 下載地址: https://ngrok.com/download

# 啟動 ngrok 隧道
ngrok http 8080

# 複製生成的公網地址，例如: https://abc123.ngrok.io
```

更新 `backend/backend/src/main/resources/application.yml` 中的回調地址：
```yaml
alipay:
  config:
    notify-url: https://your-ngrok-url.ngrok.io/api/payment/alipay/callback
```

## 🧪 功能測試

### 手動測試流程

1. **用戶登錄**
   - 訪問 http://localhost:3000
   - 使用測試賬號登錄

2. **購物車測試**
   - 瀏覽商品頁面
   - 添加商品到購物車
   - 進入購物車頁面測試功能

3. **訂單測試**
   - 在購物車中選擇商品
   - 點擊"去結算"
   - 填寫收貨信息
   - 提交訂單

4. **支付測試**
   - 在支付頁面點擊"立即支付"
   - 使用支付寶沙箱賬號完成支付
   - 驗證支付狀態更新

### 自動化測試

```bash
# API 測試
node test-cart-api.js

# 前端 E2E 測試
cd frontend
npx playwright test tests/cart-payment.spec.ts

# 數據庫測試
mysql -u root -p java_springboot_redis_mail_login_test_250708 < test-database.sql
```

## 🔧 API 端點

### 購物車 API
- `GET /api/cart` - 獲取購物車
- `POST /api/cart/add` - 添加商品到購物車
- `PUT /api/cart/update/{cartItemId}` - 更新商品數量
- `DELETE /api/cart/remove/{cartItemId}` - 移除商品
- `DELETE /api/cart/clear` - 清空購物車
- `PUT /api/cart/toggle-selected/{cartItemId}` - 切換選中狀態
- `GET /api/cart/selected` - 獲取選中項目

### 訂單 API
- `POST /api/orders/create-from-cart` - 從購物車創建訂單
- `POST /api/orders/create-direct` - 直接購買
- `GET /api/orders` - 獲取訂單列表
- `GET /api/orders/{orderId}` - 獲取訂單詳情
- `PUT /api/orders/{orderId}/cancel` - 取消訂單

### 支付 API
- `POST /api/payment/alipay/create` - 發起支付寶支付
- `POST /api/payment/alipay/callback` - 支付寶異步回調
- `GET /api/payment/alipay/return` - 支付寶同步回調
- `GET /api/payment/status/{orderId}` - 查詢支付狀態
- `POST /api/payment/admin/confirm` - 管理員確認支付（測試用）

## 🎯 支付寶沙箱測試

### 測試賬號信息
- **買家賬號**: <EMAIL>
- **登錄密碼**: 111111
- **支付密碼**: 111111

### 測試流程
1. 創建訂單後進入支付頁面
2. 點擊"立即支付"跳轉到支付寶沙箱
3. 使用測試賬號登錄
4. 輸入支付密碼完成支付
5. 返回系統查看支付狀態

## 🐛 常見問題

### 1. 後端啟動失敗
- 檢查 MySQL 和 Redis 服務是否啟動
- 檢查數據庫連接配置
- 檢查端口 8080 是否被占用

### 2. 前端無法訪問後端 API
- 檢查跨域配置
- 檢查 API 基礎地址配置
- 檢查網絡連接

### 3. 支付寶回調失敗
- 檢查 ngrok 是否正常運行
- 檢查回調地址配置是否正確
- 檢查支付寶沙箱配置

### 4. 購物車數據不同步
- 檢查 Redis 服務狀態
- 檢查用戶認證狀態
- 清除瀏覽器緩存

## 📊 監控和日誌

### 後端日誌
```bash
# 查看應用日誌
tail -f logs/application.log

# 查看支付相關日誌
grep "payment\|alipay" logs/application.log
```

### 數據庫監控
```sql
-- 查看購物車統計
SELECT COUNT(*) as cart_count FROM carts WHERE status = 1;

-- 查看訂單統計
SELECT status, COUNT(*) as order_count FROM orders GROUP BY status;

-- 查看支付統計
SELECT payment_status, COUNT(*) as payment_count FROM payments GROUP BY payment_status;
```

### Redis 監控
```bash
# 連接 Redis
redis-cli

# 查看所有鍵
KEYS *

# 查看購物車相關數據
KEYS cart:*
```

## 🎉 功能特點

✅ **完整的購物車系統**
- 商品添加、刪除、數量調整
- 商品選中狀態管理
- 購物車統計和計算

✅ **完整的訂單流程**
- 從購物車創建訂單
- 直接購買功能
- 訂單狀態管理

✅ **支付寶沙箱集成**
- 支付寶頁面跳轉支付
- 異步回調處理
- 支付狀態實時更新

✅ **響應式前端界面**
- Vue3 + TypeScript
- Element Plus UI 組件
- 移動端適配

✅ **完整的後端架構**
- Spring Boot + JPA
- Redis 緩存
- MySQL 數據持久化

## 📞 技術支持

如果在使用過程中遇到問題，請檢查：
1. 服務啟動狀態
2. 數據庫連接
3. 網絡配置
4. 日誌輸出

祝您測試愉快！🎊
