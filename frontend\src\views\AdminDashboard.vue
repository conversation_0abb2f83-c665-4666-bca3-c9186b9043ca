<template>
  <div class="admin-dashboard">
    <!-- 頂部導航 -->
    <el-header class="header">
      <div class="header-content">
        <h1>管理員後台</h1>
        <div class="header-actions">
          <span class="admin-info">歡迎，{{ adminStore.admin?.realName || adminStore.admin?.username }}</span>
          <el-button @click="handleLogout" type="danger" plain>登出</el-button>
        </div>
      </div>
    </el-header>

    <el-container class="main-container">
      <!-- 側邊欄 -->
      <el-aside width="250px" class="sidebar">
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          @select="handleMenuSelect"
        >
          <el-menu-item index="dashboard">
            <el-icon><Odometer /></el-icon>
            <span>儀表板</span>
          </el-menu-item>
          <el-menu-item index="identity-review">
            <el-icon><UserFilled /></el-icon>
            <span>身份認證審核</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主內容區 -->
      <el-main class="content">
        <!-- 儀表板視圖 -->
        <div v-if="activeMenu === 'dashboard'" class="dashboard-view">
          <h2>系統概覽</h2>
          
          <!-- 統計卡片 -->
          <div class="stats-cards">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-item">
                    <div class="stat-icon pending">
                      <el-icon><Clock /></el-icon>
                    </div>
                    <div class="stat-content">
                      <h3>{{ pendingCount }}</h3>
                      <p>待審核</p>
                    </div>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-item">
                    <div class="stat-icon approved">
                      <el-icon><Check /></el-icon>
                    </div>
                    <div class="stat-content">
                      <h3>{{ approvedCount }}</h3>
                      <p>已通過</p>
                    </div>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-item">
                    <div class="stat-icon rejected">
                      <el-icon><Close /></el-icon>
                    </div>
                    <div class="stat-content">
                      <h3>{{ rejectedCount }}</h3>
                      <p>已拒絕</p>
                    </div>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-item">
                    <div class="stat-icon total">
                      <el-icon><DataAnalysis /></el-icon>
                    </div>
                    <div class="stat-content">
                      <h3>{{ totalCount }}</h3>
                      <p>總計</p>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 身份認證審核視圖 -->
        <div v-if="activeMenu === 'identity-review'" class="identity-review-view">
          <h2>身份認證審核</h2>
          
          <!-- 待審核列表 -->
          <el-card>
            <template #header>
              <div class="card-header">
                <span>待審核申請</span>
                <el-button @click="loadPendingVerifications" :loading="loading">刷新</el-button>
              </div>
            </template>
            
            <el-table :data="pendingVerifications" v-loading="loading">
              <el-table-column prop="username" label="用戶名" width="120" />
              <el-table-column prop="email" label="郵箱" width="200" />
              <el-table-column prop="realName" label="真實姓名" width="120" />
              <el-table-column prop="idCardNumber" label="身份證號" width="180" />
              <el-table-column prop="submittedAt" label="提交時間" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.submittedAt) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="{ row }">
                  <el-button @click="viewDetail(row)" type="primary" size="small">查看詳情</el-button>
                  <el-button @click="approveVerification(row.id)" type="success" size="small">通過</el-button>
                  <el-button @click="rejectVerification(row.id)" type="danger" size="small">拒絕</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-main>
    </el-container>

    <!-- 詳情對話框 -->
    <el-dialog v-model="detailDialogVisible" title="身份認證詳情" width="800px">
      <div v-if="currentDetail" class="detail-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-section">
              <h4>基本信息</h4>
              <p><strong>用戶名：</strong>{{ currentDetail.username }}</p>
              <p><strong>郵箱：</strong>{{ currentDetail.email }}</p>
              <p><strong>真實姓名：</strong>{{ currentDetail.realName }}</p>
              <p><strong>身份證號：</strong>{{ currentDetail.idCardNumber }}</p>
              <p><strong>提交時間：</strong>{{ formatDate(currentDetail.submittedAt) }}</p>
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="detail-section">
              <h4>身份證圖片</h4>
              <div class="id-card-images">
                <div class="id-card-item">
                  <p>正面：</p>
                  <img 
                    :src="`http://localhost:8080/api/files/${currentDetail.idCardFrontUrl}`" 
                    alt="身份證正面"
                    @click="previewImage(`http://localhost:8080/api/files/${currentDetail.idCardFrontUrl}`)"
                  />
                </div>
                <div class="id-card-item">
                  <p>反面：</p>
                  <img 
                    :src="`http://localhost:8080/api/files/${currentDetail.idCardBackUrl}`" 
                    alt="身份證反面"
                    @click="previewImage(`http://localhost:8080/api/files/${currentDetail.idCardBackUrl}`)"
                  />
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <div class="detail-section">
          <h4>審核操作</h4>
          <el-form :model="reviewForm" label-width="80px">
            <el-form-item label="審核意見">
              <el-input
                v-model="reviewForm.comment"
                type="textarea"
                :rows="3"
                placeholder="請輸入審核意見（可選）"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="closeDetailDialog">取消</el-button>
        <el-button @click="reviewVerification(currentDetail.id, false)" type="danger" :loading="reviewLoading">拒絕</el-button>
        <el-button @click="reviewVerification(currentDetail.id, true)" type="success" :loading="reviewLoading">通過</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Odometer, 
  UserFilled, 
  Clock, 
  Check, 
  Close, 
  DataAnalysis 
} from '@element-plus/icons-vue'
import { useAdminStore } from '../stores/admin'

const router = useRouter()
const adminStore = useAdminStore()

const activeMenu = ref('dashboard')
const loading = ref(false)
const reviewLoading = ref(false)
const detailDialogVisible = ref(false)

const pendingVerifications = ref<any[]>([])
const currentDetail = ref<any>(null)

const reviewForm = ref({
  comment: ''
})

// 統計數據
const pendingCount = computed(() => pendingVerifications.value.length)
const approvedCount = ref(0)
const rejectedCount = ref(0)
const totalCount = computed(() => pendingCount.value + approvedCount.value + rejectedCount.value)

onMounted(() => {
  // 確保從 localStorage 初始化管理員狀態
  adminStore.initializeFromStorage()

  if (!adminStore.isLoggedIn) {
    router.push('/admin-login')
    return
  }

  loadPendingVerifications()
})

const handleMenuSelect = (index: string) => {
  activeMenu.value = index
  if (index === 'identity-review') {
    loadPendingVerifications()
  }
}

const loadPendingVerifications = async () => {
  loading.value = true
  try {
    const response = await adminStore.identityAPI.getPendingVerifications()
    if (response.success) {
      pendingVerifications.value = response.data
    }
  } catch (error: any) {
    ElMessage.error(error.message || '加載待審核列表失敗')
  } finally {
    loading.value = false
  }
}

const viewDetail = async (row: any) => {
  try {
    const response = await adminStore.identityAPI.getVerificationDetail(row.id)
    if (response.success) {
      currentDetail.value = response.data
      reviewForm.value.comment = ''
      detailDialogVisible.value = true
    }
  } catch (error: any) {
    ElMessage.error(error.message || '獲取詳情失敗')
  }
}

const approveVerification = async (id: number) => {
  try {
    await ElMessageBox.confirm('確定要通過這個身份認證嗎？', '確認操作', {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'success'
    })
    
    await reviewVerification(id, true)
  } catch {
    // 用戶取消
  }
}

const rejectVerification = async (id: number) => {
  try {
    await ElMessageBox.confirm('確定要拒絕這個身份認證嗎？', '確認操作', {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await reviewVerification(id, false)
  } catch {
    // 用戶取消
  }
}

const reviewVerification = async (id: number, approved: boolean, comment?: string) => {
  reviewLoading.value = true
  try {
    const reviewComment = comment || reviewForm.value.comment
    const response = await adminStore.identityAPI.reviewVerification(id, approved, reviewComment)
    
    if (response.success) {
      ElMessage.success(approved ? '審核通過' : '審核拒絕')
      detailDialogVisible.value = false
      loadPendingVerifications() // 刷新列表
      
      // 更新統計
      if (approved) {
        approvedCount.value++
      } else {
        rejectedCount.value++
      }
    }
  } catch (error: any) {
    ElMessage.error(error.message || '審核失敗')
  } finally {
    reviewLoading.value = false
  }
}

const closeDetailDialog = () => {
  detailDialogVisible.value = false
  currentDetail.value = null
  reviewForm.value.comment = ''
}

const previewImage = (url: string) => {
  window.open(url, '_blank')
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-TW')
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('確定要登出嗎？', '確認登出', {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    adminStore.logout()
    ElMessage.success('登出成功')
    router.push('/admin-login')
  } catch {
    // 用戶取消
  }
}
</script>

<style scoped>
.admin-dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-content h1 {
  margin: 0;
  font-size: 20px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.admin-info {
  font-size: 14px;
}

.main-container {
  flex: 1;
}

.sidebar {
  background: #f5f5f5;
  border-right: 1px solid #e6e6e6;
}

.sidebar-menu {
  border: none;
  background: transparent;
}

.content {
  padding: 20px;
  background: #f9f9f9;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.pending {
  background: #e6a23c;
}

.stat-icon.approved {
  background: #67c23a;
}

.stat-icon.rejected {
  background: #f56c6c;
}

.stat-icon.total {
  background: #409eff;
}

.stat-content h3 {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
}

.stat-content p {
  margin: 5px 0 0 0;
  color: #666;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-content {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.detail-section p {
  margin: 8px 0;
  line-height: 1.5;
}

.id-card-images {
  display: flex;
  gap: 20px;
}

.id-card-item {
  flex: 1;
}

.id-card-item img {
  width: 100%;
  max-width: 200px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
}

.id-card-item img:hover {
  transform: scale(1.05);
}
</style>
