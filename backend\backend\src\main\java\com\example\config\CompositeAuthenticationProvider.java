package com.example.config;

import com.example.service.AdminService;
import com.example.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CompositeAuthenticationProvider implements AuthenticationProvider {

    @Autowired
    @Lazy
    private UserService userService;

    @Autowired
    @Lazy
    private AdminService adminService;

    private PasswordEncoder passwordEncoder;

    public CompositeAuthenticationProvider() {
        // 在構造函數中初始化 PasswordEncoder 以避免循環依賴
        this.passwordEncoder = new org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder();
    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String username = authentication.getName();
        String password = authentication.getCredentials().toString();

        UserDetails userDetails = null;
        
        // 首先嘗試從用戶服務加載
        try {
            userDetails = userService.loadUserByUsername(username);
            log.debug("從用戶服務找到用戶: {}", username);
        } catch (UsernameNotFoundException e) {
            // 如果用戶服務找不到，嘗試從管理員服務加載
            try {
                userDetails = adminService.loadUserByUsername(username);
                log.debug("從管理員服務找到用戶: {}", username);
            } catch (UsernameNotFoundException adminE) {
                log.warn("用戶和管理員服務都找不到用戶: {}", username);
                throw new UsernameNotFoundException("用戶不存在: " + username);
            }
        }

        // 驗證密碼
        if (userDetails != null && passwordEncoder.matches(password, userDetails.getPassword())) {
            log.info("用戶認證成功: {}", username);
            return new UsernamePasswordAuthenticationToken(
                userDetails, 
                password, 
                userDetails.getAuthorities()
            );
        } else {
            log.warn("用戶認證失敗 - 密碼錯誤: {}", username);
            throw new BadCredentialsException("密碼錯誤");
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }
}
