package com.example.controller;

import com.example.dto.ApiResponse;
import com.example.dto.MenuDto;
import com.example.dto.MenuCreateRequest;
import com.example.dto.MenuUpdateRequest;
import com.example.entity.Admin;
import com.example.entity.User;
import com.example.service.MenuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 菜單管理控制器
 * 提供菜單的CRUD操作和查詢功能
 */
@RestController
@RequestMapping("/api/menu")
@CrossOrigin(origins = "*")
@Slf4j
public class MenuController {
    
    @Autowired
    private MenuService menuService;
    
    /**
     * 獲取當前用戶的菜單樹
     */
    @GetMapping("/tree")
    public ResponseEntity<ApiResponse<List<MenuDto>>> getUserMenuTree(Authentication authentication) {
        try {
            if (authentication == null || authentication.getPrincipal() == null) {
                return ResponseEntity.status(401)
                    .body(ApiResponse.error("請先登入"));
            }
            
            List<MenuDto> menuTree;
            
            // 判斷用戶類型並獲取相應的菜單樹
            if (authentication.getPrincipal() instanceof User) {
                User user = (User) authentication.getPrincipal();
                menuTree = menuService.getUserMenuTree(user);
                log.debug("獲取用戶菜單樹: {}", user.getUsername());
            } else if (authentication.getPrincipal() instanceof Admin) {
                // 管理員獲取完整菜單樹
                menuTree = menuService.getMenuTree();
                Admin admin = (Admin) authentication.getPrincipal();
                log.debug("獲取管理員菜單樹: {}", admin.getUsername());
            } else {
                return ResponseEntity.status(403)
                    .body(ApiResponse.error("無效的用戶類型"));
            }
            
            return ResponseEntity.ok(ApiResponse.success(menuTree));
            
        } catch (Exception e) {
            log.error("獲取菜單樹失敗", e);
            return ResponseEntity.status(500)
                .body(ApiResponse.error("獲取菜單樹失敗: " + e.getMessage()));
        }
    }
    
    /**
     * 獲取完整菜單樹（管理員專用）
     */
    @GetMapping("/admin/tree")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<MenuDto>>> getFullMenuTree() {
        try {
            List<MenuDto> menuTree = menuService.getMenuTree();
            return ResponseEntity.ok(ApiResponse.success(menuTree));
            
        } catch (Exception e) {
            log.error("獲取完整菜單樹失敗", e);
            return ResponseEntity.status(500)
                .body(ApiResponse.error("獲取菜單樹失敗: " + e.getMessage()));
        }
    }
    
    /**
     * 根據ID獲取菜單詳情（管理員專用）
     */
    @GetMapping("/admin/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<MenuDto>> getMenuById(@PathVariable Long id) {
        try {
            MenuDto menu = menuService.getMenuById(id);
            return ResponseEntity.ok(ApiResponse.success(menu));
            
        } catch (Exception e) {
            log.error("獲取菜單詳情失敗: id={}", id, e);
            return ResponseEntity.status(500)
                .body(ApiResponse.error("獲取菜單詳情失敗: " + e.getMessage()));
        }
    }
    
    /**
     * 創建菜單（管理員專用）
     */
    @PostMapping("/admin")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<MenuDto>> createMenu(
            @RequestBody MenuCreateRequest request,
            Authentication authentication) {
        try {
            // 轉換為MenuDto
            MenuDto menuDto = new MenuDto();
            menuDto.setName(request.getName());
            menuDto.setPath(request.getPath());
            menuDto.setIcon(request.getIcon());
            menuDto.setDescription(request.getDescription());
            menuDto.setSortOrder(request.getSortOrder());
            menuDto.setEnabled(request.getEnabled());
            menuDto.setMenuType(request.getMenuType());
            menuDto.setPermission(request.getPermission());
            menuDto.setParentId(request.getParentId());
            
            MenuDto createdMenu = menuService.createMenu(menuDto);
            
            Admin admin = (Admin) authentication.getPrincipal();
            log.info("管理員 {} 創建菜單: {}", admin.getUsername(), createdMenu.getName());
            
            return ResponseEntity.ok(ApiResponse.success(createdMenu));
            
        } catch (Exception e) {
            log.error("創建菜單失敗", e);
            return ResponseEntity.status(500)
                .body(ApiResponse.error("創建菜單失敗: " + e.getMessage()));
        }
    }
    
    /**
     * 更新菜單（管理員專用）
     */
    @PutMapping("/admin/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<MenuDto>> updateMenu(
            @PathVariable Long id,
            @RequestBody MenuUpdateRequest request,
            Authentication authentication) {
        try {
            // 轉換為MenuDto
            MenuDto menuDto = new MenuDto();
            menuDto.setId(id);
            menuDto.setName(request.getName());
            menuDto.setPath(request.getPath());
            menuDto.setIcon(request.getIcon());
            menuDto.setDescription(request.getDescription());
            menuDto.setSortOrder(request.getSortOrder());
            menuDto.setEnabled(request.getEnabled());
            menuDto.setMenuType(request.getMenuType());
            menuDto.setPermission(request.getPermission());
            menuDto.setParentId(request.getParentId());
            
            MenuDto updatedMenu = menuService.updateMenu(id, menuDto);
            
            Admin admin = (Admin) authentication.getPrincipal();
            log.info("管理員 {} 更新菜單: {}", admin.getUsername(), updatedMenu.getName());
            
            return ResponseEntity.ok(ApiResponse.success(updatedMenu));
            
        } catch (Exception e) {
            log.error("更新菜單失敗: id={}", id, e);
            return ResponseEntity.status(500)
                .body(ApiResponse.error("更新菜單失敗: " + e.getMessage()));
        }
    }
    
    /**
     * 刪除菜單（管理員專用）
     */
    @DeleteMapping("/admin/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deleteMenu(
            @PathVariable Long id,
            Authentication authentication) {
        try {
            menuService.deleteMenu(id);
            
            Admin admin = (Admin) authentication.getPrincipal();
            log.info("管理員 {} 刪除菜單: id={}", admin.getUsername(), id);
            
            return ResponseEntity.ok(ApiResponse.success("菜單刪除成功"));
            
        } catch (Exception e) {
            log.error("刪除菜單失敗: id={}", id, e);
            return ResponseEntity.status(500)
                .body(ApiResponse.error("刪除菜單失敗: " + e.getMessage()));
        }
    }
    
    /**
     * 清除菜單緩存（管理員專用）
     */
    @PostMapping("/admin/cache/clear")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> clearMenuCache(Authentication authentication) {
        try {
            menuService.clearMenuCache();
            
            Admin admin = (Admin) authentication.getPrincipal();
            log.info("管理員 {} 清除菜單緩存", admin.getUsername());
            
            return ResponseEntity.ok(ApiResponse.success("菜單緩存清除成功"));
            
        } catch (Exception e) {
            log.error("清除菜單緩存失敗", e);
            return ResponseEntity.status(500)
                .body(ApiResponse.error("清除菜單緩存失敗: " + e.getMessage()));
        }
    }
    
    /**
     * 預熱菜單緩存（管理員專用）
     */
    @PostMapping("/admin/cache/warmup")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> warmUpMenuCache(Authentication authentication) {
        try {
            menuService.warmUpMenuCache();
            
            Admin admin = (Admin) authentication.getPrincipal();
            log.info("管理員 {} 預熱菜單緩存", admin.getUsername());
            
            return ResponseEntity.ok(ApiResponse.success("菜單緩存預熱成功"));
            
        } catch (Exception e) {
            log.error("預熱菜單緩存失敗", e);
            return ResponseEntity.status(500)
                .body(ApiResponse.error("預熱菜單緩存失敗: " + e.getMessage()));
        }
    }
}
