# 社交關注功能開發計劃

## 項目概述
基於Redis + MySQL混合架構實現社交關注功能，包含：
1. 添加關注和取消關注
2. 查詢關注列表
3. 隨機推薦可能認識的人
4. 查詢兩個用戶之間的共同關注

## 技術方案
- **數據持久化：** MySQL user_follows表
- **緩存層：** Redis Set和Hash結構
- **架構：** 寫入同時更新MySQL和Redis，讀取優先從Redis

## Redis數據結構設計
```
user:following:{userId} -> Set {關注的用戶ID}
user:followers:{userId} -> Set {粉絲用戶ID}
user:follow:count:{userId} -> Hash {following: 數量, followers: 數量}
```

## 實施步驟
1. 創建user_follows數據表和索引
2. 實體類和Repository層實現
3. Redis緩存操作工具類
4. UserFollowService核心業務邏輯
5. UserFollowController API端點
6. 前端集成和界面實現
7. 數據同步和一致性保證
8. 測試和性能優化

## 功能API設計
- POST /api/user/follow/{userId} - 關注用戶
- DELETE /api/user/follow/{userId} - 取消關注
- GET /api/user/following - 查詢關注列表
- GET /api/user/followers - 查詢粉絲列表
- GET /api/user/recommend - 推薦用戶
- GET /api/user/mutual-follows/{userId} - 共同關注
