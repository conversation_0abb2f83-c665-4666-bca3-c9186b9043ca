// 在瀏覽器控制台中運行此腳本來調試支付功能

console.log('=== 支付功能調試腳本 ===');

// 1. 檢查 localStorage 中的 token
const token = localStorage.getItem('token');
console.log('Token:', token ? token.substring(0, 50) + '...' : '未找到');

// 2. 檢查當前頁面 URL
console.log('當前頁面:', window.location.href);

// 3. 檢查是否有支付按鈕
const payButton = document.querySelector('.pay-btn');
console.log('支付按鈕:', payButton);

// 4. 檢查按鈕是否可點擊
if (payButton) {
    console.log('按鈕是否禁用:', payButton.disabled);
    console.log('按鈕樣式:', window.getComputedStyle(payButton).display);
    console.log('按鈕文本:', payButton.textContent);
}

// 5. 嘗試手動觸發點擊事件
function triggerPayment() {
    if (payButton) {
        console.log('嘗試觸發點擊事件...');
        payButton.click();
    } else {
        console.log('未找到支付按鈕');
    }
}

// 6. 直接調用支付 API
async function testPaymentAPI() {
    try {
        console.log('直接測試支付 API...');
        
        const response = await fetch('/api/payment/alipay/create?orderId=3', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log('API 響應狀態:', response.status);
        console.log('API 響應頭:', Object.fromEntries(response.headers.entries()));
        
        const result = await response.json();
        console.log('API 響應結果:', result);
        
        if (result.success && result.data) {
            console.log('支付表單 HTML:', result.data.substring(0, 200) + '...');
            
            // 創建支付表單
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'about:blank';
            form.target = '_blank';
            form.innerHTML = result.data;
            
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
            
            console.log('支付頁面已在新窗口打開');
        } else {
            console.error('支付失敗:', result.message);
        }
        
    } catch (error) {
        console.error('API 調用失敗:', error);
    }
}

// 7. 檢查 Vue 實例
function checkVueInstance() {
    const app = document.querySelector('#app');
    if (app && app.__vue__) {
        console.log('Vue 實例:', app.__vue__);
    } else {
        console.log('未找到 Vue 實例');
    }
}

// 執行檢查
console.log('\n=== 執行檢查 ===');
checkVueInstance();

console.log('\n=== 可用函數 ===');
console.log('triggerPayment() - 觸發支付按鈕點擊');
console.log('testPaymentAPI() - 直接測試支付 API');

// 自動執行 API 測試
if (token) {
    console.log('\n=== 自動執行 API 測試 ===');
    testPaymentAPI();
} else {
    console.log('\n請先登錄獲取 token');
}
