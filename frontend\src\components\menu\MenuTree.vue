<template>
  <div class="menu-tree">
    <!-- 加載狀態 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    
    <!-- 菜單樹 -->
    <el-menu
      v-else
      :default-active="activeMenu"
      :collapse="collapsed"
      :unique-opened="uniqueOpened"
      :router="router"
      class="menu-container"
      @select="handleMenuSelect"
    >
      <menu-item
        v-for="menu in menuTree"
        :key="menu.id"
        :menu="menu"
        :collapsed="collapsed"
      />
    </el-menu>
    
    <!-- 錯誤提示 -->
    <div v-if="error" class="error-container">
      <el-alert
        :title="error"
        type="error"
        :closable="false"
        show-icon
      />
      <el-button
        type="primary"
        size="small"
        @click="loadMenuTree"
        style="margin-top: 10px"
      >
        重新加載
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import MenuService, { type MenuDto } from '@/api/menu'
import MenuItem from './MenuItem.vue'

// Props
interface Props {
  collapsed?: boolean
  uniqueOpened?: boolean
  router?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false,
  uniqueOpened: true,
  router: true
})

// Emits
const emit = defineEmits<{
  menuSelect: [menu: MenuDto]
  loadComplete: [menuTree: MenuDto[]]
  loadError: [error: string]
}>()

// 響應式數據
const loading = ref(false)
const error = ref('')
const menuTree = ref<MenuDto[]>([])
const route = useRoute()

// 計算屬性
const activeMenu = computed(() => {
  return route.path
})

// 監聽路由變化
watch(() => route.path, (newPath) => {
  // 可以在這裡添加路由變化時的邏輯
  console.log('當前路由:', newPath)
})

// 方法
const loadMenuTree = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const data = await MenuService.getUserMenuTree()
    menuTree.value = data
    emit('loadComplete', data)
    
    console.log('菜單樹加載成功:', data)
    
  } catch (err: any) {
    error.value = err.message || '加載菜單失敗'
    emit('loadError', error.value)
    
    ElMessage.error(error.value)
    
  } finally {
    loading.value = false
  }
}

const handleMenuSelect = (index: string, indexPath: string[]) => {
  // 查找選中的菜單項
  const selectedMenu = MenuService.findMenuByPath(menuTree.value, index)
  if (selectedMenu) {
    emit('menuSelect', selectedMenu)
  }
  
  console.log('菜單選中:', index, indexPath)
}

// 刷新菜單樹
const refreshMenuTree = () => {
  loadMenuTree()
}

// 獲取菜單面包屑
const getMenuBreadcrumb = (path: string) => {
  return MenuService.getMenuBreadcrumb(menuTree.value, path)
}

// 暴露方法給父組件
defineExpose({
  refreshMenuTree,
  getMenuBreadcrumb,
  menuTree: computed(() => menuTree.value)
})

// 生命週期
onMounted(() => {
  // 启用动态菜单加载
  loadMenuTree()
})
</script>

<style scoped>
.menu-tree {
  height: 100%;
  overflow-y: auto;
}

.loading-container {
  padding: 24px;
}

.menu-container {
  border: none;
  height: 100%;
  background: transparent;
  width: 100% !important;
}

.menu-container:not(.el-menu--collapse) {
  width: 100% !important;
}

.error-container {
  padding: 24px;
  text-align: center;
}

/* 自定義滾動條 */
.menu-tree::-webkit-scrollbar {
  width: 4px;
}

.menu-tree::-webkit-scrollbar-track {
  background: transparent;
}

.menu-tree::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.menu-tree::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 菜單項樣式調整 */
:deep(.menu-container .el-menu-item) {
  height: 48px;
  line-height: 48px;
  margin: 2px 12px;
  border-radius: 8px;
  background: transparent;
  color: rgba(255, 255, 255, 0.9);
  border: none;
  transition: background-color 0.15s ease, color 0.15s ease;
  position: relative;
}

:deep(.menu-container .el-menu-item:hover) {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

:deep(.menu-container .el-sub-menu .el-sub-menu__title) {
  height: 52px;
  line-height: 52px;
  margin: 4px 16px;
  border-radius: 12px;
  background: transparent;
  color: rgba(255, 255, 255, 0.9);
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.menu-container .el-sub-menu .el-sub-menu__title:hover) {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  transform: translateX(4px);
}

/* 折疊狀態下的樣式 */
:deep(.menu-container.el-menu--collapse .el-menu-item),
:deep(.menu-container.el-menu--collapse .el-sub-menu .el-sub-menu__title) {
  text-align: center;
  padding: 0;
  margin: 4px 8px;
  width: calc(100% - 16px);
}

/* 激活狀態樣式 */
:deep(.menu-container .el-menu-item.is-active) {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

:deep(.menu-container .el-menu-item.is-active::after) {
  content: '';
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: white;
  border-radius: 2px;
}

/* 子菜单样式 */
:deep(.menu-container .el-sub-menu .el-menu) {
  background: rgba(0, 0, 0, 0.1);
  margin: 4px 0;
  border-radius: 8px;
}

:deep(.menu-container .el-sub-menu .el-menu .el-menu-item) {
  margin: 2px 8px;
  height: 44px;
  line-height: 44px;
  font-size: 14px;
  padding-left: 40px;
}

/* 图标样式 */
:deep(.menu-container .el-menu-item .el-icon),
:deep(.menu-container .el-sub-menu__title .el-icon) {
  margin-right: 12px;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

:deep(.menu-container .el-menu-item:hover .el-icon),
:deep(.menu-container .el-sub-menu__title:hover .el-icon),
:deep(.menu-container .el-menu-item.is-active .el-icon) {
  color: white;
}

/* 折叠状态下的图标 */
:deep(.menu-container.el-menu--collapse .el-menu-item .el-icon),
:deep(.menu-container.el-menu--collapse .el-sub-menu__title .el-icon) {
  margin-right: 0;
  font-size: 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  :deep(.menu-container .el-menu-item),
  :deep(.menu-container .el-sub-menu .el-sub-menu__title) {
    margin: 4px 12px;
    height: 48px;
    line-height: 48px;
  }
}
</style>
