package com.example.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * 批處理優化工具類
 * 用於合併多個相似的操作，減少數據庫訪問次數
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Slf4j
@Component
public class BatchOptimizer {

    @Value("${app.performance.batch.size:50}")
    private int batchSize;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    private final Map<String, BatchBuffer<?>> buffers = new ConcurrentHashMap<>();

    /**
     * 批處理緩衝區
     */
    private static class BatchBuffer<T> {
        private final List<T> items = Collections.synchronizedList(new ArrayList<>());
        private final Consumer<List<T>> processor;
        private final int maxSize;
        private volatile long lastAddTime = System.currentTimeMillis();

        public BatchBuffer(Consumer<List<T>> processor, int maxSize) {
            this.processor = processor;
            this.maxSize = maxSize;
        }

        public void add(T item) {
            items.add(item);
            lastAddTime = System.currentTimeMillis();
            
            if (items.size() >= maxSize) {
                flush();
            }
        }

        public void flush() {
            if (!items.isEmpty()) {
                List<T> toProcess = new ArrayList<>(items);
                items.clear();
                try {
                    processor.accept(toProcess);
                    log.debug("批處理完成，處理了 {} 個項目", toProcess.size());
                } catch (Exception e) {
                    log.error("批處理執行失敗", e);
                }
            }
        }

        public boolean shouldFlush(long maxWaitTime) {
            return !items.isEmpty() && 
                   (System.currentTimeMillis() - lastAddTime) > maxWaitTime;
        }
    }

    /**
     * 添加項目到批處理隊列
     */
    @SuppressWarnings("unchecked")
    public <T> void addToBatch(String batchKey, T item, Consumer<List<T>> processor) {
        BatchBuffer<T> buffer = (BatchBuffer<T>) buffers.computeIfAbsent(batchKey, 
            k -> new BatchBuffer<>(processor, batchSize));
        buffer.add(item);
    }

    /**
     * 手動刷新指定批處理
     */
    @SuppressWarnings("unchecked")
    public void flushBatch(String batchKey) {
        BatchBuffer<?> buffer = buffers.get(batchKey);
        if (buffer != null) {
            buffer.flush();
        }
    }

    /**
     * 刷新所有批處理
     */
    public void flushAll() {
        buffers.values().forEach(BatchBuffer::flush);
    }

    /**
     * 初始化定時刷新任務
     */
    public void init() {
        // 每5秒檢查一次是否需要刷新
        scheduler.scheduleAtFixedRate(() -> {
            buffers.values().forEach(buffer -> {
                if (buffer.shouldFlush(5000)) { // 5秒超時
                    buffer.flush();
                }
            });
        }, 5, 5, TimeUnit.SECONDS);
        
        log.info("批處理優化器已啟動，批處理大小: {}", batchSize);
    }

    /**
     * 關閉批處理優化器
     */
    public void shutdown() {
        flushAll();
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 獲取批處理統計信息
     */
    public Map<String, Integer> getStatistics() {
        Map<String, Integer> stats = new HashMap<>();
        buffers.forEach((key, buffer) -> {
            stats.put(key, buffer.items.size());
        });
        return stats;
    }
}
