-- 菜單表結構
CREATE TABLE IF NOT EXISTS menus (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '菜單ID',
    name VARCHAR(100) NOT NULL COMMENT '菜單名稱',
    path VARCHAR(200) COMMENT '菜單路徑/URL',
    icon VARCHAR(50) COMMENT '菜單圖標',
    description VARCHAR(500) COMMENT '菜單描述',
    sort_order INT DEFAULT 0 COMMENT '排序順序',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否啟用',
    menu_type ENUM('MENU', 'BUTTON', 'LINK') DEFAULT 'MENU' COMMENT '菜單類型',
    permission VARCHAR(100) COMMENT '權限標識',
    parent_id BIGINT COMMENT '父菜單ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '創建時間',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
    
    INDEX idx_parent_id (parent_id),
    INDEX idx_enabled (enabled),
    INDEX idx_sort_order (sort_order),
    INDEX idx_menu_type (menu_type),
    INDEX idx_permission (permission),
    
    FOREIGN KEY (parent_id) REFERENCES menus(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜單表';

-- 插入示例菜單數據
INSERT INTO menus (name, path, icon, description, sort_order, enabled, menu_type, permission, parent_id) VALUES
-- 根菜單
('首頁', '/app/home', 'House', '系統首頁', 1, TRUE, 'MENU', NULL, NULL),
('用戶管理', '/app/user', 'User', '用戶相關功能', 2, TRUE, 'MENU', 'USER', NULL),
('商品管理', '/app/products', 'ShoppingBag', '商品相關功能', 3, TRUE, 'MENU', 'PRODUCT', NULL),
('系統管理', '/app/system', 'Setting', '系統管理功能', 4, TRUE, 'MENU', 'ADMIN', NULL),

-- 用戶管理子菜單
('個人資料', '/app/profile', 'UserFilled', '查看和編輯個人資料', 1, TRUE, 'MENU', NULL, 2),
('身份認證', '/app/identity', 'CreditCard', '身份認證管理', 2, TRUE, 'MENU', NULL, 2),
('關注管理', '/app/follow', 'Star', '關注和粉絲管理', 3, TRUE, 'MENU', NULL, 2),
('我的收藏', '/app/my-favorites', 'Star', '我的收藏管理', 4, TRUE, 'MENU', NULL, 2),

-- 商品管理子菜單
('商品列表', '/app/products', 'Box', '瀏覽商品列表', 1, TRUE, 'MENU', NULL, 3),
('商品分類', '/app/products/categories', 'FolderOpened', '商品分類瀏覽', 2, TRUE, 'MENU', NULL, 3),
('商品搜索', '/app/products/search', 'Search', '搜索商品', 3, TRUE, 'MENU', NULL, 3),
('熱門商品', '/app/products/hot', 'Fire', '熱門商品', 4, TRUE, 'MENU', NULL, 3),
('推薦商品', '/app/products/recommended', 'Trophy', '推薦商品', 5, TRUE, 'MENU', NULL, 3),

-- 系統管理子菜單
('菜單管理', '/app/system/menu', 'Menu', '系統菜單管理', 1, TRUE, 'MENU', 'ADMIN', 4),
('用戶審核', '/app/system/user-audit', 'View', '用戶身份認證審核', 2, TRUE, 'MENU', 'ADMIN', 4),
('系統設置', '/app/system/settings', 'Tools', '系統參數設置', 3, TRUE, 'MENU', 'ADMIN', 4);

-- 更新父菜單ID（因為需要先插入父菜單才能獲得ID）
UPDATE menus SET parent_id = (SELECT id FROM (SELECT id FROM menus WHERE name = '用戶管理') AS temp) WHERE name IN ('個人資料', '身份認證', '關注管理', '我的收藏');
UPDATE menus SET parent_id = (SELECT id FROM (SELECT id FROM menus WHERE name = '商品管理') AS temp) WHERE name IN ('商品列表', '商品分類', '商品搜索', '熱門商品', '推薦商品');
UPDATE menus SET parent_id = (SELECT id FROM (SELECT id FROM menus WHERE name = '系統管理') AS temp) WHERE name IN ('菜單管理', '用戶審核', '系統設置');
