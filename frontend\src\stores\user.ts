import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI, userAPI } from '../api'
import { isTokenValid, getStoredTokens, storeTokens, clearTokens } from '../utils/token'

export interface User {
  id: number
  username: string
  email: string
  realName?: string
  phoneNumber?: string
  emailVerified: boolean
  identityVerified: boolean
  identityStatus: 'NOT_SUBMITTED' | 'PENDING' | 'APPROVED' | 'REJECTED'
  role: 'USER' | 'ADMIN'
  createdAt: string
  updatedAt?: string
  idCardNumber?: string
  idCardFrontUrl?: string
  idCardBackUrl?: string
}

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const refreshToken = ref<string | null>(localStorage.getItem('refreshToken'))
  const loading = ref(false)

  // 計算屬性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'ADMIN')
  const identityStatusText = computed(() => {
    switch (user.value?.identityStatus) {
      case 'NOT_SUBMITTED': return '未提交'
      case 'PENDING': return '待審核'
      case 'APPROVED': return '已通過'
      case 'REJECTED': return '已拒絕'
      default: return '未知'
    }
  })

  // 登入
  const login = async (credentials: { username: string; password: string }) => {
    loading.value = true
    try {
      const response = await authAPI.login(credentials)
      if (response.success) {
        token.value = response.data.accessToken
        refreshToken.value = response.data.refreshToken
        user.value = response.data.user

        // 使用工具函數存儲 tokens
        storeTokens(response.data.accessToken, response.data.refreshToken)
        localStorage.setItem('user', JSON.stringify(response.data.user))
        return { success: true }
      }
      return { success: false, message: response.message }
    } catch (error: any) {
      return { success: false, message: error.message || '登入失敗' }
    } finally {
      loading.value = false
    }
  }

  // 註冊
  const register = async (data: {
    username: string
    email: string
    password: string
    verificationCode: string
  }) => {
    loading.value = true
    try {
      const response = await authAPI.register(data)
      if (response.success) {
        token.value = response.data.accessToken
        refreshToken.value = response.data.refreshToken
        user.value = response.data.user

        // 使用工具函數存儲 tokens
        storeTokens(response.data.accessToken, response.data.refreshToken)
        localStorage.setItem('user', JSON.stringify(response.data.user))
        return { success: true }
      }
      return { success: false, message: response.message }
    } catch (error: any) {
      return { success: false, message: error.message || '註冊失敗' }
    } finally {
      loading.value = false
    }
  }

  // 刷新 Access Token
  const refreshAccessToken = async (): Promise<boolean> => {
    if (!refreshToken.value) {
      return false
    }

    try {
      const response = await authAPI.refreshToken({ refreshToken: refreshToken.value })
      if (response.success) {
        token.value = response.data.accessToken
        storeTokens(response.data.accessToken, refreshToken.value)
        return true
      }
      return false
    } catch (error) {
      console.error('刷新 token 失敗:', error)
      return false
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = null
    refreshToken.value = null
    clearTokens()
  }

  // 獲取用戶信息
  const fetchUserProfile = async () => {
    try {
      const response = await userAPI.getProfile()
      if (response.success) {
        user.value = response.data
        localStorage.setItem('user', JSON.stringify(response.data))
      }
    } catch (error) {
      console.error('獲取用戶信息失敗:', error)
    }
  }

  // 更新用戶信息
  const updateProfile = async (data: Partial<User>) => {
    try {
      const response = await userAPI.updateProfile(data)
      if (response.success) {
        user.value = response.data
        localStorage.setItem('user', JSON.stringify(response.data))
        return { success: true }
      }
      return { success: false, message: response.message }
    } catch (error: any) {
      return { success: false, message: error.message || '更新失敗' }
    }
  }

  // 初始化用戶狀態
  const initializeUser = () => {
    const savedUser = localStorage.getItem('user')
    const tokens = getStoredTokens()

    // 更新 ref 狀態
    token.value = tokens.accessToken
    refreshToken.value = tokens.refreshToken

    if (savedUser && tokens.accessToken) {
      try {
        // 檢查 access token 是否有效
        if (isTokenValid(tokens.accessToken)) {
          user.value = JSON.parse(savedUser)
        } else {
          // token 無效，清理狀態
          console.warn('Access token 已過期，清理用戶狀態')
          logout()
        }
      } catch (error) {
        console.error('解析用戶信息失敗:', error)
        logout()
      }
    }
  }

  return {
    user,
    token,
    refreshToken,
    loading,
    isLoggedIn,
    isAdmin,
    identityStatusText,
    login,
    register,
    logout,
    refreshAccessToken,
    fetchUserProfile,
    updateProfile,
    initializeUser
  }
})
