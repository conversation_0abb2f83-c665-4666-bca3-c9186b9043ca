package com.example.dto;

import com.example.entity.Menu;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

/**
 * 菜單數據傳輸對象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MenuDto {
    
    private Long id;
    private String name;
    private String path;
    private String icon;
    private String description;
    private Integer sortOrder;
    private Boolean enabled;
    private Menu.MenuType menuType;
    private String permission;
    private Long parentId;
    private List<MenuDto> children = new ArrayList<>();
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    /**
     * 從Menu實體轉換為DTO
     */
    public static MenuDto fromEntity(Menu menu) {
        if (menu == null) {
            return null;
        }
        
        MenuDto dto = new MenuDto();
        dto.setId(menu.getId());
        dto.setName(menu.getName());
        dto.setPath(menu.getPath());
        dto.setIcon(menu.getIcon());
        dto.setDescription(menu.getDescription());
        dto.setSortOrder(menu.getSortOrder());
        dto.setEnabled(menu.getEnabled());
        dto.setMenuType(menu.getMenuType());
        dto.setPermission(menu.getPermission());
        dto.setParentId(menu.getParentId());
        dto.setCreatedAt(menu.getCreatedAt());
        dto.setUpdatedAt(menu.getUpdatedAt());
        
        return dto;
    }
    
    /**
     * 從Menu實體轉換為DTO（包含子菜單）
     */
    public static MenuDto fromEntityWithChildren(Menu menu) {
        MenuDto dto = fromEntity(menu);
        if (dto != null && menu.getChildren() != null) {
            for (Menu child : menu.getChildren()) {
                dto.getChildren().add(fromEntityWithChildren(child));
            }
        }
        return dto;
    }
    
    /**
     * 轉換為Menu實體
     */
    public Menu toEntity() {
        Menu menu = new Menu();
        menu.setId(this.id);
        menu.setName(this.name);
        menu.setPath(this.path);
        menu.setIcon(this.icon);
        menu.setDescription(this.description);
        menu.setSortOrder(this.sortOrder);
        menu.setEnabled(this.enabled);
        menu.setMenuType(this.menuType);
        menu.setPermission(this.permission);
        menu.setParentId(this.parentId);
        menu.setCreatedAt(this.createdAt);
        menu.setUpdatedAt(this.updatedAt);
        
        return menu;
    }
    
    /**
     * 判斷是否為根菜單
     */
    public boolean isRoot() {
        return parentId == null;
    }
    
    /**
     * 判斷是否有子菜單
     */
    public boolean hasChildren() {
        return children != null && !children.isEmpty();
    }
}


