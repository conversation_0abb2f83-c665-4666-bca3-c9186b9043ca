package com.example.dto;

import com.example.entity.ProductCategory;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

/**
 * 商品分類DTO類
 * 用於樹形結構的序列化，避免Hibernate懶加載問題
 * 
 * <AUTHOR>
 * @since 2025-01-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductCategoryDTO {
    
    private Long id;
    private String name;
    private Long parentId;
    private Integer level;
    private Integer sortOrder;
    private Integer status;
    private String description;
    private String iconUrl;
    private Integer isLeaf;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Long createdBy;
    
    // 子分類列表 - 用於樹形結構
    private List<ProductCategoryDTO> children = new ArrayList<>();
    
    /**
     * 從實體轉換為DTO
     */
    public static ProductCategoryDTO fromEntity(ProductCategory entity) {
        if (entity == null) {
            return null;
        }
        
        ProductCategoryDTO dto = new ProductCategoryDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setParentId(entity.getParentId());
        dto.setLevel(entity.getLevel());
        dto.setSortOrder(entity.getSortOrder());
        dto.setStatus(entity.getStatus());
        dto.setDescription(entity.getDescription());
        dto.setIconUrl(entity.getIconUrl());
        dto.setIsLeaf(entity.getIsLeaf());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setCreatedBy(entity.getCreatedBy());
        
        return dto;
    }
    
    /**
     * 轉換為實體
     */
    public ProductCategory toEntity() {
        ProductCategory entity = new ProductCategory();
        entity.setId(this.id);
        entity.setName(this.name);
        entity.setParentId(this.parentId);
        entity.setLevel(this.level);
        entity.setSortOrder(this.sortOrder);
        entity.setStatus(this.status);
        entity.setDescription(this.description);
        entity.setIconUrl(this.iconUrl);
        entity.setIsLeaf(this.isLeaf);
        entity.setCreatedAt(this.createdAt);
        entity.setUpdatedAt(this.updatedAt);
        entity.setCreatedBy(this.createdBy);
        
        return entity;
    }
}