package com.example.service;

import com.example.dto.ApiResponse;
import com.example.dto.FavoriteDto;
import com.example.dto.FavoriteItemDto;
import com.example.dto.PagedResponse;
import com.example.entity.Favorite;
import com.example.entity.FavoriteItem;
import com.example.enums.ItemType;
import com.example.exception.DuplicateFavoriteException;
import com.example.exception.FavoriteNotFoundException;
import com.example.repository.FavoriteItemRepository;
import com.example.repository.FavoriteRepository;
import com.example.service.impl.FavoriteServiceImpl;
import com.example.service.FavoriteCacheService;
import com.example.service.RedisService;

import java.time.Duration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 收藏服务单元测试
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@ExtendWith(MockitoExtension.class)
class FavoriteServiceTest {
    
    @Mock
    private FavoriteRepository favoriteRepository;
    
    @Mock
    private FavoriteItemRepository favoriteItemRepository;
    
    @Mock
    private RedisService redisService;
    
    @Mock
    private FavoriteCacheService cacheService;
    
    @InjectMocks
    private FavoriteServiceImpl favoriteService;
    
    private FavoriteItem testItem;
    private Favorite testFavorite;
    private Long userId = 1L;
    private Long itemId = 1L;
    
    @BeforeEach
    void setUp() {
        testItem = new FavoriteItem();
        testItem.setId(itemId);
        testItem.setTitle("Test Item");
        testItem.setDescription("Test Description");
        testItem.setItemType(ItemType.ARTICLE);
        testItem.setFavoriteCount(0);
        testItem.setCreatedAt(LocalDateTime.now());
        
        testFavorite = new Favorite();
        testFavorite.setId(1L);
        testFavorite.setUserId(userId);
        testFavorite.setItemId(itemId);
        testFavorite.setItemType(ItemType.ARTICLE);
        testFavorite.setCreatedAt(LocalDateTime.now());
    }
    
    @Test
    void testAddFavorite_Success() {
        // Given
        when(redisService.get(anyString())).thenReturn(null);
        doNothing().when(redisService).set(anyString(), anyString(), any(Duration.class));
        when(favoriteItemRepository.findById(itemId)).thenReturn(Optional.of(testItem));
        when(favoriteRepository.existsByUserIdAndItemId(userId, itemId)).thenReturn(false);
        when(favoriteRepository.save(any(Favorite.class))).thenReturn(testFavorite);
        
        // When
        ApiResponse<String> response = favoriteService.addFavorite(userId, itemId);
        
        // Then
        assertTrue(response.isSuccess());
        assertEquals("收藏成功", response.getMessage());
        verify(favoriteRepository).save(any(Favorite.class));
        verify(favoriteItemRepository).incrementFavoriteCount(itemId);
        verify(cacheService).evictItemRelatedCache(itemId);
    }
    
    @Test
    void testAddFavorite_ItemNotFound() {
        // Given
        when(redisService.get(anyString())).thenReturn(null);
        doNothing().when(redisService).set(anyString(), anyString(), any(Duration.class));
        when(favoriteItemRepository.findById(itemId)).thenReturn(Optional.empty());
        
        // When & Then
        assertThrows(RuntimeException.class, () -> {
            favoriteService.addFavorite(userId, itemId);
        });
    }
    
    @Test
    void testAddFavorite_AlreadyFavorited() {
        // Given
        when(redisService.get(anyString())).thenReturn(null);
        doNothing().when(redisService).set(anyString(), anyString(), any(Duration.class));
        when(favoriteItemRepository.findById(itemId)).thenReturn(Optional.of(testItem));
        when(favoriteRepository.existsByUserIdAndItemId(userId, itemId)).thenReturn(true);
        
        // When & Then
        assertThrows(DuplicateFavoriteException.class, () -> {
            favoriteService.addFavorite(userId, itemId);
        });
    }
    
    @Test
    void testRemoveFavorite_Success() {
        // Given
        when(redisService.get(anyString())).thenReturn(null);
        doNothing().when(redisService).set(anyString(), anyString(), any(Duration.class));
        when(favoriteRepository.findByUserIdAndItemId(userId, itemId)).thenReturn(Optional.of(testFavorite));
        
        // When
        ApiResponse<String> response = favoriteService.removeFavorite(userId, itemId);
        
        // Then
        assertTrue(response.isSuccess());
        assertEquals("取消收藏成功", response.getMessage());
        verify(favoriteRepository).deleteByUserIdAndItemId(userId, itemId);
        verify(favoriteItemRepository).decrementFavoriteCount(itemId);
        verify(cacheService).evictItemRelatedCache(itemId);
    }
    
    @Test
    void testRemoveFavorite_NotFound() {
        // Given
        when(redisService.get(anyString())).thenReturn(null);
        doNothing().when(redisService).set(anyString(), anyString(), any(Duration.class));
        when(favoriteRepository.findByUserIdAndItemId(userId, itemId)).thenReturn(Optional.empty());
        
        // When & Then
        assertThrows(FavoriteNotFoundException.class, () -> {
            favoriteService.removeFavorite(userId, itemId);
        });
    }
    
    @Test
    void testIsFavorited_True() {
        // Given
        when(favoriteRepository.existsByUserIdAndItemId(userId, itemId)).thenReturn(true);
        
        // When
        ApiResponse<Boolean> response = favoriteService.isFavorited(userId, itemId);
        
        // Then
        assertTrue(response.isSuccess());
        assertTrue(response.getData());
    }
    
    @Test
    void testIsFavorited_False() {
        // Given
        when(favoriteRepository.existsByUserIdAndItemId(userId, itemId)).thenReturn(false);
        
        // When
        ApiResponse<Boolean> response = favoriteService.isFavorited(userId, itemId);
        
        // Then
        assertTrue(response.isSuccess());
        assertFalse(response.getData());
    }
    
    @Test
    void testGetUserFavorites_Success() {
        // Given
        List<Favorite> favorites = Arrays.asList(testFavorite);
        Page<Favorite> favoritePage = new PageImpl<>(favorites, PageRequest.of(0, 20), 1);
        
        when(favoriteRepository.findByUserIdOrderByCreatedAtDesc(eq(userId), any(Pageable.class)))
                .thenReturn(favoritePage);
        when(favoriteItemRepository.findById(itemId)).thenReturn(Optional.of(testItem));
        
        // When
        ApiResponse<PagedResponse<FavoriteDto>> response = favoriteService.getUserFavorites(userId, 0, 20);
        
        // Then
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().getContent().size());
        assertEquals(1, response.getData().getTotalElements());
    }
    
    @Test
    void testGetFavoriteRanking_Success() {
        // Given
        List<FavoriteItem> items = Arrays.asList(testItem);
        Page<FavoriteItem> itemPage = new PageImpl<>(items, PageRequest.of(0, 20), 1);
        
        when(cacheService.getRankingCache(0, 20, null)).thenReturn(null);
        when(favoriteItemRepository.findAllByOrderByFavoriteCountDesc(any(Pageable.class)))
                .thenReturn(itemPage);
        
        // When
        ApiResponse<PagedResponse<FavoriteItemDto>> response = favoriteService.getFavoriteRanking(0, 20, null);
        
        // Then
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().getContent().size());
        verify(cacheService).setRankingCache(eq(0), eq(20), isNull(), any());
    }
    
    @Test
    void testGetFavoriteRanking_FromCache() {
        // Given
        PagedResponse<FavoriteItemDto> cachedResponse = new PagedResponse<>();
        cachedResponse.setContent(Arrays.asList(new FavoriteItemDto(testItem)));
        
        when(cacheService.getRankingCache(0, 20, null)).thenReturn(cachedResponse);
        
        // When
        ApiResponse<PagedResponse<FavoriteItemDto>> response = favoriteService.getFavoriteRanking(0, 20, null);
        
        // Then
        assertTrue(response.isSuccess());
        assertEquals(cachedResponse, response.getData());
        verify(favoriteItemRepository, never()).findAllByOrderByFavoriteCountDesc(any());
    }
    
    @Test
    void testCreateFavoriteItem_Success() {
        // Given
        when(favoriteItemRepository.save(any(FavoriteItem.class))).thenReturn(testItem);
        
        // When
        ApiResponse<FavoriteItemDto> response = favoriteService.createFavoriteItem(
                "Test Title", "Test Description", ItemType.ARTICLE, 
                "http://example.com", "http://example.com/thumb.jpg"
        );
        
        // Then
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals("Test Title", response.getData().getTitle());
        verify(favoriteItemRepository).save(any(FavoriteItem.class));
    }
    
    @Test
    void testDeleteFavoriteItem_Success() {
        // Given
        when(favoriteItemRepository.findById(itemId)).thenReturn(Optional.of(testItem));
        
        // When
        ApiResponse<String> response = favoriteService.deleteFavoriteItem(itemId);
        
        // Then
        assertTrue(response.isSuccess());
        assertEquals("删除成功", response.getMessage());
        verify(favoriteRepository).deleteByItemId(itemId);
        verify(favoriteItemRepository).deleteById(itemId);
        verify(cacheService).evictItemRelatedCache(itemId);
    }
    
    @Test
    void testDeleteFavoriteItem_NotFound() {
        // Given
        when(favoriteItemRepository.findById(itemId)).thenReturn(Optional.empty());
        
        // When
        ApiResponse<String> response = favoriteService.deleteFavoriteItem(itemId);
        
        // Then
        assertFalse(response.isSuccess());
        assertEquals("内容不存在", response.getMessage());
        verify(favoriteRepository, never()).deleteByItemId(any());
        verify(favoriteItemRepository, never()).deleteById(any());
    }
}
