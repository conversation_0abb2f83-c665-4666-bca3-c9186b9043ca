package com.example;

import com.example.entity.User;
import com.example.service.UserFollowService;
import com.example.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

@SpringBootTest
@ActiveProfiles("test")
public class UserFollowServiceTest {

    @Autowired
    private UserFollowService userFollowService;

    @Autowired
    private UserService userService;

    @Test
    public void testFollowUser() {
        try {
            // 測試關注用戶
            Long followerId = 3L; // how 用戶
            Long followingId = 6L; // test3 用戶

            System.out.println("測試關注功能...");
            boolean result = userFollowService.followUser(followerId, followingId);
            System.out.println("關注結果: " + result);

            // 檢查關注狀態
            boolean isFollowing = userFollowService.isFollowing(followerId, followingId);
            System.out.println("關注狀態: " + isFollowing);

            // 獲取關注列表
            List<User> followingList = userFollowService.getFollowingList(followerId, 0, 10);
            System.out.println("關注列表大小: " + followingList.size());

            // 獲取統計數據
            long followingCount = userFollowService.getFollowingCount(followerId);
            long followersCount = userFollowService.getFollowersCount(followingId);
            System.out.println("關注數: " + followingCount + ", 粉絲數: " + followersCount);

        } catch (Exception e) {
            System.err.println("測試失敗: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testRecommendUsers() {
        try {
            Long userId = 3L; // how 用戶
            
            System.out.println("測試推薦用戶功能...");
            List<User> recommendedUsers = userFollowService.recommendUsers(userId, 5);
            System.out.println("推薦用戶數量: " + recommendedUsers.size());
            
            for (User user : recommendedUsers) {
                System.out.println("推薦用戶: " + user.getUsername() + " (" + user.getEmail() + ")");
            }
            
        } catch (Exception e) {
            System.err.println("推薦用戶測試失敗: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testMutualFollows() {
        try {
            Long userId1 = 3L; // how 用戶
            Long userId2 = 4L; // test1 用戶
            
            System.out.println("測試共同關注功能...");
            List<User> mutualFollows = userFollowService.getMutualFollowing(userId1, userId2, 0, 10);
            System.out.println("共同關注數量: " + mutualFollows.size());
            
            for (User user : mutualFollows) {
                System.out.println("共同關注: " + user.getUsername() + " (" + user.getEmail() + ")");
            }
            
        } catch (Exception e) {
            System.err.println("共同關注測試失敗: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testRedisSync() {
        try {
            System.out.println("測試Redis數據同步...");

            Long userId = 3L; // how 用戶

            // 通過調用 getFollowingCount 來觸發數據同步
            long followingCount = userFollowService.getFollowingCount(userId);
            System.out.println("關注數量: " + followingCount);

            // 驗證Redis中的數據
            boolean isFollowing = userFollowService.isFollowing(userId, 4L);
            System.out.println("Redis中的關注狀態: " + isFollowing);

            // 獲取關注列表（這也會觸發同步）
            List<User> followingList = userFollowService.getFollowingList(userId, 0, 10);
            System.out.println("關注列表大小: " + followingList.size());

        } catch (Exception e) {
            System.err.println("Redis同步測試失敗: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
