# 支付寶簽名問題診斷與解決方案

## 🔍 問題現狀

✅ **支付功能正常跳轉** - 能夠成功跳轉到支付寶沙箱頁面
❌ **簽名驗證失敗** - 出現 `invalid-signature` 錯誤

## 📋 錯誤詳情

**錯誤代碼**: `invalid-signature`
**錯誤原因**: 驗簽出錯，建議檢查簽名字符串或簽名私鑰與應用公鑰是否匹配

**支付寶生成的驗簽字符串**:
```
alipay_sdk=alipay-sdk-java-dynamicVersionNo
&app_id=9021000129631387
&biz_content={"out_trade_no":"ORD202507250940323996","product_code":"FAST_INSTANT_TRADE_PAY","subject":"商品訂單-ORD202507250940323996","timeout_express":"10m","total_amount":"89.00"}
&charset=UTF-8
&format=JSON
&method=alipay.trade.page.pay
&notify_url=https://b02ad650a2dc.ngrok-free.app/api/payment/alipay/callback
&return_url=https://b02ad650a2dc.ngrok-free.app/payment/success
&sign_type=RSA2
&timestamp=2025-07-25 10:35:30
&version=1.0
```

## 🎯 問題分析

所有參數都正確：
- ✅ 應用ID: 9021000129631387
- ✅ 訂單號: ORD202507250940323996
- ✅ 金額: 89.00
- ✅ 回調地址: ngrok 公網地址
- ✅ 簽名類型: RSA2

**問題可能原因**:
1. 應用公鑰未正確上傳到支付寶沙箱
2. 私鑰與上傳的公鑰不匹配
3. 支付寶公鑰配置錯誤

## 🔧 解決方案

### 方案1: 重新生成密鑰對

1. **生成新的密鑰對**:
```bash
# 生成私鑰
openssl genrsa -out app_private_key.pem 2048

# 生成公鑰
openssl rsa -in app_private_key.pem -pubout -out app_public_key.pem

# 轉換私鑰格式（去掉頭尾標識）
openssl rsa -in app_private_key.pem -out app_private_key_pkcs8.pem -outform PEM
```

2. **上傳公鑰到支付寶沙箱**:
   - 登錄: https://openhome.alipay.com/develop/sandbox/app
   - 上傳 `app_public_key.pem` 內容
   - 獲取支付寶公鑰

3. **更新後端配置**:
   - 使用新生成的私鑰
   - 使用從沙箱獲取的支付寶公鑰

### 方案2: 驗證當前密鑰配置

1. **檢查支付寶沙箱配置**:
   - 確認應用公鑰已正確上傳
   - 確認支付寶公鑰是最新的

2. **驗證密鑰匹配**:
   - 使用支付寶提供的簽名驗證工具
   - 確認私鑰和公鑰是一對

### 方案3: 使用支付寶官方測試密鑰

如果上述方案仍有問題，可以使用支付寶官方提供的測試密鑰對。

## 📝 當前配置檢查

**後端配置文件**: `backend/backend/src/main/resources/application.yml`

```yaml
alipay:
  config:
    appid: 9021000129631387
    serverUrl: https://openapi-sandbox.dl.alipaydev.com/gateway.do
    alipay-public-key: [當前配置的支付寶公鑰]
    private-key: [當前配置的應用私鑰]
    return-url: https://b02ad650a2dc.ngrok-free.app/payment/success
    notify-url: https://b02ad650a2dc.ngrok-free.app/api/payment/alipay/callback
```

## 🚀 下一步操作

1. **立即檢查支付寶沙箱**:
   - 登錄支付寶開放平台沙箱
   - 檢查應用公鑰配置
   - 獲取最新的支付寶公鑰

2. **如果公鑰配置正確**:
   - 重新生成密鑰對
   - 上傳新的應用公鑰
   - 更新後端配置

3. **測試驗證**:
   - 重啟後端服務
   - 重新測試支付功能

## 💡 重要提醒

- 每次更新密鑰配置後都需要重啟後端服務
- 確保 ngrok 保持運行狀態
- 支付寶沙箱環境的密鑰配置變更可能需要幾分鐘生效

---

**結論**: 支付功能的核心邏輯已經完全正確，只需要解決密鑰配置問題即可正常使用支付寶沙箱支付。
