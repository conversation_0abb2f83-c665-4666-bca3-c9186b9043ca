-- 為訂單表添加物流單號字段
-- 創建時間: 2025-01-24
-- 描述: 為支持訂單發貨功能，添加物流單號字段

-- 檢查字段是否已存在，如果不存在則添加
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'orders' 
         AND COLUMN_NAME = 'tracking_number') = 0,
        'ALTER TABLE orders ADD COLUMN tracking_number VARCHAR(100) NULL COMMENT ''物流單號'' AFTER remark;',
        'SELECT ''tracking_number column already exists'' as message;'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 為物流單號字段添加索引（如果不存在）
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'orders' 
         AND INDEX_NAME = 'idx_tracking_number') = 0,
        'ALTER TABLE orders ADD INDEX idx_tracking_number (tracking_number);',
        'SELECT ''idx_tracking_number index already exists'' as message;'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 驗證字段添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'orders' 
AND COLUMN_NAME = 'tracking_number';
