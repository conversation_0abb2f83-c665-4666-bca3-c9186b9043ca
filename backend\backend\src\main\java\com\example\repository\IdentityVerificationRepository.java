package com.example.repository;

import com.example.entity.IdentityVerification;
import com.example.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IdentityVerificationRepository extends JpaRepository<IdentityVerification, Long> {
    
    Optional<IdentityVerification> findByUserAndStatus(User user, IdentityVerification.Status status);
    
    List<IdentityVerification> findByUserOrderBySubmittedAtDesc(User user);
    
    List<IdentityVerification> findByStatusOrderBySubmittedAtAsc(IdentityVerification.Status status);

    List<IdentityVerification> findByStatusOrderBySubmittedAtDesc(IdentityVerification.Status status);

    List<IdentityVerification> findAllByOrderBySubmittedAtDesc();

    // 使用 JOIN FETCH 預加載 User 實體，避免懶加載問題
    @Query("SELECT iv FROM IdentityVerification iv JOIN FETCH iv.user WHERE iv.status = :status ORDER BY iv.submittedAt ASC")
    List<IdentityVerification> findByStatusWithUserOrderBySubmittedAtAsc(IdentityVerification.Status status);

    @Query("SELECT iv FROM IdentityVerification iv JOIN FETCH iv.user ORDER BY iv.submittedAt DESC")
    List<IdentityVerification> findAllWithUserOrderBySubmittedAtDesc();

    @Query("SELECT iv FROM IdentityVerification iv JOIN FETCH iv.user WHERE iv.id = :id")
    Optional<IdentityVerification> findByIdWithUser(@Param("id") Long id);

    long countByStatus(IdentityVerification.Status status);

    boolean existsByIdCardNumber(String idCardNumber);

    // 檢查身份證號碼是否已被其他用戶使用
    boolean existsByIdCardNumberAndUserIdNot(String idCardNumber, Long userId);
}
