package com.example.repository;

import com.example.entity.Menu;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 菜單數據訪問層
 */
@Repository
public interface MenuRepository extends JpaRepository<Menu, Long> {
    
    /**
     * 查找所有啟用的根菜單（按排序順序）
     */
    @Query("SELECT m FROM Menu m WHERE m.parentId IS NULL AND m.enabled = true ORDER BY m.sortOrder ASC")
    List<Menu> findRootMenusEnabled();
    
    /**
     * 查找所有根菜單（按排序順序）
     */
    @Query("SELECT m FROM Menu m WHERE m.parentId IS NULL ORDER BY m.sortOrder ASC")
    List<Menu> findRootMenus();
    
    /**
     * 根據父菜單ID查找子菜單（啟用的，按排序順序）
     */
    @Query("SELECT m FROM Menu m WHERE m.parentId = :parentId AND m.enabled = true ORDER BY m.sortOrder ASC")
    List<Menu> findChildrenByParentIdEnabled(@Param("parentId") Long parentId);
    
    /**
     * 根據父菜單ID查找子菜單（按排序順序）
     */
    @Query("SELECT m FROM Menu m WHERE m.parentId = :parentId ORDER BY m.sortOrder ASC")
    List<Menu> findChildrenByParentId(@Param("parentId") Long parentId);
    
    /**
     * 查找所有啟用的菜單（按排序順序）
     */
    @Query("SELECT m FROM Menu m WHERE m.enabled = true ORDER BY m.sortOrder ASC")
    List<Menu> findAllEnabled();
    
    /**
     * 根據路徑查找菜單
     */
    Optional<Menu> findByPath(String path);
    
    /**
     * 根據權限標識查找菜單
     */
    List<Menu> findByPermission(String permission);
    
    /**
     * 查找指定菜單的所有子菜單（遞歸）
     */
    @Query("SELECT m FROM Menu m WHERE m.parentId = :parentId")
    List<Menu> findAllDescendants(@Param("parentId") Long parentId);
    
    /**
     * 檢查菜單名稱是否存在（同一父菜單下）
     */
    @Query("SELECT COUNT(m) > 0 FROM Menu m WHERE m.name = :name AND m.parentId = :parentId AND m.id != :excludeId")
    boolean existsByNameAndParentIdAndIdNot(@Param("name") String name, @Param("parentId") Long parentId, @Param("excludeId") Long excludeId);
    
    /**
     * 檢查菜單名稱是否存在（根菜單）
     */
    @Query("SELECT COUNT(m) > 0 FROM Menu m WHERE m.name = :name AND m.parentId IS NULL AND m.id != :excludeId")
    boolean existsByNameAndParentIdIsNullAndIdNot(@Param("name") String name, @Param("excludeId") Long excludeId);
    
    /**
     * 根據菜單類型查找菜單
     */
    List<Menu> findByMenuTypeAndEnabledOrderBySortOrder(Menu.MenuType menuType, Boolean enabled);
    
    /**
     * 查找最大排序順序
     */
    @Query("SELECT COALESCE(MAX(m.sortOrder), 0) FROM Menu m WHERE m.parentId = :parentId")
    Integer findMaxSortOrderByParentId(@Param("parentId") Long parentId);
    
    /**
     * 查找根菜單的最大排序順序
     */
    @Query("SELECT COALESCE(MAX(m.sortOrder), 0) FROM Menu m WHERE m.parentId IS NULL")
    Integer findMaxSortOrderForRoot();
}
