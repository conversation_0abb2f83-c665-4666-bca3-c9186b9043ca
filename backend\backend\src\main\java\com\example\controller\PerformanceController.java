package com.example.controller;

import com.example.common.ApiResponse;
import com.example.service.PerformanceMonitorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 性能監控控制器
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Slf4j
@RestController
@RequestMapping("/api/performance")
@RequiredArgsConstructor
public class PerformanceController {

    private final PerformanceMonitorService performanceMonitorService;

    /**
     * 獲取性能統計信息
     */
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getPerformanceStats() {
        try {
            Map<String, Object> stats = performanceMonitorService.getPerformanceStats();
            return ApiResponse.success(stats);
        } catch (Exception e) {
            log.error("獲取性能統計失敗", e);
            return ApiResponse.error("獲取性能統計失敗: " + e.getMessage());
        }
    }

    /**
     * 獲取Redis統計信息
     */
    @GetMapping("/redis")
    public ApiResponse<Map<String, Object>> getRedisStats() {
        try {
            Map<String, Object> stats = performanceMonitorService.getRedisStats();
            return ApiResponse.success(stats);
        } catch (Exception e) {
            log.error("獲取Redis統計失敗", e);
            return ApiResponse.error("獲取Redis統計失敗: " + e.getMessage());
        }
    }

    /**
     * 清理慢查詢記錄
     */
    @PostMapping("/cleanup-slow-queries")
    public ApiResponse<String> cleanupSlowQueries() {
        try {
            performanceMonitorService.cleanupSlowQueries();
            return ApiResponse.success("慢查詢記錄清理完成");
        } catch (Exception e) {
            log.error("清理慢查詢記錄失敗", e);
            return ApiResponse.error("清理失敗: " + e.getMessage());
        }
    }

    /**
     * 重置性能統計
     */
    @PostMapping("/reset")
    public ApiResponse<String> resetStats() {
        try {
            performanceMonitorService.resetStats();
            return ApiResponse.success("性能統計已重置");
        } catch (Exception e) {
            log.error("重置性能統計失敗", e);
            return ApiResponse.error("重置失敗: " + e.getMessage());
        }
    }
}
