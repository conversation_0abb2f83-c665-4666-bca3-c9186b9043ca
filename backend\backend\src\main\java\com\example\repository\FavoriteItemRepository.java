package com.example.repository;

import com.example.entity.FavoriteItem;
import com.example.enums.ItemType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 可收藏内容数据访问接口
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Repository
public interface FavoriteItemRepository extends JpaRepository<FavoriteItem, Long> {
    
    /**
     * 按收藏数量倒序查询所有内容
     * 
     * @param pageable 分页参数
     * @return 内容列表
     */
    Page<FavoriteItem> findAllByOrderByFavoriteCountDesc(Pageable pageable);
    
    /**
     * 按类型和收藏数量倒序查询内容
     * 
     * @param itemType 内容类型
     * @param pageable 分页参数
     * @return 内容列表
     */
    Page<FavoriteItem> findByItemTypeOrderByFavoriteCountDesc(ItemType itemType, Pageable pageable);
    
    /**
     * 更新内容的收藏数量
     * 
     * @param itemId 内容ID
     * @param count 收藏数量
     */
    @Modifying
    @Query("UPDATE FavoriteItem f SET f.favoriteCount = :count WHERE f.id = :itemId")
    void updateFavoriteCount(@Param("itemId") Long itemId, @Param("count") Integer count);
    
    /**
     * 增加内容的收藏数量
     * 
     * @param itemId 内容ID
     */
    @Modifying
    @Query("UPDATE FavoriteItem f SET f.favoriteCount = f.favoriteCount + 1 WHERE f.id = :itemId")
    void incrementFavoriteCount(@Param("itemId") Long itemId);
    
    /**
     * 减少内容的收藏数量
     * 
     * @param itemId 内容ID
     */
    @Modifying
    @Query("UPDATE FavoriteItem f SET f.favoriteCount = CASE WHEN f.favoriteCount > 0 THEN f.favoriteCount - 1 ELSE 0 END WHERE f.id = :itemId")
    void decrementFavoriteCount(@Param("itemId") Long itemId);
    
    /**
     * 按标题模糊搜索内容
     * 
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 内容列表
     */
    Page<FavoriteItem> findByTitleContainingIgnoreCaseOrderByFavoriteCountDesc(String keyword, Pageable pageable);
    
    /**
     * 按标题或描述模糊搜索内容
     * 
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 内容列表
     */
    @Query("SELECT f FROM FavoriteItem f WHERE LOWER(f.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(f.description) LIKE LOWER(CONCAT('%', :keyword, '%')) ORDER BY f.favoriteCount DESC")
    Page<FavoriteItem> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 查找最近创建的内容
     * 
     * @param pageable 分页参数
     * @return 内容列表
     */
    Page<FavoriteItem> findAllByOrderByCreatedAtDesc(Pageable pageable);
    
    /**
     * 查找特定类型的最近创建的内容
     * 
     * @param itemType 内容类型
     * @param pageable 分页参数
     * @return 内容列表
     */
    Page<FavoriteItem> findByItemTypeOrderByCreatedAtDesc(ItemType itemType, Pageable pageable);
    
    /**
     * 批量查询指定ID的内容
     *
     * @param ids ID列表
     * @return 内容列表
     */
    List<FavoriteItem> findByIdIn(List<Long> ids);

    /**
     * 按类型统计内容数量
     *
     * @param itemType 内容类型
     * @return 数量
     */
    long countByItemType(ItemType itemType);
}
