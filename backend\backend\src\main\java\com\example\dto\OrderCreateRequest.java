package com.example.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

/**
 * 訂單創建請求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderCreateRequest {
    
    /**
     * 商品ID（直接購買時使用）
     */
    private Long productId;
    
    /**
     * 購買數量（直接購買時使用）
     */
    @Positive(message = "購買數量必須大於0")
    private Integer quantity;
    
    /**
     * 收貨人姓名
     */
    @NotBlank(message = "收貨人姓名不能為空")
    private String receiverName;
    
    /**
     * 收貨人電話
     */
    @NotBlank(message = "收貨人電話不能為空")
    private String receiverPhone;
    
    /**
     * 收貨地址
     */
    @NotBlank(message = "收貨地址不能為空")
    private String receiverAddress;
    
    /**
     * 訂單備註
     */
    private String remark;
    
    /**
     * 訂單類型：cart-從購物車創建，direct-直接購買
     */
    @NotBlank(message = "訂單類型不能為空")
    private String orderType;
}
