package com.example.controller;

import com.example.dto.ApiResponse;
import com.example.dto.FavoriteDto;
import com.example.dto.FavoriteItemDto;
import com.example.dto.FavoriteStatsDto;
import com.example.dto.PagedResponse;
import com.example.enums.ItemType;
import com.example.entity.User;
import com.example.service.FavoriteService;
import com.example.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

/**
 * 收藏功能控制器
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/api/favorite")
@CrossOrigin(origins = "*")
public class FavoriteController {
    
    private static final Logger logger = LoggerFactory.getLogger(FavoriteController.class);
    
    @Autowired
    private FavoriteService favoriteService;

    @Autowired
    private UserService userService;
    
    /**
     * 收藏内容
     * 
     * @param itemId 内容ID
     * @param auth 认证信息
     * @return 操作结果
     */
    @PostMapping("/{itemId}")
    public ResponseEntity<ApiResponse<String>> addFavorite(
            @PathVariable Long itemId,
            Authentication auth) {
        
        try {
            if (auth == null || auth.getName() == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error("请先登录"));
            }
            
            Long userId = getUserIdFromAuth(auth);

            if (userId == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error("用户信息无效"));
            }
            
            ApiResponse<String> response = favoriteService.addFavorite(userId, itemId);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("收藏操作失败: itemId={}", itemId, e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("服务器内部错误"));
        }
    }
    
    /**
     * 取消收藏
     * 
     * @param itemId 内容ID
     * @param auth 认证信息
     * @return 操作结果
     */
    @DeleteMapping("/{itemId}")
    public ResponseEntity<ApiResponse<String>> removeFavorite(
            @PathVariable Long itemId,
            Authentication auth) {
        
        try {
            if (auth == null || auth.getName() == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error("请先登录"));
            }
            
            Long userId = getUserIdFromAuth(auth);

            if (userId == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error("用户信息无效"));
            }
            
            ApiResponse<String> response = favoriteService.removeFavorite(userId, itemId);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("取消收藏操作失败: itemId={}", itemId, e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("服务器内部错误"));
        }
    }
    
    /**
     * 检查收藏状态
     * 
     * @param itemId 内容ID
     * @param auth 认证信息
     * @return 收藏状态
     */
    @GetMapping("/status/{itemId}")
    public ResponseEntity<ApiResponse<Boolean>> getFavoriteStatus(
            @PathVariable Long itemId,
            Authentication auth) {
        
        try {
            if (auth == null || auth.getName() == null) {
                return ResponseEntity.ok(ApiResponse.success(false));
            }
            
            Long userId = getUserIdFromAuth(auth);

            if (userId == null) {
                return ResponseEntity.ok(ApiResponse.success(false));
            }
            
            ApiResponse<Boolean> response = favoriteService.isFavorited(userId, itemId);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("检查收藏状态失败: itemId={}", itemId, e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("服务器内部错误"));
        }
    }
    
    /**
     * 获取我的收藏列表
     * 
     * @param page 页码
     * @param size 页大小
     * @param itemType 内容类型（可选）
     * @param auth 认证信息
     * @return 收藏列表
     */
    @GetMapping("/my-favorites")
    public ResponseEntity<ApiResponse<PagedResponse<FavoriteDto>>> getMyFavorites(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) ItemType itemType,
            Authentication auth) {
        
        try {
            if (auth == null || auth.getName() == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error("请先登录"));
            }
            
            Long userId = getUserIdFromAuth(auth);

            if (userId == null) {
                return ResponseEntity.status(401)
                        .body(ApiResponse.error("用户信息无效"));
            }
            
            ApiResponse<PagedResponse<FavoriteDto>> response;
            if (itemType != null) {
                response = favoriteService.getUserFavoritesByType(userId, itemType, page, size);
            } else {
                response = favoriteService.getUserFavorites(userId, page, size);
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取收藏列表失败: page={}, size={}", page, size, e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("服务器内部错误"));
        }
    }

    /**
     * 获取收藏排行榜
     *
     * @param page 页码
     * @param size 页大小
     * @param itemType 内容类型（可选）
     * @return 排行榜
     */
    @GetMapping("/ranking")
    public ResponseEntity<ApiResponse<PagedResponse<FavoriteItemDto>>> getFavoriteRanking(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) ItemType itemType) {

        try {
            ApiResponse<PagedResponse<FavoriteItemDto>> response =
                    favoriteService.getFavoriteRanking(page, size, itemType);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取收藏排行榜失败: page={}, size={}, itemType={}", page, size, itemType, e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("服务器内部错误"));
        }
    }

    /**
     * 获取收藏统计信息
     *
     * @param itemId 内容ID
     * @param auth 认证信息
     * @return 统计信息
     */
    @GetMapping("/stats/{itemId}")
    public ResponseEntity<ApiResponse<FavoriteStatsDto>> getFavoriteStats(
            @PathVariable Long itemId,
            Authentication auth) {

        try {
            Long userId = getUserIdFromAuth(auth);

            ApiResponse<FavoriteStatsDto> response = favoriteService.getFavoriteStats(itemId, userId);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取收藏统计失败: itemId={}", itemId, e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("服务器内部错误"));
        }
    }

    /**
     * 搜索可收藏内容
     *
     * @param keyword 关键词
     * @param page 页码
     * @param size 页大小
     * @param auth 认证信息
     * @return 搜索结果
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<PagedResponse<FavoriteItemDto>>> searchFavoriteItems(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication auth) {

        try {
            Long userId = getUserIdFromAuth(auth);

            ApiResponse<PagedResponse<FavoriteItemDto>> response =
                    favoriteService.searchFavoriteItems(keyword, page, size, userId);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("搜索收藏内容失败: keyword={}", keyword, e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("服务器内部错误"));
        }
    }

    /**
     * 获取最近创建的内容
     *
     * @param page 页码
     * @param size 页大小
     * @param itemType 内容类型（可选）
     * @param auth 认证信息
     * @return 最近内容列表
     */
    @GetMapping("/recent")
    public ResponseEntity<ApiResponse<PagedResponse<FavoriteItemDto>>> getRecentItems(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) ItemType itemType,
            Authentication auth) {

        try {
            Long userId = getUserIdFromAuth(auth);

            ApiResponse<PagedResponse<FavoriteItemDto>> response =
                    favoriteService.getRecentItems(page, size, itemType, userId);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取最近内容失败: itemType={}", itemType, e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("服务器内部错误"));
        }
    }

    /**
     * 根据认证信息获取用户ID
     *
     * @param auth 认证信息
     * @return 用户ID，如果获取失败返回null
     */
    private Long getUserIdFromAuth(Authentication auth) {
        if (auth == null || auth.getName() == null) {
            return null;
        }

        try {
            String username = auth.getName();
            return userService.findByUsername(username)
                    .map(User::getId)
                    .orElse(null);
        } catch (Exception e) {
            logger.warn("获取用户ID失败: username={}", auth.getName(), e);
            return null;
        }
    }
}
