/**
 * Playwright 測試運行腳本
 * 用於運行商品排序和篩選功能測試
 */

const { execSync } = require('child_process')
const path = require('path')

// 測試配置
const TEST_CONFIG = {
  // 測試文件路徑
  testFile: 'tests/product-sorting-filtering.spec.ts',
  
  // 瀏覽器配置
  browsers: ['chromium'], // 可選: 'firefox', 'webkit'
  
  // 測試模式
  headed: false, // 設為 true 可以看到瀏覽器界面
  
  // 超時設置
  timeout: 30000,
  
  // 重試次數
  retries: 1,
  
  // 並發數
  workers: 1
}

// 顏色輸出函數
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function colorLog(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// 檢查環境
function checkEnvironment() {
  colorLog('🔍 檢查測試環境...', 'cyan')
  
  try {
    // 檢查 Playwright 是否安裝
    execSync('npx playwright --version', { stdio: 'pipe' })
    colorLog('✅ Playwright 已安裝', 'green')
  } catch (error) {
    colorLog('❌ Playwright 未安裝，正在安裝...', 'red')
    try {
      execSync('npm install -D @playwright/test', { stdio: 'inherit' })
      execSync('npx playwright install', { stdio: 'inherit' })
      colorLog('✅ Playwright 安裝完成', 'green')
    } catch (installError) {
      colorLog('❌ Playwright 安裝失敗', 'red')
      process.exit(1)
    }
  }
  
  // 檢查測試文件是否存在
  const fs = require('fs')
  const testFilePath = path.join(__dirname, TEST_CONFIG.testFile)
  if (!fs.existsSync(testFilePath)) {
    colorLog(`❌ 測試文件不存在: ${testFilePath}`, 'red')
    process.exit(1)
  }
  
  colorLog('✅ 環境檢查完成', 'green')
}

// 運行測試
function runTests() {
  colorLog('🚀 開始運行商品排序和篩選功能測試...', 'cyan')
  
  // 構建 Playwright 命令
  const playwrightCmd = [
    'npx playwright test',
    TEST_CONFIG.testFile,
    `--timeout=${TEST_CONFIG.timeout}`,
    `--retries=${TEST_CONFIG.retries}`,
    `--workers=${TEST_CONFIG.workers}`,
    TEST_CONFIG.headed ? '--headed' : '',
    '--reporter=list'
  ].filter(Boolean).join(' ')
  
  colorLog(`📝 執行命令: ${playwrightCmd}`, 'yellow')
  
  try {
    // 運行測試
    execSync(playwrightCmd, { 
      stdio: 'inherit',
      cwd: path.dirname(__dirname)
    })
    
    colorLog('🎉 所有測試完成！', 'green')
    
  } catch (error) {
    colorLog('❌ 測試執行失敗', 'red')
    colorLog('錯誤詳情:', 'red')
    console.error(error.message)
    
    // 提供故障排除建議
    colorLog('\n🔧 故障排除建議:', 'yellow')
    colorLog('1. 確保前端服務正在運行 (npm run dev)', 'yellow')
    colorLog('2. 確保後端服務正在運行 (Spring Boot)', 'yellow')
    colorLog('3. 確保數據庫連接正常', 'yellow')
    colorLog('4. 檢查網絡連接', 'yellow')
    
    process.exit(1)
  }
}

// 生成測試報告
function generateReport() {
  colorLog('📊 生成測試報告...', 'cyan')
  
  try {
    // 生成 HTML 報告
    execSync('npx playwright show-report', { 
      stdio: 'inherit',
      cwd: path.dirname(__dirname)
    })
  } catch (error) {
    colorLog('⚠️  無法打開測試報告', 'yellow')
  }
}

// 主函數
function main() {
  const args = process.argv.slice(2)
  
  // 解析命令行參數
  if (args.includes('--headed')) {
    TEST_CONFIG.headed = true
  }
  
  if (args.includes('--help') || args.includes('-h')) {
    colorLog('商品排序和篩選功能測試運行器', 'bright')
    colorLog('\n使用方法:', 'cyan')
    colorLog('  node test-runner.js [選項]', 'white')
    colorLog('\n選項:', 'cyan')
    colorLog('  --headed     顯示瀏覽器界面', 'white')
    colorLog('  --help, -h   顯示幫助信息', 'white')
    colorLog('\n示例:', 'cyan')
    colorLog('  node test-runner.js', 'white')
    colorLog('  node test-runner.js --headed', 'white')
    return
  }
  
  colorLog('🎯 商品排序和篩選功能測試', 'bright')
  colorLog('=' .repeat(50), 'cyan')
  
  // 執行測試流程
  checkEnvironment()
  runTests()
  
  // 詢問是否查看報告
  if (!TEST_CONFIG.headed) {
    colorLog('\n📋 是否查看詳細測試報告？(y/N)', 'yellow')
    
    const readline = require('readline')
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    })
    
    rl.question('', (answer) => {
      if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
        generateReport()
      }
      rl.close()
    })
  }
}

// 處理未捕獲的異常
process.on('uncaughtException', (error) => {
  colorLog('❌ 未捕獲的異常:', 'red')
  console.error(error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  colorLog('❌ 未處理的 Promise 拒絕:', 'red')
  console.error('Promise:', promise)
  console.error('Reason:', reason)
  process.exit(1)
})

// 運行主函數
if (require.main === module) {
  main()
}

module.exports = {
  runTests,
  TEST_CONFIG
}
