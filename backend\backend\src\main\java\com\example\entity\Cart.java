package com.example.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 購物車實體類
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Entity
@Table(name = "carts")
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "user"})
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Cart {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 用戶ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    /**
     * 購物車總金額
     */
    @Column(name = "total_amount", precision = 10, scale = 2)
    private BigDecimal totalAmount = BigDecimal.ZERO;
    
    /**
     * 購物車總商品數量
     */
    @Column(name = "total_quantity", nullable = false)
    private Integer totalQuantity = 0;
    
    /**
     * 購物車狀態：1-正常，0-已清空，-1-已轉為訂單
     */
    @Column(name = "status", nullable = false)
    private Integer status = 1;
    
    /**
     * 創建時間
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    /**
     * 更新時間
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // 關聯關係 - 用戶
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;
    
    // 關聯關係 - 購物車項目列表
    @OneToMany(mappedBy = "cart", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private List<CartItem> cartItems;
    
    /**
     * 購物車狀態枚舉
     */
    public static class Status {
        public static final int CONVERTED_TO_ORDER = -1;  // 已轉為訂單
        public static final int CLEARED = 0;              // 已清空
        public static final int ACTIVE = 1;               // 正常
    }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 構造函數 - 創建新購物車
     */
    public Cart(Long userId) {
        this.userId = userId;
        this.totalAmount = BigDecimal.ZERO;
        this.totalQuantity = 0;
        this.status = Status.ACTIVE;
    }
    
    /**
     * 計算購物車總金額和總數量
     */
    public void calculateTotals() {
        if (cartItems == null || cartItems.isEmpty()) {
            this.totalAmount = BigDecimal.ZERO;
            this.totalQuantity = 0;
            return;
        }
        
        this.totalAmount = cartItems.stream()
                .map(item -> item.getPrice().multiply(BigDecimal.valueOf(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        this.totalQuantity = cartItems.stream()
                .mapToInt(CartItem::getQuantity)
                .sum();
    }
}
