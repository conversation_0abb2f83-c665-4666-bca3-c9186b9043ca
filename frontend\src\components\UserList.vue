<template>
  <div class="user-list">
    <el-card v-for="user in users" :key="user.id" class="user-card">
      <div class="user-info">
        <div class="user-avatar">
          <el-avatar :size="50">{{ user.username.charAt(0).toUpperCase() }}</el-avatar>
        </div>
        <div class="user-details">
          <h4>{{ user.username }}</h4>
          <p class="user-email">{{ user.email }}</p>
          <p class="user-meta">
            註冊時間: {{ formatDate(user.createdAt) }}
          </p>
        </div>
        <div class="user-actions">
          <FollowButton 
            :userId="user.id" 
            @followChanged="handleFollowChanged(user.id, $event)"
          />
        </div>
      </div>
    </el-card>
    
    <div v-if="users.length === 0" class="empty-state">
      <el-empty description="暫無用戶數據" />
    </div>
    
    <div v-if="showPagination && totalCount > pageSize" class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="totalCount"
        layout="prev, pager, next, total"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import FollowButton from './FollowButton.vue'

interface User {
  id: number
  username: string
  email: string
  createdAt: string
}

interface Props {
  users: User[]
  totalCount?: number
  currentPage?: number
  pageSize?: number
  showPagination?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  totalCount: 0,
  currentPage: 1,
  pageSize: 20,
  showPagination: false
})

const emit = defineEmits<{
  pageChange: [page: number]
  followChanged: [userId: number, isFollowing: boolean]
}>()

const currentPage = ref(props.currentPage)

const handlePageChange = (page: number) => {
  currentPage.value = page
  emit('pageChange', page)
}

const handleFollowChanged = (userId: number, isFollowing: boolean) => {
  emit('followChanged', userId, isFollowing)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-TW')
}
</script>

<style scoped>
.user-list {
  max-width: 800px;
  margin: 0 auto;
}

.user-card {
  margin-bottom: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-avatar {
  flex-shrink: 0;
}

.user-details {
  flex: 1;
}

.user-details h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.user-email {
  margin: 0 0 4px 0;
  color: #666;
  font-size: 14px;
}

.user-meta {
  margin: 0;
  color: #999;
  font-size: 12px;
}

.user-actions {
  flex-shrink: 0;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}
</style>
