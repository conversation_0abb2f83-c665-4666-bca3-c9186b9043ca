package com.example.controller;

import com.example.dto.ApiResponse;
import com.example.entity.User;
import com.example.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/user")
@CrossOrigin(origins = "*")
@Slf4j
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 獲取當前用戶信息
     */
    @GetMapping("/profile")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getProfile(Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> userInfo = getUserInfo(user);
            
            return ResponseEntity.ok(ApiResponse.success(userInfo));
            
        } catch (Exception e) {
            log.error("獲取用戶信息失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("獲取用戶信息失敗"));
        }
    }
    
    /**
     * 更新用戶基本信息
     */
    @PutMapping("/profile")
    public ResponseEntity<ApiResponse<Map<String, Object>>> updateProfile(
            @RequestBody Map<String, String> updateData,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            
            // 更新允許修改的字段
            if (updateData.containsKey("realName")) {
                user.setRealName(updateData.get("realName"));
            }
            if (updateData.containsKey("phoneNumber")) {
                user.setPhoneNumber(updateData.get("phoneNumber"));
            }
            
            User updatedUser = userService.updateUser(user);
            Map<String, Object> userInfo = getUserInfo(updatedUser);
            
            log.info("用戶信息更新成功: {}", user.getUsername());
            return ResponseEntity.ok(ApiResponse.success("信息更新成功", userInfo));
            
        } catch (Exception e) {
            log.error("更新用戶信息失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("更新用戶信息失敗"));
        }
    }
    
    /**
     * 獲取用戶統計信息
     */
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserStats(Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("emailVerified", user.getEmailVerified());
            stats.put("identityVerified", user.getIdentityVerified());
            stats.put("identityStatus", user.getIdentityStatus());
            stats.put("accountCreated", user.getCreatedAt());
            stats.put("lastUpdated", user.getUpdatedAt());
            
            return ResponseEntity.ok(ApiResponse.success(stats));
            
        } catch (Exception e) {
            log.error("獲取用戶統計信息失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("獲取統計信息失敗"));
        }
    }
    
    /**
     * 獲取用戶信息（不包含敏感信息）
     */
    private Map<String, Object> getUserInfo(User user) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("username", user.getUsername());
        userInfo.put("email", user.getEmail());
        userInfo.put("realName", user.getRealName());
        userInfo.put("phoneNumber", user.getPhoneNumber());
        userInfo.put("emailVerified", user.getEmailVerified());
        userInfo.put("identityVerified", user.getIdentityVerified());
        userInfo.put("identityStatus", user.getIdentityStatus());
        userInfo.put("role", user.getRole());
        userInfo.put("createdAt", user.getCreatedAt());
        userInfo.put("updatedAt", user.getUpdatedAt());
        return userInfo;
    }
}
