<template>
  <div class="favorite-ranking">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon><TrendCharts /></el-icon>
            收藏排行榜
          </h1>
          <p class="page-subtitle">发现最受欢迎的精彩内容</p>
        </div>
        
        <div class="header-actions">
          <el-button @click="$router.push('/app/my-favorites')">
            <el-icon><Star /></el-icon>
            我的收藏
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filters-section">
      <div class="filters-content">
        <div class="filter-group">
          <label class="filter-label">类型筛选：</label>
          <el-select 
            v-model="selectedType" 
            @change="handleTypeChange" 
            placeholder="全部类型"
            style="width: 140px"
          >
            <el-option label="全部类型" value="" />
            <el-option label="文章" value="ARTICLE" />
            <el-option label="视频" value="VIDEO" />
            <el-option label="图片" value="IMAGE" />
            <el-option label="链接" value="LINK" />
          </el-select>
        </div>
        
        <div class="action-group">
          <el-button 
            @click="refreshRanking" 
            :loading="favoriteStore.loading"
            type="primary"
          >
            <el-icon><Refresh /></el-icon>
            刷新排行榜
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <div class="stats-cards">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><DataBoard /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ favoriteStore.rankingPage.totalElements }}</div>
            <div class="stat-label">热门内容</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><Star /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ getTotalFavorites() }}</div>
            <div class="stat-label">总收藏数</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="favoriteStore.loading && rankingItems.length === 0" class="loading-section">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 空状态 -->
    <div v-else-if="rankingItems.length === 0" class="empty-section">
      <el-empty 
        :image-size="120"
        description="暂无排行榜数据"
      >
        <template #image>
          <el-icon size="120" color="#c0c4cc"><TrendCharts /></el-icon>
        </template>
        <p>还没有内容被收藏，快去收藏一些精彩内容吧！</p>
      </el-empty>
    </div>

    <!-- 排行榜列表 -->
    <div v-else class="ranking-section">
      <div class="ranking-list">
        <div
          v-for="(item, index) in rankingItems"
          :key="item.id"
          class="ranking-item"
          :class="{ 'top-three': getRankNumber(index) <= 3 }"
        >
          <!-- 排名徽章 -->
          <div class="rank-badge" :class="`rank-${getRankNumber(index)}`">
            <span v-if="getRankNumber(index) <= 3" class="medal">
              {{ getRankNumber(index) === 1 ? '🥇' : getRankNumber(index) === 2 ? '🥈' : '🥉' }}
            </span>
            <span v-else class="rank-number">{{ getRankNumber(index) }}</span>
          </div>

          <!-- 内容卡片 -->
          <div class="content-card">
            <div class="card-header">
              <div class="title-section">
                <el-tag 
                  :type="getTypeTagType(item.type)" 
                  size="small"
                  effect="light"
                >
                  {{ getTypeLabel(item.type) }}
                </el-tag>
                <h3 class="content-title">
                  <a
                    v-if="item.url"
                    :href="item.url"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="title-link"
                  >
                    {{ item.title }}
                    <el-icon size="12"><Link /></el-icon>
                  </a>
                  <span v-else>{{ item.title }}</span>
                </h3>
              </div>
              
              <div class="stats-section">
                <div class="favorite-stats">
                  <el-icon color="#f56c6c"><Star /></el-icon>
                  <span class="count">{{ formatCount(item.favoriteCount) }}</span>
                </div>
                <div class="view-stats">
                  <el-icon color="#909399"><View /></el-icon>
                  <span class="count">{{ formatCount(item.viewCount) }}</span>
                </div>
              </div>
            </div>

            <div class="card-content">
              <div v-if="item.imageUrl" class="thumbnail">
                <el-image 
                  :src="item.imageUrl" 
                  :alt="item.title"
                  fit="cover"
                  lazy
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
              
              <div class="content-info">
                <p v-if="item.description" class="description">
                  {{ item.description }}
                </p>
                
                <div class="meta-info">
                  <div v-if="item.author" class="author">
                    <el-icon><User /></el-icon>
                    {{ item.author }}
                  </div>
                  <div class="created-date">
                    <el-icon><Calendar /></el-icon>
                    {{ formatDate(item.createdAt) }}
                  </div>
                </div>
                
                <div v-if="item.tags && item.tags.length" class="tags">
                  <el-tag 
                    v-for="tag in item.tags.slice(0, 3)" 
                    :key="tag"
                    size="small"
                    type="info"
                    effect="plain"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
            </div>

            <div class="card-actions">
              <FavoriteButton
                :item-id="item.id"
                :show-count="true"
                :show-text="false"
                size="small"
                @favorite-changed="handleFavoriteChanged"
              />
              
              <el-button
                v-if="item.url"
                size="small"
                type="primary"
                @click="openLink(item.url)"
              >
                <el-icon><View /></el-icon>
                查看内容
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div v-if="favoriteStore.hasMoreRanking" class="load-more-section">
        <el-button
          @click="loadMore"
          :loading="favoriteStore.loading"
          size="large"
          style="width: 200px"
        >
          {{ favoriteStore.loading ? '加载中...' : '加载更多' }}
        </el-button>
      </div>
    </div>

    <!-- 错误提示 -->
    <el-alert
      v-if="favoriteStore.error"
      :title="favoriteStore.error"
      type="error"
      :closable="true"
      @close="favoriteStore.clearError"
      style="margin-top: 20px"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useFavoriteStore } from '@/stores/favorite'
import FavoriteButton from '@/components/FavoriteButton.vue'

const favoriteStore = useFavoriteStore()

const selectedType = ref('')

// 計算屬性
const rankingItems = computed(() => favoriteStore.favoriteRanking)

// 方法
const handleTypeChange = async () => {
  await favoriteStore.loadFavoriteRanking(0, 20, selectedType.value || undefined)
}

const refreshRanking = async () => {
  await favoriteStore.loadFavoriteRanking(0, 20, selectedType.value || undefined)
}

const loadMore = async () => {
  const nextPage = favoriteStore.rankingPage.current + 1
  await favoriteStore.loadFavoriteRanking(
    nextPage,
    20,
    selectedType.value || undefined,
    true // append
  )
}

const handleFavoriteChanged = (itemId: number, isFavorited: boolean) => {
  // FavoriteButton組件會自動更新store中的狀態
  // 這裡不需要額外處理，因為排行榜數據會自動同步
}

const openLink = (url: string) => {
  window.open(url, '_blank', 'noopener,noreferrer')
}

const getTotalFavorites = (): number => {
  return rankingItems.value.reduce((total, item) => total + item.favoriteCount, 0)
}

const getRankNumber = (index: number): number => {
  return favoriteStore.rankingPage.current * favoriteStore.rankingPage.size + index + 1
}

const getTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    ARTICLE: '文章',
    VIDEO: '视频',
    IMAGE: '图片',
    LINK: '链接',
    OTHER: '其他'
  }
  return labels[type] || type
}

const getTypeTagType = (type: string): string => {
  const typeMap: Record<string, string> = {
    ARTICLE: 'success',
    VIDEO: 'danger',
    IMAGE: 'warning',
    LINK: 'primary',
    OTHER: 'info'
  }
  return typeMap[type] || 'info'
}

const formatCount = (count: number): string => {
  if (count < 1000) return count.toString()
  if (count < 10000) return (count / 1000).toFixed(1) + 'k'
  if (count < 1000000) return Math.floor(count / 1000) + 'k'
  return (count / 1000000).toFixed(1) + 'M'
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days < 1) {
    return '今天'
  } else if (days < 7) {
    return `${days}天前`
  } else if (days < 30) {
    return `${Math.floor(days / 7)}週前`
  } else {
    return date.toLocaleDateString('zh-TW')
  }
}

// 生命週期
onMounted(async () => {
  await favoriteStore.loadFavoriteRanking()
})
</script>

<style scoped>
.favorite-ranking {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
}

/* 页面头部 */
.page-header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.title-section {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 32px;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 8px 0;
}

.page-title .el-icon {
  color: #667eea;
}

.page-subtitle {
  color: #718096;
  margin: 0;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 筛选区域 */
.filters-section {
  max-width: 1200px;
  margin: 0 auto 24px auto;
  padding: 0 24px;
}

.filters-content {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-label {
  font-weight: 500;
  color: #4a5568;
  white-space: nowrap;
}

.action-group {
  display: flex;
  gap: 12px;
}

/* 统计信息 */
.stats-section {
  max-width: 1200px;
  margin: 0 auto 24px auto;
  padding: 0 24px;
}

.stats-cards {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 160px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #718096;
}

/* 加载和空状态 */
.loading-section,
.empty-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.loading-section {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.empty-section {
  background: white;
  border-radius: 16px;
  padding: 60px 40px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  text-align: center;
}

/* 排行榜列表 */
.ranking-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 32px;
}

.ranking-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.ranking-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #e2e8f0, #e2e8f0);
}

.ranking-item.top-three::before {
  background: linear-gradient(90deg, #ffd700, #ffed4e);
}

.ranking-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

/* 排名徽章 */
.rank-badge {
  flex-shrink: 0;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  font-weight: 700;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.rank-1 {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
}

.rank-2 {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #333;
}

.rank-3 {
  background: linear-gradient(135deg, #cd7f32, #daa520);
  color: white;
}

.rank-badge:not(.rank-1):not(.rank-2):not(.rank-3) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.medal {
  font-size: 24px;
}

.rank-number {
  font-size: 20px;
}

/* 内容卡片 */
.content-card {
  flex: 1;
  min-width: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 20px;
}

.title-section {
  flex: 1;
  min-width: 0;
}

.content-title {
  margin: 8px 0 0 0;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.4;
  color: #2d3748;
}

.title-link {
  color: #2d3748;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: color 0.2s ease;
}

.title-link:hover {
  color: #667eea;
}

.stats-section {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-shrink: 0;
}

.favorite-stats,
.view-stats {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
}

.count {
  color: #4a5568;
}

.card-content {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.thumbnail {
  flex-shrink: 0;
  width: 100px;
  height: 100px;
  border-radius: 12px;
  overflow: hidden;
  background: #f7fafc;
}

.thumbnail .el-image {
  width: 100%;
  height: 100%;
}

.image-error {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #cbd5e0;
  font-size: 32px;
}

.content-info {
  flex: 1;
  min-width: 0;
}

.description {
  margin: 0 0 16px 0;
  font-size: 15px;
  color: #718096;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.meta-info {
  display: flex;
  gap: 20px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.author,
.created-date {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #a0aec0;
}

.tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

/* 加载更多 */
.load-more-section {
  text-align: center;
  margin: 32px 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    padding: 24px 20px;
  }
  
  .filters-section,
  .stats-section,
  .ranking-section {
    padding: 0 20px;
  }
}

@media (max-width: 768px) {
  .page-title {
    font-size: 28px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  
  .filters-content {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .stats-cards {
    justify-content: center;
  }
  
  .ranking-item {
    flex-direction: column;
    gap: 16px;
  }
  
  .rank-badge {
    align-self: flex-start;
  }
  
  .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .stats-section {
    justify-content: flex-start;
  }
  
  .card-content {
    flex-direction: column;
    gap: 12px;
  }
  
  .thumbnail {
    width: 100%;
    height: 120px;
  }
}

@media (max-width: 480px) {
  .header-content,
  .filters-section,
  .stats-section,
  .ranking-section {
    padding: 0 16px;
  }
  
  .filters-content,
  .ranking-item {
    padding: 16px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .rank-badge {
    width: 48px;
    height: 48px;
    font-size: 16px;
  }
  
  .medal {
    font-size: 20px;
  }
}
</style>
