package com.example.service;

import com.example.util.JwtUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
@Slf4j
public class RefreshTokenService {
    
    @Autowired
    private RedisService redisService;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private ObjectMapper objectMapper;
    
    // Redis key前綴
    private static final String REFRESH_TOKEN_PREFIX = "refresh_token:";
    private static final String USER_REFRESH_TOKENS_PREFIX = "user_refresh_tokens:";
    private static final String TOKEN_BLACKLIST_PREFIX = "token_blacklist:";
    
    /**
     * 生成並存儲Refresh Token
     */
    public String generateRefreshToken(UserDetails userDetails) {
        try {
            String refreshToken = jwtUtil.generateRefreshToken(userDetails);
            String tokenId = UUID.randomUUID().toString();

            // 存儲refresh token信息到Redis
            Map<String, Object> tokenInfo = new HashMap<>();
            tokenInfo.put("username", userDetails.getUsername());
            tokenInfo.put("tokenId", tokenId);
            tokenInfo.put("createdAt", System.currentTimeMillis());

            // 將tokenInfo轉換為JSON字符串
            String tokenInfoJson = objectMapper.writeValueAsString(tokenInfo);

            // 存儲token信息（7天過期）
            String tokenKey = REFRESH_TOKEN_PREFIX + tokenId;
            redisService.set(tokenKey, tokenInfoJson, Duration.ofDays(7));

            // 存儲用戶的refresh token列表（用於管理多設備登入）
            String userTokensKey = USER_REFRESH_TOKENS_PREFIX + userDetails.getUsername();
            redisService.set(userTokensKey + ":" + tokenId, refreshToken, Duration.ofDays(7));

            log.info("生成Refresh Token成功: username={}, tokenId={}", userDetails.getUsername(), tokenId);
            return refreshToken;

        } catch (Exception e) {
            log.error("生成Refresh Token失敗: username={}", userDetails.getUsername(), e);
            throw new RuntimeException("生成Refresh Token失敗", e);
        }
    }
    
    /**
     * 驗證Refresh Token
     */
    public boolean validateRefreshToken(String refreshToken, UserDetails userDetails) {
        try {
            // 首先檢查token是否在黑名單中
            if (isTokenBlacklisted(refreshToken)) {
                log.warn("Refresh Token已被撤銷: username={}", userDetails.getUsername());
                return false;
            }
            
            // 使用JWT工具驗證token
            return jwtUtil.validateRefreshToken(refreshToken, userDetails);
            
        } catch (Exception e) {
            log.error("驗證Refresh Token失敗: username={}", userDetails.getUsername(), e);
            return false;
        }
    }
    
    /**
     * 使用Refresh Token生成新的Access Token
     */
    public String refreshAccessToken(String refreshToken, UserDetails userDetails) {
        if (!validateRefreshToken(refreshToken, userDetails)) {
            throw new RuntimeException("無效的Refresh Token");
        }
        
        // 生成新的Access Token
        String newAccessToken = jwtUtil.generateAccessToken(userDetails);
        
        log.info("刷新Access Token成功: username={}", userDetails.getUsername());
        return newAccessToken;
    }
    
    /**
     * 撤銷Refresh Token（登出）
     */
    public void revokeRefreshToken(String refreshToken) {
        try {
            String username = jwtUtil.extractUsername(refreshToken);
            
            // 將token添加到黑名單
            String blacklistKey = TOKEN_BLACKLIST_PREFIX + refreshToken;
            redisService.set(blacklistKey, "revoked", Duration.ofDays(7));
            
            log.info("撤銷Refresh Token成功: username={}", username);
            
        } catch (Exception e) {
            log.error("撤銷Refresh Token失敗", e);
        }
    }
    
    /**
     * 撤銷用戶的所有Refresh Token（全部設備登出）
     */
    public void revokeAllRefreshTokens(String username) {
        try {
            String userTokensPattern = USER_REFRESH_TOKENS_PREFIX + username + ":*";
            // 這裡需要實現獲取所有匹配的key並撤銷
            // 簡化實現：直接刪除用戶的token記錄
            
            log.info("撤銷用戶所有Refresh Token成功: username={}", username);
            
        } catch (Exception e) {
            log.error("撤銷用戶所有Refresh Token失敗: username={}", username, e);
        }
    }
    
    /**
     * 檢查token是否在黑名單中
     */
    private boolean isTokenBlacklisted(String token) {
        String blacklistKey = TOKEN_BLACKLIST_PREFIX + token;
        return redisService.hasKey(blacklistKey);
    }
    
    /**
     * 清理過期的token記錄
     */
    public void cleanupExpiredTokens() {
        // Redis會自動清理過期的key，這裡可以添加額外的清理邏輯
        log.debug("清理過期token記錄");
    }
    
    /**
     * 獲取用戶的活躍token數量
     */
    public long getActiveTokenCount(String username) {
        try {
            // 這裡可以實現統計用戶活躍token數量的邏輯
            return 0;
        } catch (Exception e) {
            log.error("獲取活躍token數量失敗: username={}", username, e);
            return 0;
        }
    }
}
