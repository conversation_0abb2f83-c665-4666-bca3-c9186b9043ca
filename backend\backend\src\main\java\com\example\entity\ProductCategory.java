package com.example.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商品分類實體類
 * 支持樹形結構，類似圖書分類系統
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Entity
@Table(name = "product_categories")
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class ProductCategory {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 分類名稱
     */
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    /**
     * 父分類ID，0表示根分類
     */
    @Column(name = "parent_id", nullable = false)
    private Long parentId = 0L;
    
    /**
     * 分類層級，從1開始
     */
    @Column(name = "level", nullable = false)
    private Integer level = 1;
    
    /**
     * 排序號，數字越小越靠前
     */
    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;
    
    /**
     * 分類狀態：1-啟用，0-禁用
     */
    @Column(name = "status", nullable = false)
    private Integer status = 1;
    
    /**
     * 分類描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    /**
     * 分類圖標URL
     */
    @Column(name = "icon_url")
    private String iconUrl;
    
    /**
     * 是否為葉子節點：1-是，0-否
     */
    @Column(name = "is_leaf", nullable = false)
    private Integer isLeaf = 1;
    
    /**
     * 創建時間
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    /**
     * 更新時間
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    /**
     * 創建者ID
     */
    @Column(name = "created_by")
    private Long createdBy;
    
    // 關聯關係 - 父分類
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", insertable = false, updatable = false)
    @JsonIgnore
    private ProductCategory parent;
    
    // 關聯關係 - 子分類列表
    @OneToMany(mappedBy = "parent", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<ProductCategory> children;
    
    // 關聯關係 - 創建者
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by", insertable = false, updatable = false)
    @JsonIgnore
    private User creator;
    
    /**
     * 分類狀態枚舉
     */
    public static class Status {
        public static final int DISABLED = 0;  // 禁用
        public static final int ENABLED = 1;   // 啟用
    }
    
    /**
     * 葉子節點標識
     */
    public static class LeafFlag {
        public static final int NOT_LEAF = 0;  // 非葉子節點
        public static final int IS_LEAF = 1;   // 葉子節點
    }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 構造函數 - 創建新分類
     */
    public ProductCategory(String name, Long parentId, Integer level, Long createdBy) {
        this.name = name;
        this.parentId = parentId;
        this.level = level;
        this.createdBy = createdBy;
        this.status = Status.ENABLED;
        this.isLeaf = LeafFlag.IS_LEAF;
        this.sortOrder = 0;
    }
}
