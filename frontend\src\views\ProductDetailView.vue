<template>
  <div class="product-detail-view">
    <div class="page-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首頁</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/products' }">商品列表</el-breadcrumb-item>
        <el-breadcrumb-item>商品詳情</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    
    <div class="page-content">
      <ProductDetail 
        :product-id="productId"
        @product-select="handleProductSelect"
        @buy-now="handleBuyNow"
        @add-to-cart="handleAddToCart"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import ProductDetail from '../components/ProductDetail.vue'
import { useCartStore } from '../stores/cart'
import type { Product } from '../api/product'

// Router
const route = useRoute()
const router = useRouter()
const cartStore = useCartStore()

// 計算屬性
const productId = computed(() => {
  const id = route.params.id
  console.log('ProductDetailView - route.params.id:', id)
  const parsedId = typeof id === 'string' ? parseInt(id, 10) : 0
  console.log('ProductDetailView - parsedId:', parsedId)
  return parsedId
})

// 處理商品選擇
const handleProductSelect = (product: Product) => {
  router.push(`/app/products/${product.id}`)
}

// 處理立即購買
const handleBuyNow = async (product: Product, quantity: number) => {
  try {
    ElMessage.info(`立即購買 ${product.name} x ${quantity}`)

    // 創建直接訂單
    const orderData = {
      productId: product.id,
      quantity: quantity,
      receiverName: '測試用戶', // 這裡應該從用戶信息獲取
      receiverPhone: '13800138000',
      receiverAddress: '測試地址'
    }

    // 使用 URLSearchParams 發送表單數據，因為後端期望的是表單參數
    const formData = new URLSearchParams()
    formData.append('productId', product.id.toString())
    formData.append('quantity', quantity.toString())
    formData.append('receiverName', '測試用戶')
    formData.append('receiverPhone', '13800138000')
    formData.append('receiverAddress', '測試地址')

    const response = await fetch('/api/orders/create-direct', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: formData
    })

    const result = await response.json()

    if (result.success) {
      ElMessage.success('訂單創建成功，正在跳轉到支付頁面...')
      // 跳轉到支付頁面
      router.push(`/app/payment/${result.data.id}`)
    } else {
      ElMessage.error(result.message || '創建訂單失敗')
    }
  } catch (error) {
    console.error('立即購買失敗:', error)
    ElMessage.error('操作失敗，請重試')
  }
}

// 處理加入購物車
const handleAddToCart = async (product: Product, quantity: number) => {
  await cartStore.addToCart(product.id, quantity)
}

// 組件掛載時檢查商品ID
onMounted(() => {
  if (!productId.value) {
    ElMessage.error('無效的商品ID')
    router.push('/app/products')
  }
})
</script>

<style scoped>
.product-detail-view {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}
</style>
