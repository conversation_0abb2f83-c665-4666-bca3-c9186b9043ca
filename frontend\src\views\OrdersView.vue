<template>
  <div class="orders-view">
    <div class="container">
      <h1 class="page-title">我的訂單</h1>
      
      <!-- 訂單篩選 -->
      <div class="order-filters">
        <button 
          v-for="filter in filters" 
          :key="filter.value"
          class="filter-btn"
          :class="{ active: currentFilter === filter.value }"
          @click="changeFilter(filter.value)"
        >
          {{ filter.label }}
        </button>
      </div>
      
      <!-- 訂單列表 -->
      <div v-if="loading" class="loading">
        <div class="loading-spinner"></div>
        <p>加載中...</p>
      </div>
      
      <div v-else-if="orders.length === 0" class="empty-orders">
        <div class="empty-icon">📋</div>
        <p>暫無訂單</p>
        <router-link to="/products" class="btn btn-primary">去購物</router-link>
      </div>
      
      <div v-else class="orders-list">
        <div 
          v-for="order in orders" 
          :key="order.id" 
          class="order-card"
        >
          <div class="order-header">
            <div class="order-info">
              <span class="order-number">訂單號：{{ order.orderNumber }}</span>
              <span class="order-date">{{ formatDate(order.createdAt) }}</span>
            </div>
            <div class="order-status" :class="getStatusClass(order.status)">
              {{ getStatusText(order.status) }}
            </div>
          </div>
          
          <div class="order-items">
            <div 
              v-for="item in order.orderItems" 
              :key="item.id"
              class="order-item"
            >
              <div class="item-image">
                <img :src="item.productImageUrl || '/default-product.jpg'" :alt="item.productName" />
              </div>
              <div class="item-info">
                <h3 class="item-name">{{ item.productName }}</h3>
                <p class="item-specs">{{ item.productBrand }} {{ item.productModel }}</p>
                <p class="item-price">¥{{ item.unitPrice }} x {{ item.quantity }}</p>
              </div>
              <div class="item-subtotal">
                ¥{{ item.subtotal }}
              </div>
            </div>
          </div>
          
          <div class="order-footer">
            <div class="order-total">
              <span>實付款：</span>
              <span class="total-amount">¥{{ order.totalAmount }}</span>
            </div>
            <div class="order-actions">
              <button 
                v-if="order.status === 0" 
                class="btn btn-primary btn-sm"
                @click="goToPayment(order.id)"
              >
                立即支付
              </button>
              <button 
                v-if="order.status === 0" 
                class="btn btn-secondary btn-sm"
                @click="cancelOrder(order)"
              >
                取消訂單
              </button>
              <button 
                v-if="order.status === 2" 
                class="btn btn-primary btn-sm"
                @click="confirmReceipt(order)"
              >
                確認收貨
              </button>
              <button 
                class="btn btn-outline btn-sm"
                @click="viewOrderDetail(order.id)"
              >
                查看詳情
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 分頁 -->
      <div v-if="totalPages > 1" class="pagination">
        <button 
          class="page-btn"
          :disabled="currentPage === 0"
          @click="changePage(currentPage - 1)"
        >
          上一頁
        </button>
        
        <span class="page-info">
          第 {{ currentPage + 1 }} 頁，共 {{ totalPages }} 頁
        </span>
        
        <button 
          class="page-btn"
          :disabled="currentPage >= totalPages - 1"
          @click="changePage(currentPage + 1)"
        >
          下一頁
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

interface OrderItem {
  id: number
  productId: number
  productName: string
  productImageUrl: string
  productBrand: string
  productModel: string
  quantity: number
  unitPrice: number
  subtotal: number
}

interface Order {
  id: number
  orderNumber: string
  totalAmount: number
  status: number
  createdAt: string
  orderItems: OrderItem[]
}

const router = useRouter()

const orders = ref<Order[]>([])
const loading = ref(false)
const currentFilter = ref('all')
const currentPage = ref(0)
const totalPages = ref(0)
const pageSize = 10

const filters = [
  { label: '全部', value: 'all' },
  { label: '待付款', value: '0' },
  { label: '已付款', value: '1' },
  { label: '已發貨', value: '2' },
  { label: '已完成', value: '3' },
  { label: '已取消', value: '-1' }
]

// 方法
const loadOrders = async () => {
  try {
    loading.value = true
    
    let url = `/api/orders?page=${currentPage.value}&size=${pageSize}`
    if (currentFilter.value !== 'all') {
      url += `&status=${currentFilter.value}`
    }
    
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data) {
        orders.value = result.data.content
        totalPages.value = result.data.totalPages
      }
    } else {
      ElMessage.error('加載訂單失敗')
    }
  } catch (error) {
    console.error('加載訂單失敗:', error)
    ElMessage.error('加載訂單失敗')
  } finally {
    loading.value = false
  }
}

const changeFilter = (filter: string) => {
  currentFilter.value = filter
  currentPage.value = 0
  loadOrders()
}

const changePage = (page: number) => {
  currentPage.value = page
  loadOrders()
}

const goToPayment = (orderId: number) => {
  router.push(`/app/payment/${orderId}`)
}

const cancelOrder = async (order: Order) => {
  try {
    await ElMessageBox.confirm('確定要取消這個訂單嗎？', '確認取消', {
      type: 'warning'
    })
    
    const response = await fetch(`/api/orders/${order.id}/cancel`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (response.ok) {
      ElMessage.success('訂單已取消')
      loadOrders()
    } else {
      ElMessage.error('取消訂單失敗')
    }
  } catch (error) {
    // 用戶取消操作
  }
}

const confirmReceipt = async (order: Order) => {
  try {
    await ElMessageBox.confirm('確認已收到商品嗎？', '確認收貨', {
      type: 'info'
    })
    
    const response = await fetch(`/api/orders/${order.id}/confirm`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (response.ok) {
      ElMessage.success('確認收貨成功')
      loadOrders()
    } else {
      ElMessage.error('確認收貨失敗')
    }
  } catch (error) {
    // 用戶取消操作
  }
}

const viewOrderDetail = (orderId: number) => {
  router.push(`/app/orders/${orderId}`)
}

const getStatusClass = (status: number) => {
  switch (status) {
    case 0: return 'pending'
    case 1: return 'paid'
    case 2: return 'shipped'
    case 3: return 'completed'
    case -1: return 'cancelled'
    default: return ''
  }
}

const getStatusText = (status: number) => {
  switch (status) {
    case 0: return '待付款'
    case 1: return '已付款'
    case 2: return '已發貨'
    case 3: return '已完成'
    case -1: return '已取消'
    default: return '未知狀態'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  loadOrders()
})
</script>

<style scoped>
.orders-view {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-title {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.order-filters {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-btn:hover {
  border-color: #007bff;
}

.filter-btn.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.loading {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.empty-orders {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.order-card {
  background: white;
  border-radius: 8px;
  margin-bottom: 20px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.order-number {
  font-weight: bold;
  margin-right: 20px;
}

.order-date {
  color: #666;
  font-size: 14px;
}

.order-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.order-status.pending {
  background-color: #fff3cd;
  color: #856404;
}

.order-status.paid {
  background-color: #d4edda;
  color: #155724;
}

.order-status.shipped {
  background-color: #cce5ff;
  color: #004085;
}

.order-status.completed {
  background-color: #d1ecf1;
  color: #0c5460;
}

.order-status.cancelled {
  background-color: #f8d7da;
  color: #721c24;
}

.order-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
}

.item-image img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}

.item-info {
  flex: 1;
  margin-left: 15px;
}

.item-name {
  font-size: 16px;
  margin-bottom: 5px;
}

.item-specs {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.item-price {
  color: #999;
  font-size: 14px;
}

.item-subtotal {
  font-weight: bold;
  color: #e74c3c;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
}

.total-amount {
  font-size: 18px;
  font-weight: bold;
  color: #e74c3c;
}

.order-actions {
  display: flex;
  gap: 10px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
}

.page-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  font-size: 12px;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-outline {
  background-color: transparent;
  color: #007bff;
  border: 1px solid #007bff;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}
</style>
