package com.example.controller;

import com.example.dto.ApiResponse;
import com.example.dto.LoginRequest;
import com.example.entity.Admin;
import com.example.service.AdminService;
import com.example.util.JwtUtil;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/auth")
@CrossOrigin(origins = "*")
@Slf4j
public class AdminAuthController {
    
    @Autowired
    private AuthenticationManager authenticationManager;
    
    @Autowired
    private AdminService adminService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 管理員登入
     */
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<Map<String, Object>>> login(@Valid @RequestBody LoginRequest request) {
        try {
            // 認證管理員
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword())
            );
            
            Admin admin = (Admin) authentication.getPrincipal();
            
            // 檢查是否為管理員
            if (!admin.getAuthorities().stream()
                    .anyMatch(auth -> auth.getAuthority().equals("ROLE_ADMIN") || auth.getAuthority().equals("ROLE_SUPER_ADMIN"))) {
                return ResponseEntity.badRequest().body(ApiResponse.error("無管理員權限"));
            }
            
            // 生成 JWT Token
            String token = jwtUtil.generateToken(admin);
            
            // 更新最後登入時間
            adminService.updateLastLoginTime(admin.getUsername());
            
            // 返回管理員信息和 Token
            Map<String, Object> data = new HashMap<>();
            data.put("token", token);
            data.put("admin", getAdminInfo(admin));
            
            log.info("管理員登入成功: {}", admin.getUsername());
            return ResponseEntity.ok(ApiResponse.success("登入成功", data));
            
        } catch (AuthenticationException e) {
            log.warn("管理員登入失敗: {}", request.getUsername());
            return ResponseEntity.badRequest().body(ApiResponse.error("用戶名或密碼錯誤"));
        }
    }
    
    /**
     * 獲取當前管理員信息
     */
    @GetMapping("/profile")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getProfile(Authentication authentication) {
        try {
            Admin admin = (Admin) authentication.getPrincipal();
            Map<String, Object> adminInfo = getAdminInfo(admin);
            
            return ResponseEntity.ok(ApiResponse.success(adminInfo));
            
        } catch (Exception e) {
            log.error("獲取管理員信息失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("獲取管理員信息失敗"));
        }
    }
    
    /**
     * 管理員登出
     */
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<String>> logout(Authentication authentication) {
        try {
            Admin admin = (Admin) authentication.getPrincipal();
            log.info("管理員登出: {}", admin.getUsername());
            
            return ResponseEntity.ok(ApiResponse.success("登出成功"));
            
        } catch (Exception e) {
            log.error("管理員登出失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("登出失敗"));
        }
    }
    
    /**
     * 轉換管理員信息為 Map
     */
    private Map<String, Object> getAdminInfo(Admin admin) {
        Map<String, Object> adminInfo = new HashMap<>();
        adminInfo.put("id", admin.getId());
        adminInfo.put("username", admin.getUsername());
        adminInfo.put("email", admin.getEmail());
        adminInfo.put("realName", admin.getRealName());
        adminInfo.put("phoneNumber", admin.getPhoneNumber());
        adminInfo.put("role", admin.getRole());
        adminInfo.put("enabled", admin.isEnabled());
        adminInfo.put("lastLoginAt", admin.getLastLoginAt());
        adminInfo.put("createdAt", admin.getCreatedAt());
        
        return adminInfo;
    }
}
