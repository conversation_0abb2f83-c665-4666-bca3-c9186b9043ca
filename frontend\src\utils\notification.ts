import { ElMessage, ElNotification } from 'element-plus'
import { parseError, type ErrorInfo } from './errorHandler'

/**
 * 通知工具类
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */

// 通知类型
export type NotificationType = 'success' | 'warning' | 'info' | 'error'

// 通知选项
export interface NotificationOptions {
  title?: string
  message: string
  type?: NotificationType
  duration?: number
  showClose?: boolean
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
}

// 消息选项
export interface MessageOptions {
  message: string
  type?: NotificationType
  duration?: number
  showClose?: boolean
  center?: boolean
}

/**
 * 显示成功消息
 */
export function showSuccessMessage(message: string, duration: number = 3000) {
  ElMessage({
    message,
    type: 'success',
    duration,
    showClose: true
  })
}

/**
 * 显示错误消息
 */
export function showErrorMessage(error: any, duration: number = 5000) {
  const errorInfo = parseError(error)
  ElMessage({
    message: errorInfo.message,
    type: 'error',
    duration,
    showClose: true
  })
}

/**
 * 显示警告消息
 */
export function showWarningMessage(message: string, duration: number = 4000) {
  ElMessage({
    message,
    type: 'warning',
    duration,
    showClose: true
  })
}

/**
 * 显示信息消息
 */
export function showInfoMessage(message: string, duration: number = 3000) {
  ElMessage({
    message,
    type: 'info',
    duration,
    showClose: true
  })
}

/**
 * 显示自定义消息
 */
export function showMessage(options: MessageOptions) {
  ElMessage({
    message: options.message,
    type: options.type || 'info',
    duration: options.duration || 3000,
    showClose: options.showClose !== false,
    center: options.center || false
  })
}

/**
 * 显示成功通知
 */
export function showSuccessNotification(title: string, message: string, duration: number = 4500) {
  ElNotification({
    title,
    message,
    type: 'success',
    duration,
    position: 'top-right'
  })
}

/**
 * 显示错误通知
 */
export function showErrorNotification(title: string, error: any, duration: number = 6000) {
  const errorInfo = parseError(error)
  ElNotification({
    title,
    message: errorInfo.message,
    type: 'error',
    duration,
    position: 'top-right'
  })
}

/**
 * 显示警告通知
 */
export function showWarningNotification(title: string, message: string, duration: number = 5000) {
  ElNotification({
    title,
    message,
    type: 'warning',
    duration,
    position: 'top-right'
  })
}

/**
 * 显示信息通知
 */
export function showInfoNotification(title: string, message: string, duration: number = 4000) {
  ElNotification({
    title,
    message,
    type: 'info',
    duration,
    position: 'top-right'
  })
}

/**
 * 显示自定义通知
 */
export function showNotification(options: NotificationOptions) {
  ElNotification({
    title: options.title,
    message: options.message,
    type: options.type || 'info',
    duration: options.duration || 4500,
    showClose: options.showClose !== false,
    position: options.position || 'top-right'
  })
}

/**
 * 收藏操作相关的通知方法
 */
export const favoriteNotifications = {
  /**
   * 收藏成功
   */
  addSuccess: (itemTitle?: string) => {
    const message = itemTitle ? `已收藏「${itemTitle}」` : '收藏成功'
    showSuccessMessage(message)
  },

  /**
   * 取消收藏成功
   */
  removeSuccess: (itemTitle?: string) => {
    const message = itemTitle ? `已取消收藏「${itemTitle}」` : '取消收藏成功'
    showSuccessMessage(message)
  },

  /**
   * 收藏失败
   */
  addError: (error: any, itemTitle?: string) => {
    const title = itemTitle ? `收藏「${itemTitle}」失败` : '收藏失败'
    showErrorNotification(title, error)
  },

  /**
   * 取消收藏失败
   */
  removeError: (error: any, itemTitle?: string) => {
    const title = itemTitle ? `取消收藏「${itemTitle}」失败` : '取消收藏失败'
    showErrorNotification(title, error)
  },

  /**
   * 重复收藏提示
   */
  duplicateWarning: (itemTitle?: string) => {
    const message = itemTitle ? `「${itemTitle}」已经在收藏夹中` : '已经收藏过该内容'
    showWarningMessage(message)
  },

  /**
   * 操作频率限制提示
   */
  rateLimitWarning: () => {
    showWarningMessage('操作过于频繁，请稍后再试')
  },

  /**
   * 需要登录提示
   */
  loginRequired: () => {
    showWarningMessage('请先登录后再进行收藏操作')
  },

  /**
   * 加载失败
   */
  loadError: (error: any, type: string = '数据') => {
    showErrorNotification(`加载${type}失败`, error)
  }
}

/**
 * 清除所有消息
 */
export function clearAllMessages() {
  ElMessage.closeAll()
}

/**
 * 清除所有通知
 */
export function clearAllNotifications() {
  // Element Plus 没有提供清除所有通知的方法
  // 可以通过设置较短的持续时间来快速清除
}
