package com.example.service;

import com.example.dto.ApiResponse;
import com.example.dto.PagedResponse;
import com.example.entity.*;
import com.example.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * 訂單服務類
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Slf4j
@Service
@Transactional
public class OrderService {
    
    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private OrderItemRepository orderItemRepository;
    
    @Autowired
    private CartRepository cartRepository;
    
    @Autowired
    private CartItemRepository cartItemRepository;
    
    @Autowired
    private ProductRepository productRepository;
    
    @Autowired
    private PaymentRepository paymentRepository;
    
    /**
     * 從購物車創建訂單
     */
    public ApiResponse<Order> createOrderFromCart(Long userId, String receiverName, String receiverPhone,
                                                 String receiverAddress, String remark) {
        try {
            log.info("開始創建訂單: userId={}, receiverName={}, receiverPhone={}, receiverAddress={}",
                    userId, receiverName, receiverPhone, receiverAddress);

            // 獲取用戶購物車
            Optional<Cart> cartOpt = cartRepository.findByUserIdAndStatus(userId, Cart.Status.ACTIVE);
            if (cartOpt.isEmpty()) {
                log.warn("用戶購物車為空: userId={}", userId);
                return ApiResponse.error("購物車為空");
            }

            Cart cart = cartOpt.get();
            log.info("找到購物車: cartId={}, userId={}", cart.getId(), cart.getUserId());

            List<CartItem> selectedItems = cartItemRepository.findSelectedItemsByCartIdWithProduct(cart.getId());
            log.info("購物車選中項目數量: {}", selectedItems.size());

            if (selectedItems.isEmpty()) {
                log.warn("購物車中沒有選中的商品: cartId={}", cart.getId());
                return ApiResponse.error("請選擇要購買的商品");
            }
            
            // 檢查庫存
            for (CartItem item : selectedItems) {
                Product product = item.getProduct();
                log.info("檢查商品庫存: productId={}, productName={}, stock={}, requestQuantity={}",
                        item.getProductId(), item.getProductName(),
                        product != null ? product.getStock() : "null", item.getQuantity());

                if (product == null || product.getStatus() != Product.Status.ON_SHELF) {
                    log.warn("商品已下架: productId={}, productName={}", item.getProductId(), item.getProductName());
                    return ApiResponse.error("商品 " + item.getProductName() + " 已下架");
                }
                if (product.getStock() < item.getQuantity()) {
                    log.warn("商品庫存不足: productId={}, stock={}, requestQuantity={}",
                            item.getProductId(), product.getStock(), item.getQuantity());
                    return ApiResponse.error("商品 " + item.getProductName() + " 庫存不足");
                }
            }

            // 計算總金額
            BigDecimal totalAmount = selectedItems.stream()
                    .map(item -> item.getPrice().multiply(BigDecimal.valueOf(item.getQuantity())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            log.info("計算訂單總金額: {}", totalAmount);

            // 生成訂單號
            String orderNumber = generateOrderNumber();
            log.info("生成訂單號: {}", orderNumber);

            // 創建訂單
            Order order = new Order(orderNumber, userId, totalAmount, receiverName, receiverPhone, receiverAddress);
            if (remark != null && !remark.trim().isEmpty()) {
                order.setRemark(remark);
            }
            log.info("準備保存訂單: orderNumber={}, userId={}, totalAmount={}", orderNumber, userId, totalAmount);
            Order savedOrder = orderRepository.save(order);
            log.info("訂單保存成功: orderId={}, orderNumber={}", savedOrder.getId(), savedOrder.getOrderNumber());
            
            // 創建訂單項目
            log.info("開始創建訂單項目，共{}個商品", selectedItems.size());
            for (CartItem cartItem : selectedItems) {
                Product product = cartItem.getProduct();
                OrderItem orderItem = new OrderItem(
                    savedOrder.getId(),
                    cartItem.getProductId(),
                    cartItem.getProductName(),
                    cartItem.getProductImageUrl(),
                    cartItem.getQuantity(),
                    cartItem.getPrice(),
                    product.getBrand(),
                    product.getModel()
                );
                orderItemRepository.save(orderItem);
                log.info("訂單項目創建成功: productId={}, quantity={}", cartItem.getProductId(), cartItem.getQuantity());

                // 減少庫存
                int oldStock = product.getStock();
                int oldSoldCount = product.getSoldCount();
                product.setStock(product.getStock() - cartItem.getQuantity());
                product.setSoldCount(product.getSoldCount() + cartItem.getQuantity());
                productRepository.save(product);
                log.info("庫存更新成功: productId={}, stock: {} -> {}, soldCount: {} -> {}",
                        product.getId(), oldStock, product.getStock(), oldSoldCount, product.getSoldCount());
            }

            // 創建支付記錄
            Payment payment = new Payment(savedOrder.getId(), Payment.PaymentMethod.ALIPAY, totalAmount);
            paymentRepository.save(payment);
            log.info("支付記錄創建成功: orderId={}, amount={}", savedOrder.getId(), totalAmount);

            // 清除購物車選中項目
            log.info("準備清除購物車選中項目: cartId={}", cart.getId());
            cartItemRepository.deleteSelectedItemsByCartId(cart.getId());
            log.info("購物車選中項目清除成功");

            log.info("訂單創建完成: orderId={}, orderNumber={}", savedOrder.getId(), savedOrder.getOrderNumber());
            return ApiResponse.success(savedOrder);
        } catch (Exception e) {
            log.error("從購物車創建訂單失敗: userId={}", userId, e);
            return ApiResponse.error("創建訂單失敗: " + e.getMessage());
        }
    }
    
    /**
     * 直接購買商品（跳過購物車）
     */
    public ApiResponse<Order> createDirectOrder(Long userId, Long productId, Integer quantity,
                                              String receiverName, String receiverPhone, 
                                              String receiverAddress, String remark) {
        try {
            // 檢查商品
            Optional<Product> productOpt = productRepository.findById(productId);
            if (productOpt.isEmpty()) {
                return ApiResponse.error("商品不存在");
            }
            
            Product product = productOpt.get();
            if (product.getStatus() != Product.Status.ON_SHELF) {
                return ApiResponse.error("商品已下架");
            }
            if (product.getStock() < quantity) {
                return ApiResponse.error("庫存不足");
            }
            
            // 計算總金額
            BigDecimal totalAmount = product.getPrice().multiply(BigDecimal.valueOf(quantity));
            
            // 生成訂單號
            String orderNumber = generateOrderNumber();
            
            // 創建訂單
            Order order = new Order(orderNumber, userId, totalAmount, receiverName, receiverPhone, receiverAddress);
            if (remark != null && !remark.trim().isEmpty()) {
                order.setRemark(remark);
            }
            Order savedOrder = orderRepository.save(order);
            
            // 創建訂單項目
            OrderItem orderItem = new OrderItem(
                savedOrder.getId(),
                productId,
                product.getName(),
                product.getMainImageUrl(),
                quantity,
                product.getPrice(),
                product.getBrand(),
                product.getModel()
            );
            orderItemRepository.save(orderItem);
            
            // 減少庫存
            product.setStock(product.getStock() - quantity);
            product.setSoldCount(product.getSoldCount() + quantity);
            productRepository.save(product);
            
            // 創建支付記錄
            Payment payment = new Payment(savedOrder.getId(), Payment.PaymentMethod.ALIPAY, totalAmount);
            paymentRepository.save(payment);
            
            return ApiResponse.success(savedOrder);
        } catch (Exception e) {
            log.error("創建直接訂單失敗: userId={}, productId={}, quantity={}", userId, productId, quantity, e);
            return ApiResponse.error("創建訂單失敗: " + e.getMessage());
        }
    }
    
    /**
     * 獲取用戶訂單列表
     */
    public ApiResponse<PagedResponse<Order>> getUserOrders(Long userId, Pageable pageable) {
        try {
            // 使用預加載查詢獲取所有訂單
            List<Order> allOrders = orderRepository.findByUserIdWithOrderItemsAndPayment(userId);

            // 手動實現分頁
            int page = pageable.getPageNumber();
            int size = pageable.getPageSize();
            int start = page * size;
            int end = Math.min(start + size, allOrders.size());

            List<Order> pageContent = allOrders.subList(start, end);

            // 創建分頁響應
            PagedResponse<Order> response = new PagedResponse<>();
            response.setContent(pageContent);
            response.setPage(page);
            response.setSize(size);
            response.setTotalElements((long) allOrders.size());
            response.setTotalPages((int) Math.ceil((double) allOrders.size() / size));
            response.setFirst(page == 0);
            response.setLast(page >= response.getTotalPages() - 1);

            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("獲取用戶訂單列表失敗: userId={}", userId, e);
            return ApiResponse.error("獲取訂單列表失敗: " + e.getMessage());
        }
    }
    
    /**
     * 獲取訂單詳情
     */
    public ApiResponse<Order> getOrderDetail(Long userId, Long orderId) {
        try {
            Optional<Order> orderOpt = orderRepository.findByIdWithOrderItemsAndPayment(orderId);
            if (orderOpt.isEmpty()) {
                return ApiResponse.error("訂單不存在");
            }
            
            Order order = orderOpt.get();
            if (!order.getUserId().equals(userId)) {
                return ApiResponse.error("無權限查看此訂單");
            }
            
            return ApiResponse.success(order);
        } catch (Exception e) {
            log.error("獲取訂單詳情失敗: userId={}, orderId={}", userId, orderId, e);
            return ApiResponse.error("獲取訂單詳情失敗: " + e.getMessage());
        }
    }
    
    /**
     * 取消訂單
     */
    public ApiResponse<String> cancelOrder(Long userId, Long orderId) {
        try {
            Optional<Order> orderOpt = orderRepository.findById(orderId);
            if (orderOpt.isEmpty()) {
                return ApiResponse.error("訂單不存在");
            }
            
            Order order = orderOpt.get();
            if (!order.getUserId().equals(userId)) {
                return ApiResponse.error("無權限操作此訂單");
            }
            
            if (order.getStatus() != Order.Status.PENDING_PAYMENT) {
                return ApiResponse.error("只能取消待付款訂單");
            }
            
            // 恢復庫存
            List<OrderItem> orderItems = orderItemRepository.findByOrderIdOrderByCreatedAtAsc(orderId);
            for (OrderItem item : orderItems) {
                Optional<Product> productOpt = productRepository.findById(item.getProductId());
                if (productOpt.isPresent()) {
                    Product product = productOpt.get();
                    product.setStock(product.getStock() + item.getQuantity());
                    product.setSoldCount(product.getSoldCount() - item.getQuantity());
                    productRepository.save(product);
                }
            }
            
            // 更新訂單狀態
            order.setStatus(Order.Status.CANCELLED);
            order.setCancelledAt(LocalDateTime.now());
            orderRepository.save(order);
            
            return ApiResponse.success("訂單已取消");
        } catch (Exception e) {
            log.error("取消訂單失敗: userId={}, orderId={}", userId, orderId, e);
            return ApiResponse.error("取消訂單失敗: " + e.getMessage());
        }
    }
    
    /**
     * 確認收貨
     */
    public ApiResponse<String> confirmReceived(Long userId, Long orderId) {
        try {
            Optional<Order> orderOpt = orderRepository.findById(orderId);
            if (orderOpt.isEmpty()) {
                return ApiResponse.error("訂單不存在");
            }

            Order order = orderOpt.get();
            if (!order.getUserId().equals(userId)) {
                return ApiResponse.error("無權限操作此訂單");
            }

            if (order.getStatus() != Order.Status.SHIPPED) {
                return ApiResponse.error("訂單狀態不正確，無法確認收貨");
            }

            // 更新訂單狀態
            order.setStatus(Order.Status.COMPLETED);
            order.setCompletedAt(LocalDateTime.now());
            orderRepository.save(order);

            log.info("用戶確認收貨: userId={}, orderId={}", userId, orderId);
            return ApiResponse.success("確認收貨成功");
        } catch (Exception e) {
            log.error("確認收貨失敗: userId={}, orderId={}", userId, orderId, e);
            return ApiResponse.error("確認收貨失敗: " + e.getMessage());
        }
    }

    /**
     * 訂單發貨（管理員操作）
     */
    public ApiResponse<String> shipOrder(Long orderId, String trackingNumber) {
        try {
            Optional<Order> orderOpt = orderRepository.findById(orderId);
            if (orderOpt.isEmpty()) {
                return ApiResponse.error("訂單不存在");
            }

            Order order = orderOpt.get();
            if (order.getStatus() != Order.Status.PAID) {
                return ApiResponse.error("訂單狀態不正確，無法發貨");
            }

            // 更新訂單狀態
            order.setStatus(Order.Status.SHIPPED);
            order.setShippedAt(LocalDateTime.now());
            if (trackingNumber != null && !trackingNumber.trim().isEmpty()) {
                order.setTrackingNumber(trackingNumber);
            }
            orderRepository.save(order);

            log.info("訂單已發貨: orderId={}, trackingNumber={}", orderId, trackingNumber);
            return ApiResponse.success("訂單發貨成功");
        } catch (Exception e) {
            log.error("訂單發貨失敗: orderId={}", orderId, e);
            return ApiResponse.error("訂單發貨失敗: " + e.getMessage());
        }
    }

    /**
     * 檢查訂單狀態變更權限
     */
    private boolean canChangeOrderStatus(Order order, int fromStatus, int toStatus) {
        // 定義允許的狀態變更路徑
        switch (fromStatus) {
            case Order.Status.PENDING_PAYMENT:
                return toStatus == Order.Status.PAID || toStatus == Order.Status.CANCELLED;
            case Order.Status.PAID:
                return toStatus == Order.Status.SHIPPED || toStatus == Order.Status.CANCELLED;
            case Order.Status.SHIPPED:
                return toStatus == Order.Status.COMPLETED;
            case Order.Status.COMPLETED:
                return false; // 已完成的訂單不能再變更
            case Order.Status.CANCELLED:
                return false; // 已取消的訂單不能再變更
            default:
                return false;
        }
    }

    /**
     * 訂單狀態變更通知
     */
    private void notifyOrderStatusChange(Order order, int oldStatus, int newStatus) {
        try {
            // 這裡可以添加通知邏輯，比如發送郵件、短信等
            log.info("訂單狀態變更通知: orderId={}, orderNumber={}, {} -> {}",
                    order.getId(), order.getOrderNumber(), oldStatus, newStatus);

            // 可以根據狀態變更發送不同的通知
            switch (newStatus) {
                case Order.Status.PAID:
                    // 支付成功通知
                    break;
                case Order.Status.SHIPPED:
                    // 發貨通知
                    break;
                case Order.Status.COMPLETED:
                    // 完成通知
                    break;
                case Order.Status.CANCELLED:
                    // 取消通知
                    break;
            }
        } catch (Exception e) {
            log.error("發送訂單狀態變更通知失敗: orderId={}", order.getId(), e);
        }
    }

    /**
     * 自動取消超時未支付訂單
     */
    public void cancelExpiredOrders() {
        try {
            LocalDateTime expiredTime = LocalDateTime.now().minusMinutes(30); // 30分鐘超時

            List<Order> expiredOrders = orderRepository.findTimeoutUnpaidOrders(expiredTime);

            for (Order order : expiredOrders) {
                try {
                    // 恢復庫存
                    List<OrderItem> orderItems = orderItemRepository.findByOrderIdOrderByCreatedAtAsc(order.getId());
                    for (OrderItem item : orderItems) {
                        Optional<Product> productOpt = productRepository.findById(item.getProductId());
                        if (productOpt.isPresent()) {
                            Product product = productOpt.get();
                            product.setStock(product.getStock() + item.getQuantity());
                            product.setSoldCount(product.getSoldCount() - item.getQuantity());
                            productRepository.save(product);
                        }
                    }

                    // 更新訂單狀態
                    order.setStatus(Order.Status.CANCELLED);
                    order.setCancelledAt(LocalDateTime.now());
                    orderRepository.save(order);

                    log.info("自動取消超時訂單: orderId={}, orderNumber={}", order.getId(), order.getOrderNumber());
                } catch (Exception e) {
                    log.error("自動取消訂單失敗: orderId={}", order.getId(), e);
                }
            }

            if (!expiredOrders.isEmpty()) {
                log.info("自動取消了 {} 個超時訂單", expiredOrders.size());
            }
        } catch (Exception e) {
            log.error("自動取消超時訂單任務執行失敗", e);
        }
    }

    /**
     * 生成訂單號
     */
    private String generateOrderNumber() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = String.valueOf((int)(Math.random() * 10000));
        return "ORD" + timestamp + String.format("%04d", Integer.parseInt(random));
    }
}
