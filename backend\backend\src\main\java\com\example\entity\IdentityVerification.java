package com.example.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "identity_verifications")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IdentityVerification {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Column(name = "real_name", nullable = false)
    private String realName;
    
    @Column(name = "id_card_number", nullable = false)
    private String idCardNumber;
    
    @Column(name = "id_card_front_url", nullable = false)
    private String idCardFrontUrl;
    
    @Column(name = "id_card_back_url", nullable = false)
    private String idCardBackUrl;
    
    @Enumerated(EnumType.STRING)
    private Status status = Status.PENDING;
    
    @Column(name = "submitted_at")
    private LocalDateTime submittedAt;
    
    @Column(name = "reviewed_at")
    private LocalDateTime reviewedAt;
    
    @Column(name = "reviewed_by")
    private String reviewedBy;
    
    @Column(name = "review_comment")
    private String reviewComment;
    
    @PrePersist
    protected void onCreate() {
        submittedAt = LocalDateTime.now();
    }
    
    public enum Status {
        PENDING,   // 待審核
        APPROVED,  // 已通過
        REJECTED   // 已拒絕
    }
}
