/**
 * 購物車圖示顯示測試腳本
 * 測試購物車圖示是否正確顯示和功能是否正常
 */

const axios = require('axios');

// 測試配置
const BASE_URL = 'http://localhost:8080';
const TEST_USER = {
  username: 'how',
  password: '12345'
};

let authToken = '';

// 登錄獲取token
async function login() {
  try {
    console.log('🔐 正在登錄...');
    const response = await axios.post(`${BASE_URL}/api/auth/login`, {
      username: TEST_USER.username,
      password: TEST_USER.password
    });

    if (response.data.success) {
      authToken = response.data.data.token;
      console.log('✅ 登錄成功');
      return true;
    } else {
      console.error('❌ 登錄失敗:', response.data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ 登錄請求失敗:', error.message);
    return false;
  }
}

// 測試獲取購物車數據
async function testGetCart() {
  try {
    console.log('\n🛒 測試獲取購物車數據...');
    const response = await axios.get(`${BASE_URL}/api/cart`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (response.data.success) {
      const cart = response.data.data;
      const itemCount = cart.cartItems ? cart.cartItems.reduce((total, item) => total + item.quantity, 0) : 0;
      
      console.log('✅ 購物車數據獲取成功');
      console.log(`   - 購物車ID: ${cart.id}`);
      console.log(`   - 商品總數量: ${itemCount}`);
      console.log(`   - 總金額: ¥${cart.totalAmount || 0}`);
      
      return itemCount;
    } else {
      console.error('❌ 獲取購物車失敗:', response.data.message);
      return 0;
    }
  } catch (error) {
    console.error('❌ 獲取購物車請求失敗:', error.message);
    return 0;
  }
}

// 測試添加商品到購物車
async function testAddToCart() {
  try {
    console.log('\n➕ 測試添加商品到購物車...');
    
    // 假設商品ID為1，數量為2
    const productId = 1;
    const quantity = 2;
    
    const response = await axios.post(`${BASE_URL}/api/cart/add`, null, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      },
      params: {
        productId: productId,
        quantity: quantity
      }
    });

    if (response.data.success) {
      console.log('✅ 添加商品到購物車成功');
      console.log(`   - 商品ID: ${productId}`);
      console.log(`   - 數量: ${quantity}`);
      return true;
    } else {
      console.error('❌ 添加商品到購物車失敗:', response.data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ 添加商品到購物車請求失敗:', error.message);
    return false;
  }
}

// 測試購物車圖示數量更新
async function testCartIconUpdate() {
  console.log('\n🔄 測試購物車圖示數量更新...');
  
  // 獲取初始購物車數量
  const initialCount = await testGetCart();
  
  // 添加商品到購物車
  const addSuccess = await testAddToCart();
  
  if (addSuccess) {
    // 再次獲取購物車數量
    const newCount = await testGetCart();
    
    if (newCount > initialCount) {
      console.log('✅ 購物車圖示數量更新測試通過');
      console.log(`   - 初始數量: ${initialCount}`);
      console.log(`   - 更新後數量: ${newCount}`);
      return true;
    } else {
      console.error('❌ 購物車圖示數量更新測試失敗');
      console.error(`   - 初始數量: ${initialCount}`);
      console.error(`   - 更新後數量: ${newCount}`);
      return false;
    }
  }
  
  return false;
}

// 主測試函數
async function runTests() {
  console.log('🚀 開始購物車圖示顯示測試\n');
  
  // 登錄
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('\n❌ 測試失敗：無法登錄');
    return;
  }
  
  // 測試購物車圖示數量更新
  const updateSuccess = await testCartIconUpdate();
  
  // 測試結果總結
  console.log('\n📊 測試結果總結:');
  console.log('==================');
  console.log(`登錄測試: ${loginSuccess ? '✅ 通過' : '❌ 失敗'}`);
  console.log(`購物車數量更新測試: ${updateSuccess ? '✅ 通過' : '❌ 失敗'}`);
  
  if (loginSuccess && updateSuccess) {
    console.log('\n🎉 所有測試通過！購物車圖示功能正常');
    console.log('\n📝 前端測試建議:');
    console.log('1. 打開瀏覽器訪問 http://localhost:5173');
    console.log('2. 登錄系統');
    console.log('3. 檢查右下角是否顯示美觀的購物車圖示');
    console.log('4. 觀察按鈕進入動畫效果');
    console.log('5. 測試懸停效果和縮放動畫');
    console.log('6. 在商品頁面添加商品到購物車');
    console.log('7. 觀察購物車圖示數量是否實時更新');
    console.log('8. 檢查數量徽章的脈衝動畫效果');
    console.log('9. 點擊購物車圖示是否正確跳轉到購物車頁面');
    console.log('10. 測試快捷鍵 Ctrl+B 功能');
    console.log('11. 在不同屏幕尺寸下測試響應式效果');
  } else {
    console.log('\n❌ 部分測試失敗，請檢查後端服務和數據庫連接');
  }
}

// 運行測試
runTests().catch(error => {
  console.error('測試運行出錯:', error);
});
