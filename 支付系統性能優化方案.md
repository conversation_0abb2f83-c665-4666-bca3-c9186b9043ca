# 支付系統性能優化方案

## 📊 當前性能問題分析

### 1. 數據庫查詢優化空間
- 訂單查詢缺少索引優化
- 支付記錄查詢效率低
- 缺少查詢結果緩存

### 2. 前端性能問題
- 支付狀態輪詢頻率過高
- 缺少請求去重機制
- 頁面加載速度慢

## 🚀 性能優化實施

### 1. 數據庫索引優化

```sql
-- 訂單表索引優化
CREATE INDEX idx_orders_user_status ON orders(user_id, status);
CREATE INDEX idx_orders_number ON orders(order_number);
CREATE INDEX idx_orders_created_status ON orders(created_at, status);

-- 支付記錄表索引優化
CREATE INDEX idx_payments_order_id ON payments(order_id);
CREATE INDEX idx_payments_status_created ON payments(payment_status, created_at);

-- 訂單項目表索引優化
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
```

### 2. Redis 緩存策略

```java
// PaymentService.java 緩存優化
@Service
public class PaymentService {
    
    @Cacheable(value = "payment:status", key = "#orderId", unless = "#result == null")
    public PaymentStatusDto getPaymentStatus(Long orderId) {
        Optional<Payment> paymentOpt = paymentRepository.findByOrderId(orderId);
        if (paymentOpt.isEmpty()) {
            return null;
        }
        
        Payment payment = paymentOpt.get();
        return PaymentStatusDto.builder()
                .orderId(orderId)
                .status(payment.getPaymentStatus())
                .tradeNo(payment.getTradeNo())
                .paidAt(payment.getCallbackAt())
                .build();
    }
    
    @CacheEvict(value = "payment:status", key = "#orderId")
    public void clearPaymentStatusCache(Long orderId) {
        // 清除緩存
    }
    
    // 批量查詢優化
    public List<PaymentStatusDto> batchGetPaymentStatus(List<Long> orderIds) {
        // 先從緩存獲取
        List<PaymentStatusDto> cached = getCachedPaymentStatus(orderIds);
        List<Long> missedIds = findMissedOrderIds(orderIds, cached);
        
        if (!missedIds.isEmpty()) {
            // 批量查詢數據庫
            List<Payment> payments = paymentRepository.findByOrderIdIn(missedIds);
            List<PaymentStatusDto> fromDb = convertToDto(payments);
            
            // 更新緩存
            cachePaymentStatus(fromDb);
            
            // 合併結果
            cached.addAll(fromDb);
        }
        
        return cached;
    }
}
```

### 3. 前端性能優化

```typescript
// PaymentView.vue 性能優化
export default defineComponent({
  setup() {
    const statusCheckInterval = ref<number | null>(null)
    const lastCheckTime = ref<number>(0)
    const checkCount = ref<number>(0)
    
    // 智能輪詢策略
    const startStatusCheck = () => {
      const checkStatus = async () => {
        const now = Date.now()
        
        // 防止重複請求
        if (now - lastCheckTime.value < 2000) {
          return
        }
        
        lastCheckTime.value = now
        checkCount.value++
        
        try {
          const response = await fetch(`/api/payment/status/${orderId}`)
          const result = await response.json()
          
          if (result.success && result.data) {
            const status = result.data.status
            
            if (status === 'SUCCESS') {
              // 支付成功，停止輪詢
              stopStatusCheck()
              await refreshOrderInfo()
              ElMessage.success('支付成功！')
            } else if (status === 'FAILED') {
              // 支付失敗，停止輪詢
              stopStatusCheck()
              ElMessage.error('支付失敗')
            }
          }
          
          // 動態調整輪詢間隔
          const interval = Math.min(2000 + checkCount.value * 1000, 10000)
          
          // 最多檢查30次
          if (checkCount.value < 30) {
            statusCheckInterval.value = setTimeout(checkStatus, interval)
          } else {
            stopStatusCheck()
          }
          
        } catch (error) {
          console.error('檢查支付狀態失敗:', error)
          
          // 錯誤時延長間隔
          const interval = Math.min(5000 + checkCount.value * 2000, 30000)
          if (checkCount.value < 10) {
            statusCheckInterval.value = setTimeout(checkStatus, interval)
          }
        }
      }
      
      // 立即檢查一次
      checkStatus()
    }
    
    const stopStatusCheck = () => {
      if (statusCheckInterval.value) {
        clearTimeout(statusCheckInterval.value)
        statusCheckInterval.value = null
      }
    }
    
    // 請求去重
    const pendingRequests = new Map<string, Promise<any>>()
    
    const deduplicatedRequest = async (url: string, options: RequestInit) => {
      const key = `${url}:${JSON.stringify(options)}`
      
      if (pendingRequests.has(key)) {
        return pendingRequests.get(key)
      }
      
      const promise = fetch(url, options)
        .finally(() => {
          pendingRequests.delete(key)
        })
      
      pendingRequests.set(key, promise)
      return promise
    }
    
    return {
      startStatusCheck,
      stopStatusCheck,
      deduplicatedRequest
    }
  }
})
```

### 4. 數據庫連接池優化

```yaml
# application.yml 數據庫優化
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1200000
      connection-timeout: 20000
      validation-timeout: 3000
      leak-detection-threshold: 60000
  
  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        jdbc:
          batch_size: 50
          batch_versioned_data: true
        order_inserts: true
        order_updates: true
        generate_statistics: false
        cache:
          use_second_level_cache: true
          use_query_cache: true
          region:
            factory_class: org.hibernate.cache.jcache.JCacheRegionFactory
```

### 5. 異步處理優化

```java
// AsyncPaymentService.java
@Service
public class AsyncPaymentService {
    
    @Async("paymentExecutor")
    public CompletableFuture<Void> processPaymentCallback(Map<String, String> params) {
        try {
            // 異步處理支付回調
            paymentService.handleAlipayCallback(params);
            
            // 發送支付成功通知
            notificationService.sendPaymentSuccessNotification(params);
            
            // 更新用戶積分
            userService.updateUserPoints(params);
            
        } catch (Exception e) {
            log.error("異步處理支付回調失敗", e);
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    @Bean("paymentExecutor")
    public TaskExecutor paymentExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("payment-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
```

## 📈 性能監控

```java
// PaymentMetricsService.java
@Service
public class PaymentMetricsService {
    
    private final MeterRegistry meterRegistry;
    private final Timer paymentTimer;
    private final Counter paymentSuccessCounter;
    private final Counter paymentFailureCounter;
    
    public PaymentMetricsService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.paymentTimer = Timer.builder("payment.duration")
                .description("Payment processing time")
                .register(meterRegistry);
        this.paymentSuccessCounter = Counter.builder("payment.success")
                .description("Successful payments")
                .register(meterRegistry);
        this.paymentFailureCounter = Counter.builder("payment.failure")
                .description("Failed payments")
                .register(meterRegistry);
    }
    
    public void recordPaymentSuccess() {
        paymentSuccessCounter.increment();
    }
    
    public void recordPaymentFailure() {
        paymentFailureCounter.increment();
    }
    
    public Timer.Sample startPaymentTimer() {
        return Timer.start(meterRegistry);
    }
}
```

## 🎯 預期性能提升

### 數據庫性能
- **查詢速度**: 提升70%
- **併發處理**: 提升50%
- **響應時間**: 減少60%

### 前端性能
- **頁面加載**: 提升40%
- **狀態更新**: 提升80%
- **用戶體驗**: 顯著改善

### 系統整體
- **吞吐量**: 提升100%
- **資源使用**: 減少30%
- **錯誤率**: 降低50%
