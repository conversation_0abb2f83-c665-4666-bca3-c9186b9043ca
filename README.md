# 用戶認證系統

基於 Vue3 + TypeScript + SpringBoot + Redis + MySQL 的完整用戶認證系統，包含登入註冊、郵件驗證、身份認證等功能。

## 功能特點

### 用戶功能
- ✅ 用戶註冊（郵箱驗證碼）
- ✅ 用戶登入（JWT 認證）
- ✅ 個人資料管理
- ✅ 身份認證（上傳身份證正反面）
- ✅ 認證狀態查看

### 管理功能
- ✅ 身份認證審核
- ✅ 用戶管理
- ✅ 審核記錄查看

### 技術特點
- ✅ 郵件頻率限制（5分鐘最多2次，一天最多5次）
- ✅ JWT Token 認證
- ✅ 文件上傳（身份證圖片）
- ✅ Redis 緩存
- ✅ 響應式設計

## 技術棧

### 後端
- SpringBoot 3.2.12
- Spring Security + JWT
- Spring Data JPA
- MySQL 8.0
- Redis
- Spring Mail

### 前端
- Vue 3
- TypeScript
- Element Plus
- Vue Router
- Pinia
- Axios

## 環境要求

- Java 21
- Node.js 18+
- MySQL 8.0
- Redis 6.0+

## 快速開始

### 1. 數據庫準備

創建 MySQL 數據庫：
```sql
CREATE DATABASE user_auth_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. Redis 準備

確保 Redis 服務正在運行：
```bash
redis-server
```

### 3. 後端配置

修改 `backend/backend/src/main/resources/application.yml`：

```yaml
spring:
  datasource:
    url: *************************************************************************************************************************
    username: your_mysql_username
    password: your_mysql_password
  
  data:
    redis:
      host: localhost
      port: 6379
      password: your_redis_password  # 如果有密碼
  
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: your-app-password
```

### 4. 啟動後端

```bash
cd backend/backend
mvn spring-boot:run
```

後端將在 http://localhost:8080 啟動

### 5. 啟動前端

```bash
cd frontend
npm install
npm run dev
```

前端將在 http://localhost:5173 啟動

## 使用說明

### 用戶註冊流程
1. 訪問註冊頁面
2. 輸入用戶名、郵箱、密碼
3. 點擊發送驗證碼
4. 輸入收到的驗證碼
5. 完成註冊

### 身份認證流程
1. 登入後進入身份認證頁面
2. 填寫真實姓名和身份證號
3. 上傳身份證正反面照片
4. 提交等待審核
5. 管理員審核通過後完成認證

### 管理員功能
1. 使用管理員帳號登入
2. 進入管理後台
3. 查看待審核的身份認證
4. 點擊查看詳情審核
5. 通過或拒絕認證申請

## API 文檔

後端啟動後可訪問 Swagger 文檔：
http://localhost:8080/swagger-ui/index.html

## 項目結構

```
├── backend/backend/          # SpringBoot 後端
│   ├── src/main/java/
│   │   ├── config/          # 配置類
│   │   ├── controller/      # 控制器
│   │   ├── dto/            # 數據傳輸對象
│   │   ├── entity/         # 實體類
│   │   ├── repository/     # 數據訪問層
│   │   ├── service/        # 業務邏輯層
│   │   └── util/           # 工具類
│   └── src/main/resources/
│       └── application.yml  # 配置文件
├── frontend/                # Vue3 前端
│   ├── src/
│   │   ├── api/            # API 接口
│   │   ├── router/         # 路由配置
│   │   ├── stores/         # 狀態管理
│   │   └── views/          # 頁面組件
│   └── package.json
└── README.md
```

## 注意事項

1. 首次運行需要配置郵件服務器信息
2. 需要創建管理員帳號（可通過數據庫直接插入）
3. 文件上傳目錄需要有寫入權限
4. Redis 和 MySQL 服務需要正常運行

## 開發者

- 基於 Vue3 + SpringBoot 架構
- 使用 Element Plus UI 框架
- 集成 JWT 認證和 Redis 緩存
- 支持文件上傳和郵件發送
