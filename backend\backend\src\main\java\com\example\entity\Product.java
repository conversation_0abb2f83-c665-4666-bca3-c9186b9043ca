package com.example.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商品實體類
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Entity
@Table(name = "products")
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "category", "creator", "images"})
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Product {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 商品名稱
     */
    @Column(name = "name", nullable = false, length = 200)
    private String name;
    
    /**
     * 商品描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    /**
     * 商品分類ID
     */
    @Column(name = "category_id", nullable = false)
    private Long categoryId;
    
    /**
     * 商品價格
     */
    @Column(name = "price", nullable = false, precision = 10, scale = 2)
    private BigDecimal price;
    
    /**
     * 原價（用於顯示折扣）
     */
    @Column(name = "original_price", precision = 10, scale = 2)
    private BigDecimal originalPrice;
    
    /**
     * 庫存數量
     */
    @Column(name = "stock", nullable = false)
    private Integer stock = 0;
    
    /**
     * 已售數量
     */
    @Column(name = "sold_count", nullable = false)
    private Integer soldCount = 0;
    
    /**
     * 商品狀態：1-上架，0-下架，-1-刪除
     */
    @Column(name = "status", nullable = false)
    private Integer status = 1;
    
    /**
     * 商品主圖URL
     */
    @Column(name = "main_image_url")
    private String mainImageUrl;
    
    /**
     * 商品重量（克）
     */
    @Column(name = "weight")
    private Integer weight;
    
    /**
     * 商品品牌
     */
    @Column(name = "brand", length = 100)
    private String brand;
    
    /**
     * 商品型號
     */
    @Column(name = "model", length = 100)
    private String model;
    
    /**
     * 商品標籤（JSON格式存儲）
     */
    @Column(name = "tags", columnDefinition = "TEXT")
    private String tags;
    
    /**
     * 是否推薦：1-是，0-否
     */
    @Column(name = "is_recommended", nullable = false)
    private Integer isRecommended = 0;
    
    /**
     * 是否熱門：1-是，0-否
     */
    @Column(name = "is_hot", nullable = false)
    private Integer isHot = 0;
    
    /**
     * 排序號
     */
    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;
    
    /**
     * 創建時間
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    /**
     * 更新時間
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    /**
     * 創建者ID
     */
    @Column(name = "created_by")
    private Long createdBy;
    
    /**
     * 最後修改者ID
     */
    @Column(name = "updated_by")
    private Long updatedBy;
    
    // 關聯關係 - 商品分類
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id", insertable = false, updatable = false)
    private ProductCategory category;
    
    // 關聯關係 - 創建者
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by", insertable = false, updatable = false)
    private User creator;
    
    // 關聯關係 - 商品圖片列表
    @OneToMany(mappedBy = "product", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private List<ProductImage> images;
    
    /**
     * 商品狀態枚舉
     */
    public static class Status {
        public static final int DELETED = -1;   // 已刪除
        public static final int OFF_SHELF = 0;  // 下架
        public static final int ON_SHELF = 1;   // 上架
    }
    
    /**
     * 推薦標識
     */
    public static class RecommendFlag {
        public static final int NOT_RECOMMENDED = 0;  // 不推薦
        public static final int RECOMMENDED = 1;      // 推薦
    }
    
    /**
     * 熱門標識
     */
    public static class HotFlag {
        public static final int NOT_HOT = 0;  // 不熱門
        public static final int HOT = 1;      // 熱門
    }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 構造函數 - 創建新商品
     */
    public Product(String name, String description, Long categoryId, BigDecimal price, Integer stock, Long createdBy) {
        this.name = name;
        this.description = description;
        this.categoryId = categoryId;
        this.price = price;
        this.stock = stock;
        this.createdBy = createdBy;
        this.status = Status.ON_SHELF;
        this.soldCount = 0;
        this.isRecommended = RecommendFlag.NOT_RECOMMENDED;
        this.isHot = HotFlag.NOT_HOT;
        this.sortOrder = 0;
    }
}
