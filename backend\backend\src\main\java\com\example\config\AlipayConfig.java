package com.example.config;

import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 支付寶配置類
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Configuration
@ConfigurationProperties(prefix = "alipay.config")
@Data
public class AlipayConfig {
    
    /**
     * 支付寶網關地址
     */
    private String serverUrl;
    
    /**
     * 應用ID
     */
    private String appid;
    
    /**
     * 應用私鑰
     */
    private String privateKey;
    
    /**
     * 支付寶公鑰
     */
    private String alipayPublicKey;
    
    /**
     * 異步回調地址
     */
    private String notifyUrl;
    
    /**
     * 同步返回地址
     */
    private String returnUrl;
    
    /**
     * 簽名類型
     */
    private String signType = "RSA2";
    
    /**
     * 字符集
     */
    private String charset = "UTF-8";
    
    /**
     * 數據格式
     */
    private String format = "JSON";
    
    /**
     * 創建支付寶客戶端
     */
    @Bean
    public AlipayClient alipayClient() throws Exception {
        com.alipay.api.AlipayConfig alipayConfig = new com.alipay.api.AlipayConfig();

        // 設置網關地址
        alipayConfig.setServerUrl(serverUrl);
        // 設置應用ID
        alipayConfig.setAppId(appid);
        // 設置應用私鑰
        alipayConfig.setPrivateKey(privateKey);
        // 設置支付寶公鑰
        alipayConfig.setAlipayPublicKey(alipayPublicKey);
        // 設置請求格式，固定值json
        alipayConfig.setFormat(format);
        // 設置字符集
        alipayConfig.setCharset(charset);
        // 設置簽名類型
        alipayConfig.setSignType(signType);

        // 實例化客戶端
        return new DefaultAlipayClient(alipayConfig);
    }
}
