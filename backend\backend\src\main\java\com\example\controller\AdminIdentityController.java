package com.example.controller;

import com.example.dto.ApiResponse;
import com.example.entity.Admin;
import com.example.entity.IdentityVerification;
import com.example.service.IdentityVerificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/identity")
@CrossOrigin(origins = "*")
@Slf4j
public class AdminIdentityController {
    
    @Autowired
    private IdentityVerificationService identityVerificationService;
    
    /**
     * 獲取待審核的身份認證列表（管理員）
     */
    @GetMapping("/pending")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getPendingVerifications() {
        try {
            List<IdentityVerification> verifications = identityVerificationService.getPendingVerifications();

            List<Map<String, Object>> data = verifications.stream()
                .map(this::convertToMapWithUserInfo)
                .toList();

            return ResponseEntity.ok(ApiResponse.success(data));

        } catch (Exception e) {
            log.error("獲取待審核列表失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("獲取待審核列表失敗"));
        }
    }
    
    /**
     * 審核身份認證（管理員）
     */
    @PostMapping("/review/{id}")
    public ResponseEntity<ApiResponse<String>> reviewVerification(
            @PathVariable Long id,
            @RequestParam boolean approved,
            @RequestParam(required = false) String comment,
            Authentication authentication) {
        try {
            Admin admin = (Admin) authentication.getPrincipal();
            
            identityVerificationService.reviewIdentityVerification(
                id, approved, comment, admin.getUsername()
            );
            
            String message = approved ? "身份認證審核通過" : "身份認證審核拒絕";
            return ResponseEntity.ok(ApiResponse.success(message));
            
        } catch (Exception e) {
            log.error("身份認證審核失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 獲取身份認證詳情（管理員）
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getVerificationDetail(@PathVariable Long id) {
        try {
            IdentityVerification verification = identityVerificationService.getVerificationById(id)
                .orElseThrow(() -> new RuntimeException("身份認證記錄不存在"));
            
            Map<String, Object> data = convertToMapWithUserInfo(verification);
            
            return ResponseEntity.ok(ApiResponse.success(data));
            
        } catch (Exception e) {
            log.error("獲取身份認證詳情失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 獲取所有身份認證記錄（管理員）
     */
    @GetMapping("/all")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getAllVerifications(
            @RequestParam(required = false) String status) {
        try {
            List<IdentityVerification> verifications;
            
            if (status != null && !status.isEmpty()) {
                IdentityVerification.Status statusEnum = IdentityVerification.Status.valueOf(status.toUpperCase());
                verifications = identityVerificationService.getVerificationsByStatus(statusEnum);
            } else {
                verifications = identityVerificationService.getAllVerifications();
            }
            
            List<Map<String, Object>> data = verifications.stream()
                .map(this::convertToMapWithUserInfo)
                .toList();
            
            return ResponseEntity.ok(ApiResponse.success(data));
            
        } catch (Exception e) {
            log.error("獲取身份認證記錄失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("獲取身份認證記錄失敗"));
        }
    }
    
    /**
     * 獲取身份認證統計信息（管理員）
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getStatistics() {
        try {
            Map<String, Object> statistics = identityVerificationService.getVerificationStatistics();
            return ResponseEntity.ok(ApiResponse.success(statistics));
            
        } catch (Exception e) {
            log.error("獲取統計信息失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("獲取統計信息失敗"));
        }
    }
    
    /**
     * 轉換為Map（包含用戶信息）
     */
    private Map<String, Object> convertToMapWithUserInfo(IdentityVerification verification) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", verification.getId());
        map.put("realName", verification.getRealName());
        map.put("idCardNumber", verification.getIdCardNumber());
        map.put("status", verification.getStatus());
        map.put("submittedAt", verification.getSubmittedAt());
        map.put("reviewedAt", verification.getReviewedAt());
        map.put("reviewedBy", verification.getReviewedBy());
        map.put("reviewComment", verification.getReviewComment());
        map.put("userId", verification.getUser().getId());
        map.put("username", verification.getUser().getUsername());
        map.put("email", verification.getUser().getEmail());
        map.put("idCardFrontUrl", verification.getIdCardFrontUrl());
        map.put("idCardBackUrl", verification.getIdCardBackUrl());
        
        return map;
    }
}
