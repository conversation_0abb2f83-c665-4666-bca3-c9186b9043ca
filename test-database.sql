-- 購物車和支付功能數據庫測試腳本
-- 使用方法: mysql -u root -p java_springboot_redis_mail_login_test_250708 < test-database.sql

-- 檢查數據庫連接
SELECT 'Database connection test' as test_name, NOW() as current_time;

-- 1. 檢查表結構
SELECT '=== 檢查表結構 ===' as section;

-- 檢查購物車表
DESCRIBE carts;
DESCRIBE cart_items;

-- 檢查訂單表
DESCRIBE orders;
DESCRIBE order_items;

-- 檢查支付表
DESCRIBE payments;

-- 檢查商品表 (確保存在)
DESCRIBE products;

-- 檢查用戶表 (確保存在)
DESCRIBE users;

-- 2. 檢查表是否存在
SELECT '=== 檢查表是否存在 ===' as section;

SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    CREATE_TIME
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'java_springboot_redis_mail_login_test_250708'
AND TABLE_NAME IN ('carts', 'cart_items', 'orders', 'order_items', 'payments', 'products', 'users');

-- 3. 創建測試數據
SELECT '=== 創建測試數據 ===' as section;

-- 確保有測試用戶
INSERT IGNORE INTO users (email, password, username, status, created_at, updated_at) 
VALUES ('<EMAIL>', '$2a$10$test.hash.password', 'testuser', 1, NOW(), NOW());

-- 確保有測試商品分類
INSERT IGNORE INTO product_categories (id, name, description, status, created_at, updated_at) 
VALUES (1, '測試分類', '用於測試的商品分類', 1, NOW(), NOW());

-- 創建測試商品
INSERT IGNORE INTO products (
    name, description, category_id, price, original_price, stock, sold_count, 
    status, main_image_url, brand, model, is_recommended, is_hot, sort_order,
    created_at, updated_at, created_by, updated_by
) VALUES (
    '測試商品1', '用於測試購物車功能的商品', 1, 99.99, 129.99, 100, 0,
    1, '/images/test-product-1.jpg', '測試品牌', 'TEST-001', 1, 1, 1,
    NOW(), NOW(), 1, 1
), (
    '測試商品2', '用於測試訂單功能的商品', 1, 199.99, 249.99, 50, 5,
    1, '/images/test-product-2.jpg', '測試品牌', 'TEST-002', 0, 1, 2,
    NOW(), NOW(), 1, 1
), (
    '測試商品3', '用於測試支付功能的商品', 1, 299.99, 399.99, 30, 10,
    1, '/images/test-product-3.jpg', '測試品牌', 'TEST-003', 1, 0, 3,
    NOW(), NOW(), 1, 1
);

-- 4. 測試購物車功能
SELECT '=== 測試購物車功能 ===' as section;

-- 創建測試購物車
INSERT INTO carts (user_id, total_amount, total_quantity, status, created_at, updated_at)
VALUES (1, 0.00, 0, 1, NOW(), NOW());

SET @cart_id = LAST_INSERT_ID();

-- 添加商品到購物車
INSERT INTO cart_items (
    cart_id, product_id, quantity, price, product_name, product_image_url, selected, created_at, updated_at
) VALUES (
    @cart_id, 1, 2, 99.99, '測試商品1', '/images/test-product-1.jpg', 1, NOW(), NOW()
), (
    @cart_id, 2, 1, 199.99, '測試商品2', '/images/test-product-2.jpg', 1, NOW(), NOW()
);

-- 更新購物車統計
UPDATE carts 
SET total_amount = (
    SELECT SUM(price * quantity) FROM cart_items WHERE cart_id = @cart_id
),
total_quantity = (
    SELECT SUM(quantity) FROM cart_items WHERE cart_id = @cart_id
),
updated_at = NOW()
WHERE id = @cart_id;

-- 查看購物車數據
SELECT 'Cart Data:' as info;
SELECT * FROM carts WHERE id = @cart_id;
SELECT * FROM cart_items WHERE cart_id = @cart_id;

-- 5. 測試訂單功能
SELECT '=== 測試訂單功能 ===' as section;

-- 創建測試訂單
INSERT INTO orders (
    order_number, user_id, total_amount, paid_amount, status,
    receiver_name, receiver_phone, receiver_address, remark,
    created_at, updated_at
) VALUES (
    CONCAT('ORD', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '0001'),
    1, 399.97, NULL, 0,
    '張三', '13800138000', '北京市朝陽區測試街道123號', '測試訂單',
    NOW(), NOW()
);

SET @order_id = LAST_INSERT_ID();

-- 創建訂單項目
INSERT INTO order_items (
    order_id, product_id, product_name, product_image_url, quantity, unit_price, subtotal,
    product_brand, product_model, created_at, updated_at
) VALUES (
    @order_id, 1, '測試商品1', '/images/test-product-1.jpg', 2, 99.99, 199.98,
    '測試品牌', 'TEST-001', NOW(), NOW()
), (
    @order_id, 2, '測試商品2', '/images/test-product-2.jpg', 1, 199.99, 199.99,
    '測試品牌', 'TEST-002', NOW(), NOW()
);

-- 查看訂單數據
SELECT 'Order Data:' as info;
SELECT * FROM orders WHERE id = @order_id;
SELECT * FROM order_items WHERE order_id = @order_id;

-- 6. 測試支付功能
SELECT '=== 測試支付功能 ===' as section;

-- 創建支付記錄
INSERT INTO payments (
    order_id, payment_method, payment_status, payment_amount,
    third_party_trade_no, third_party_serial_no, buyer_account,
    created_at, updated_at
) VALUES (
    @order_id, 1, 0, 399.97,
    NULL, NULL, NULL,
    NOW(), NOW()
);

-- 查看支付數據
SELECT 'Payment Data:' as info;
SELECT * FROM payments WHERE order_id = @order_id;

-- 7. 數據完整性檢查
SELECT '=== 數據完整性檢查 ===' as section;

-- 檢查外鍵關係
SELECT 'Cart Items with Product Info:' as info;
SELECT ci.*, p.name as product_name_from_table
FROM cart_items ci
LEFT JOIN products p ON ci.product_id = p.id
WHERE ci.cart_id = @cart_id;

SELECT 'Order Items with Product Info:' as info;
SELECT oi.*, p.name as product_name_from_table
FROM order_items oi
LEFT JOIN products p ON oi.product_id = p.id
WHERE oi.order_id = @order_id;

SELECT 'Payment with Order Info:' as info;
SELECT p.*, o.order_number, o.total_amount as order_total
FROM payments p
LEFT JOIN orders o ON p.order_id = o.id
WHERE p.order_id = @order_id;

-- 8. 統計查詢測試
SELECT '=== 統計查詢測試 ===' as section;

-- 用戶購物車統計
SELECT 
    u.username,
    COUNT(c.id) as cart_count,
    COALESCE(SUM(c.total_quantity), 0) as total_items,
    COALESCE(SUM(c.total_amount), 0) as total_amount
FROM users u
LEFT JOIN carts c ON u.id = c.user_id AND c.status = 1
WHERE u.id = 1
GROUP BY u.id, u.username;

-- 用戶訂單統計
SELECT 
    u.username,
    COUNT(o.id) as order_count,
    COALESCE(SUM(o.total_amount), 0) as total_order_amount,
    COUNT(CASE WHEN o.status = 0 THEN 1 END) as pending_orders,
    COUNT(CASE WHEN o.status = 1 THEN 1 END) as paid_orders
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
WHERE u.id = 1
GROUP BY u.id, u.username;

-- 商品銷售統計
SELECT 
    p.name,
    p.stock,
    p.sold_count,
    COALESCE(SUM(oi.quantity), 0) as order_quantity,
    COALESCE(SUM(oi.subtotal), 0) as order_revenue
FROM products p
LEFT JOIN order_items oi ON p.id = oi.product_id
LEFT JOIN orders o ON oi.order_id = o.id AND o.status IN (1, 2, 3)
WHERE p.id IN (1, 2, 3)
GROUP BY p.id, p.name, p.stock, p.sold_count;

-- 9. 清理測試數據 (可選)
-- SELECT '=== 清理測試數據 ===' as section;
-- DELETE FROM cart_items WHERE cart_id = @cart_id;
-- DELETE FROM carts WHERE id = @cart_id;
-- DELETE FROM order_items WHERE order_id = @order_id;
-- DELETE FROM payments WHERE order_id = @order_id;
-- DELETE FROM orders WHERE id = @order_id;

SELECT '=== 測試完成 ===' as section;
SELECT 'Database test completed successfully!' as result;
