package com.example.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class RedisService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    // 郵件驗證相關的 Redis key 前綴
    private static final String EMAIL_VERIFICATION_PREFIX = "email_verification:";
    private static final String EMAIL_RATE_LIMIT_5MIN_PREFIX = "email_rate_limit_5min:";
    private static final String EMAIL_RATE_LIMIT_DAY_PREFIX = "email_rate_limit_day:";

    // 關注功能相關的 Redis key 前綴
    private static final String USER_FOLLOWING_PREFIX = "user:following:";
    private static final String USER_FOLLOWERS_PREFIX = "user:followers:";
    private static final String USER_FOLLOW_COUNT_PREFIX = "user:follow:count:";

    // 菜單緩存相關的 Redis key 前綴
    private static final String MENU_TREE_PREFIX = "menu:tree:";
    private static final String MENU_USER_PREFIX = "menu:user:";
    private static final String MENU_ROLE_PREFIX = "menu:role:";
    private static final String MENU_ITEM_PREFIX = "menu:item:";
    
    /**
     * 設置值
     */
    public void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }
    
    /**
     * 設置值並指定過期時間
     */
    public void set(String key, Object value, Duration timeout) {
        redisTemplate.opsForValue().set(key, value, timeout);
    }
    
    /**
     * 獲取值
     */
    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }
    
    /**
     * 刪除key
     */
    public Boolean delete(String key) {
        return redisTemplate.delete(key);
    }
    
    /**
     * 檢查key是否存在
     */
    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }
    
    /**
     * 增加計數器
     */
    public Long increment(String key) {
        return redisTemplate.opsForValue().increment(key);
    }
    
    /**
     * 增加計數器並設置過期時間
     */
    public Long incrementAndExpire(String key, Duration timeout) {
        Long count = redisTemplate.opsForValue().increment(key);
        if (count == 1) {
            redisTemplate.expire(key, timeout);
        }
        return count;
    }
    
    /**
     * 檢查郵件發送頻率限制（5分鐘內最多2次）
     */
    public boolean checkEmailRateLimit5Min(String email) {
        String key = EMAIL_RATE_LIMIT_5MIN_PREFIX + email;
        Long count = incrementAndExpire(key, Duration.ofMinutes(5));
        return count <= 2;
    }
    
    /**
     * 檢查郵件發送頻率限制（一天內最多5次）
     */
    public boolean checkEmailRateLimitDay(String email) {
        String key = EMAIL_RATE_LIMIT_DAY_PREFIX + email + ":" + LocalDate.now();
        Long count = incrementAndExpire(key, Duration.ofDays(1));
        return count <= 5;
    }
    
    /**
     * 獲取5分鐘內發送次數
     */
    public long getEmailCount5Min(String email) {
        String key = EMAIL_RATE_LIMIT_5MIN_PREFIX + email;
        Object count = get(key);
        return count != null ? Long.parseLong(count.toString()) : 0;
    }
    
    /**
     * 獲取一天內發送次數
     */
    public long getEmailCountDay(String email) {
        String key = EMAIL_RATE_LIMIT_DAY_PREFIX + email + ":" + LocalDate.now();
        Object count = get(key);
        return count != null ? Long.parseLong(count.toString()) : 0;
    }

    // ==================== 關注功能相關的Redis操作 ====================

    /**
     * 添加關注關係到Redis
     */
    public void addFollowing(Long followerId, Long followingId) {
        String followingKey = USER_FOLLOWING_PREFIX + followerId;
        String followersKey = USER_FOLLOWERS_PREFIX + followingId;

        // 添加到關注列表
        redisTemplate.opsForSet().add(followingKey, followingId.toString());
        // 添加到粉絲列表
        redisTemplate.opsForSet().add(followersKey, followerId.toString());

        // 更新計數
        updateFollowCount(followerId, followingId, true);
    }

    /**
     * 移除關注關係從Redis
     */
    public void removeFollowing(Long followerId, Long followingId) {
        String followingKey = USER_FOLLOWING_PREFIX + followerId;
        String followersKey = USER_FOLLOWERS_PREFIX + followingId;

        // 從關注列表移除
        redisTemplate.opsForSet().remove(followingKey, followingId.toString());
        // 從粉絲列表移除
        redisTemplate.opsForSet().remove(followersKey, followerId.toString());

        // 更新計數
        updateFollowCount(followerId, followingId, false);
    }

    /**
     * 檢查是否已關注
     */
    public boolean isFollowing(Long followerId, Long followingId) {
        String followingKey = USER_FOLLOWING_PREFIX + followerId;
        return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(followingKey, followingId.toString()));
    }

    /**
     * 獲取用戶關注列表
     */
    public Set<String> getFollowingList(Long userId) {
        String followingKey = USER_FOLLOWING_PREFIX + userId;
        Set<Object> members = redisTemplate.opsForSet().members(followingKey);
        return members != null ?
            members.stream().map(Object::toString).collect(Collectors.toSet()) :
            Set.of();
    }

    /**
     * 獲取用戶粉絲列表
     */
    public Set<String> getFollowersList(Long userId) {
        String followersKey = USER_FOLLOWERS_PREFIX + userId;
        Set<Object> members = redisTemplate.opsForSet().members(followersKey);
        return members != null ?
            members.stream().map(Object::toString).collect(Collectors.toSet()) :
            Set.of();
    }

    /**
     * 獲取關注數量
     */
    public long getFollowingCount(Long userId) {
        String countKey = USER_FOLLOW_COUNT_PREFIX + userId;
        Object count = redisTemplate.opsForHash().get(countKey, "following");
        return count != null ? Long.parseLong(count.toString()) : 0;
    }

    /**
     * 獲取粉絲數量
     */
    public long getFollowersCount(Long userId) {
        String countKey = USER_FOLLOW_COUNT_PREFIX + userId;
        Object count = redisTemplate.opsForHash().get(countKey, "followers");
        return count != null ? Long.parseLong(count.toString()) : 0;
    }

    /**
     * 更新關注計數
     */
    private void updateFollowCount(Long followerId, Long followingId, boolean isAdd) {
        String followerCountKey = USER_FOLLOW_COUNT_PREFIX + followerId;
        String followingCountKey = USER_FOLLOW_COUNT_PREFIX + followingId;

        if (isAdd) {
            // 增加關注數
            redisTemplate.opsForHash().increment(followerCountKey, "following", 1);
            // 增加粉絲數
            redisTemplate.opsForHash().increment(followingCountKey, "followers", 1);
        } else {
            // 減少關注數
            redisTemplate.opsForHash().increment(followerCountKey, "following", -1);
            // 減少粉絲數
            redisTemplate.opsForHash().increment(followingCountKey, "followers", -1);
        }
    }

    /**
     * 初始化用戶關注數據到Redis（從數據庫同步）
     */
    public void initUserFollowData(Long userId, Set<String> followingIds, Set<String> followerIds) {
        String followingKey = USER_FOLLOWING_PREFIX + userId;
        String followersKey = USER_FOLLOWERS_PREFIX + userId;
        String countKey = USER_FOLLOW_COUNT_PREFIX + userId;

        // 清除舊數據
        redisTemplate.delete(followingKey);
        redisTemplate.delete(followersKey);

        // 設置關注列表
        if (!followingIds.isEmpty()) {
            redisTemplate.opsForSet().add(followingKey, (Object[]) followingIds.toArray(new String[0]));
        }

        // 設置粉絲列表
        if (!followerIds.isEmpty()) {
            redisTemplate.opsForSet().add(followersKey, (Object[]) followerIds.toArray(new String[0]));
        }

        // 設置計數
        redisTemplate.opsForHash().put(countKey, "following", String.valueOf(followingIds.size()));
        redisTemplate.opsForHash().put(countKey, "followers", String.valueOf(followerIds.size()));
    }

    /**
     * 清除用戶關注相關的Redis數據
     */
    public void clearUserFollowData(Long userId) {
        String followingKey = USER_FOLLOWING_PREFIX + userId;
        String followersKey = USER_FOLLOWERS_PREFIX + userId;
        String countKey = USER_FOLLOW_COUNT_PREFIX + userId;

        redisTemplate.delete(followingKey);
        redisTemplate.delete(followersKey);
        redisTemplate.delete(countKey);
    }

    // ==================== 菜單緩存相關的Redis操作 ====================

    /**
     * 緩存完整菜單樹
     */
    public void cacheMenuTree(String menuTreeJson) {
        String key = MENU_TREE_PREFIX + "all";
        set(key, menuTreeJson, Duration.ofHours(24));
    }

    /**
     * 獲取緩存的菜單樹
     */
    public String getCachedMenuTree() {
        String key = MENU_TREE_PREFIX + "all";
        Object result = get(key);
        return result != null ? result.toString() : null;
    }

    /**
     * 緩存用戶專屬菜單樹
     */
    public void cacheUserMenuTree(Long userId, String menuTreeJson) {
        String key = MENU_USER_PREFIX + userId;
        set(key, menuTreeJson, Duration.ofHours(24));
    }

    /**
     * 獲取用戶專屬菜單樹
     */
    public String getCachedUserMenuTree(Long userId) {
        String key = MENU_USER_PREFIX + userId;
        Object result = get(key);
        return result != null ? result.toString() : null;
    }

    /**
     * 緩存角色菜單樹
     */
    public void cacheRoleMenuTree(String role, String menuTreeJson) {
        String key = MENU_ROLE_PREFIX + role;
        set(key, menuTreeJson, Duration.ofHours(24));
    }

    /**
     * 獲取角色菜單樹
     */
    public String getCachedRoleMenuTree(String role) {
        String key = MENU_ROLE_PREFIX + role;
        Object result = get(key);
        return result != null ? result.toString() : null;
    }

    /**
     * 緩存單個菜單項
     */
    public void cacheMenuItem(Long menuId, String menuJson) {
        String key = MENU_ITEM_PREFIX + menuId;
        set(key, menuJson, Duration.ofHours(24));
    }

    /**
     * 獲取緩存的菜單項
     */
    public String getCachedMenuItem(Long menuId) {
        String key = MENU_ITEM_PREFIX + menuId;
        Object result = get(key);
        return result != null ? result.toString() : null;
    }

    /**
     * 清除所有菜單緩存
     */
    public void clearAllMenuCache() {
        // 清除菜單樹緩存
        Set<String> treeKeys = redisTemplate.keys(MENU_TREE_PREFIX + "*");
        if (treeKeys != null && !treeKeys.isEmpty()) {
            redisTemplate.delete(treeKeys);
        }

        // 清除用戶菜單緩存
        Set<String> userKeys = redisTemplate.keys(MENU_USER_PREFIX + "*");
        if (userKeys != null && !userKeys.isEmpty()) {
            redisTemplate.delete(userKeys);
        }

        // 清除角色菜單緩存
        Set<String> roleKeys = redisTemplate.keys(MENU_ROLE_PREFIX + "*");
        if (roleKeys != null && !roleKeys.isEmpty()) {
            redisTemplate.delete(roleKeys);
        }

        // 清除菜單項緩存
        Set<String> itemKeys = redisTemplate.keys(MENU_ITEM_PREFIX + "*");
        if (itemKeys != null && !itemKeys.isEmpty()) {
            redisTemplate.delete(itemKeys);
        }
    }

    /**
     * 清除特定用戶的菜單緩存
     */
    public void clearUserMenuCache(Long userId) {
        String key = MENU_USER_PREFIX + userId;
        delete(key);
    }

    /**
     * 清除特定角色的菜單緩存
     */
    public void clearRoleMenuCache(String role) {
        String key = MENU_ROLE_PREFIX + role;
        delete(key);
    }
}
