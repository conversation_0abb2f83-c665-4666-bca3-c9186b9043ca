package com.example.service;

import com.example.dto.ApiResponse;
import com.example.dto.FavoriteDto;
import com.example.dto.FavoriteItemDto;
import com.example.dto.FavoriteStatsDto;
import com.example.dto.PagedResponse;
import com.example.enums.ItemType;

/**
 * 收藏服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface FavoriteService {
    
    /**
     * 收藏内容
     * 
     * @param userId 用户ID
     * @param itemId 内容ID
     * @return 操作结果
     */
    ApiResponse<String> addFavorite(Long userId, Long itemId);
    
    /**
     * 取消收藏
     * 
     * @param userId 用户ID
     * @param itemId 内容ID
     * @return 操作结果
     */
    ApiResponse<String> removeFavorite(Long userId, Long itemId);
    
    /**
     * 检查收藏状态
     * 
     * @param userId 用户ID
     * @param itemId 内容ID
     * @return 是否已收藏
     */
    ApiResponse<Boolean> isFavorited(Long userId, Long itemId);
    
    /**
     * 获取用户收藏列表
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 页大小
     * @return 收藏列表
     */
    ApiResponse<PagedResponse<FavoriteDto>> getUserFavorites(Long userId, int page, int size);
    
    /**
     * 获取用户特定类型的收藏列表
     * 
     * @param userId 用户ID
     * @param itemType 内容类型
     * @param page 页码
     * @param size 页大小
     * @return 收藏列表
     */
    ApiResponse<PagedResponse<FavoriteDto>> getUserFavoritesByType(Long userId, ItemType itemType, int page, int size);
    
    /**
     * 获取收藏排行榜
     * 
     * @param page 页码
     * @param size 页大小
     * @param itemType 内容类型（可选）
     * @return 排行榜
     */
    ApiResponse<PagedResponse<FavoriteItemDto>> getFavoriteRanking(int page, int size, ItemType itemType);
    
    /**
     * 获取收藏统计信息
     * 
     * @param itemId 内容ID
     * @param userId 用户ID（可选，用于检查收藏状态）
     * @return 统计信息
     */
    ApiResponse<FavoriteStatsDto> getFavoriteStats(Long itemId, Long userId);
    
    /**
     * 搜索可收藏内容
     * 
     * @param keyword 关键词
     * @param page 页码
     * @param size 页大小
     * @param userId 用户ID（可选，用于设置收藏状态）
     * @return 搜索结果
     */
    ApiResponse<PagedResponse<FavoriteItemDto>> searchFavoriteItems(String keyword, int page, int size, Long userId);
    
    /**
     * 获取最近创建的内容
     * 
     * @param page 页码
     * @param size 页大小
     * @param itemType 内容类型（可选）
     * @param userId 用户ID（可选，用于设置收藏状态）
     * @return 最近内容列表
     */
    ApiResponse<PagedResponse<FavoriteItemDto>> getRecentItems(int page, int size, ItemType itemType, Long userId);
    
    /**
     * 创建可收藏内容
     * 
     * @param title 标题
     * @param description 描述
     * @param itemType 内容类型
     * @param contentUrl 内容链接
     * @param thumbnailUrl 缩略图链接
     * @return 创建结果
     */
    ApiResponse<FavoriteItemDto> createFavoriteItem(String title, String description, ItemType itemType, String contentUrl, String thumbnailUrl);
    
    /**
     * 更新可收藏内容
     * 
     * @param itemId 内容ID
     * @param title 标题
     * @param description 描述
     * @param contentUrl 内容链接
     * @param thumbnailUrl 缩略图链接
     * @return 更新结果
     */
    ApiResponse<FavoriteItemDto> updateFavoriteItem(Long itemId, String title, String description, String contentUrl, String thumbnailUrl);
    
    /**
     * 删除可收藏内容
     * 
     * @param itemId 内容ID
     * @return 删除结果
     */
    ApiResponse<String> deleteFavoriteItem(Long itemId);
}
