package com.example.service.impl;

import com.example.dto.*;
import com.example.entity.Favorite;
import com.example.entity.FavoriteItem;
import com.example.enums.ItemType;
import com.example.exception.*;
import com.example.repository.FavoriteItemRepository;
import com.example.repository.FavoriteRepository;
import com.example.service.FavoriteService;
import com.example.service.FavoriteCacheService;
import com.example.service.RedisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 收藏服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
@Transactional
public class FavoriteServiceImpl implements FavoriteService {
    
    private static final Logger logger = LoggerFactory.getLogger(FavoriteServiceImpl.class);
    
    // Redis缓存键前缀
    private static final String FAVORITE_RANKING_CACHE_KEY = "favorite:ranking:";
    private static final String FAVORITE_COUNT_CACHE_KEY = "favorite:count:";
    private static final String RATE_LIMIT_KEY = "favorite:rate_limit:";
    
    // 速率限制配置
    private static final int RATE_LIMIT_MAX_REQUESTS = 20; // 每分钟最多20次操作
    private static final int RATE_LIMIT_WINDOW_SECONDS = 60; // 时间窗口60秒
    
    @Autowired
    private FavoriteRepository favoriteRepository;
    
    @Autowired
    private FavoriteItemRepository favoriteItemRepository;
    
    @Autowired
    private RedisService redisService;

    @Autowired
    private FavoriteCacheService cacheService;
    
    @Override
    public ApiResponse<String> addFavorite(Long userId, Long itemId) {
        try {
            // 检查速率限制
            if (!checkRateLimit(userId)) {
                throw new RateLimitExceededException(userId, RATE_LIMIT_MAX_REQUESTS, RATE_LIMIT_WINDOW_SECONDS);
            }

            // 检查内容是否存在
            Optional<FavoriteItem> itemOptional = favoriteItemRepository.findById(itemId);
            if (itemOptional.isEmpty()) {
                throw new FavoriteException("内容不存在", "ITEM_NOT_FOUND");
            }

            FavoriteItem item = itemOptional.get();

            // 检查是否已收藏
            if (favoriteRepository.existsByUserIdAndItemId(userId, itemId)) {
                throw new DuplicateFavoriteException(userId, itemId);
            }

            // 创建收藏记录
            Favorite favorite = new Favorite(userId, itemId, item.getItemType());
            favoriteRepository.save(favorite);

            // 更新收藏计数
            favoriteItemRepository.incrementFavoriteCount(itemId);

            // 清除相关缓存
            clearFavoriteCache(itemId);

            logger.info("用户 {} 收藏了内容 {}", userId, itemId);
            return ApiResponse.success("收藏成功");

        } catch (FavoriteException e) {
            // 重新抛出收藏相关异常，让全局异常处理器处理
            throw e;
        } catch (Exception e) {
            logger.error("收藏操作失败: userId={}, itemId={}", userId, itemId, e);
            throw new FavoriteException("收藏失败，请稍后重试", e);
        }
    }
    
    @Override
    public ApiResponse<String> removeFavorite(Long userId, Long itemId) {
        try {
            // 检查速率限制
            if (!checkRateLimit(userId)) {
                throw new RateLimitExceededException(userId, RATE_LIMIT_MAX_REQUESTS, RATE_LIMIT_WINDOW_SECONDS);
            }

            // 检查收藏记录是否存在
            Optional<Favorite> favoriteOptional = favoriteRepository.findByUserIdAndItemId(userId, itemId);
            if (favoriteOptional.isEmpty()) {
                throw new FavoriteNotFoundException(userId, itemId);
            }

            // 删除收藏记录
            favoriteRepository.deleteByUserIdAndItemId(userId, itemId);

            // 更新收藏计数
            favoriteItemRepository.decrementFavoriteCount(itemId);

            // 清除相关缓存
            clearFavoriteCache(itemId);

            logger.info("用户 {} 取消收藏了内容 {}", userId, itemId);
            return ApiResponse.success("取消收藏成功");

        } catch (FavoriteException e) {
            // 重新抛出收藏相关异常，让全局异常处理器处理
            throw e;
        } catch (Exception e) {
            logger.error("取消收藏操作失败: userId={}, itemId={}", userId, itemId, e);
            throw new FavoriteException("取消收藏失败，请稍后重试", e);
        }
    }
    
    @Override
    @Transactional(readOnly = true)
    public ApiResponse<Boolean> isFavorited(Long userId, Long itemId) {
        try {
            boolean isFavorited = favoriteRepository.existsByUserIdAndItemId(userId, itemId);
            return ApiResponse.success(isFavorited);
        } catch (Exception e) {
            logger.error("检查收藏状态失败: userId={}, itemId={}", userId, itemId, e);
            return ApiResponse.error("检查收藏状态失败");
        }
    }
    
    @Override
    @Transactional(readOnly = true)
    public ApiResponse<PagedResponse<FavoriteDto>> getUserFavorites(Long userId, int page, int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Favorite> favoritePage = favoriteRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);

            List<FavoriteDto> favoriteDtos = favoritePage.getContent().stream()
                    .map(this::convertToFavoriteDto)
                    .collect(Collectors.toList());

            PagedResponse<FavoriteDto> response = new PagedResponse<>();
            response.setContent(favoriteDtos);
            response.setPage(page);
            response.setSize(size);
            response.setTotalElements(favoritePage.getTotalElements());
            response.setTotalPages(favoritePage.getTotalPages());
            response.setFirst(favoritePage.isFirst());
            response.setLast(favoritePage.isLast());
            response.setEmpty(favoritePage.isEmpty());

            return ApiResponse.success(response);

        } catch (Exception e) {
            logger.error("获取用户收藏列表失败: userId={}", userId, e);
            return ApiResponse.error("获取收藏列表失败");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public ApiResponse<PagedResponse<FavoriteDto>> getUserFavoritesByType(Long userId, ItemType itemType, int page, int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Favorite> favoritePage = favoriteRepository.findByUserIdAndItemTypeOrderByCreatedAtDesc(userId, itemType, pageable);

            List<FavoriteDto> favoriteDtos = favoritePage.getContent().stream()
                    .map(this::convertToFavoriteDto)
                    .collect(Collectors.toList());

            PagedResponse<FavoriteDto> response = new PagedResponse<>();
            response.setContent(favoriteDtos);
            response.setPage(page);
            response.setSize(size);
            response.setTotalElements(favoritePage.getTotalElements());
            response.setTotalPages(favoritePage.getTotalPages());
            response.setFirst(favoritePage.isFirst());
            response.setLast(favoritePage.isLast());
            response.setEmpty(favoritePage.isEmpty());

            return ApiResponse.success(response);

        } catch (Exception e) {
            logger.error("获取用户特定类型收藏列表失败: userId={}, itemType={}", userId, itemType, e);
            return ApiResponse.error("获取收藏列表失败");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public ApiResponse<PagedResponse<FavoriteItemDto>> getFavoriteRanking(int page, int size, ItemType itemType) {
        try {
            // 尝试从缓存获取排行榜
            PagedResponse<FavoriteItemDto> cachedRanking = cacheService.getRankingCache(page, size, itemType);
            if (cachedRanking != null) {
                return ApiResponse.success(cachedRanking);
            }

            Pageable pageable = PageRequest.of(page, size);
            Page<FavoriteItem> itemPage;

            if (itemType != null) {
                itemPage = favoriteItemRepository.findByItemTypeOrderByFavoriteCountDesc(itemType, pageable);
            } else {
                itemPage = favoriteItemRepository.findAllByOrderByFavoriteCountDesc(pageable);
            }

            List<FavoriteItemDto> itemDtos = itemPage.getContent().stream()
                    .map(item -> new FavoriteItemDto(item))
                    .collect(Collectors.toList());

            PagedResponse<FavoriteItemDto> response = new PagedResponse<>();
            response.setContent(itemDtos);
            response.setPage(page);
            response.setSize(size);
            response.setTotalElements(itemPage.getTotalElements());
            response.setTotalPages(itemPage.getTotalPages());
            response.setFirst(itemPage.isFirst());
            response.setLast(itemPage.isLast());
            response.setEmpty(itemPage.isEmpty());

            // 缓存排行榜数据
            cacheService.setRankingCache(page, size, itemType, response);

            return ApiResponse.success(response);

        } catch (Exception e) {
            logger.error("获取收藏排行榜失败: itemType={}", itemType, e);
            return ApiResponse.error("获取排行榜失败");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public ApiResponse<FavoriteStatsDto> getFavoriteStats(Long itemId, Long userId) {
        try {
            // 尝试从缓存获取统计信息
            FavoriteStatsDto cachedStats = cacheService.getStatsCache(itemId, userId);
            if (cachedStats != null) {
                return ApiResponse.success(cachedStats);
            }

            Optional<FavoriteItem> itemOptional = favoriteItemRepository.findById(itemId);
            if (itemOptional.isEmpty()) {
                return ApiResponse.error("内容不存在");
            }

            FavoriteItem item = itemOptional.get();

            // 获取收藏统计信息
            Integer favoriteCount = item.getFavoriteCount();

            // 检查用户是否已收藏
            boolean isFavorited = false;
            if (userId != null) {
                isFavorited = favoriteRepository.existsByUserIdAndItemId(userId, itemId);
            }

            // 获取最后收藏时间（简化处理，实际项目中可以优化查询）
            LocalDateTime lastFavoriteTime = null;
            try {
                Pageable pageable = PageRequest.of(0, 1);
                Page<Favorite> recentFavoritePage = favoriteRepository.findByItemIdOrderByCreatedAtDesc(itemId, pageable);
                if (!recentFavoritePage.isEmpty()) {
                    lastFavoriteTime = recentFavoritePage.getContent().get(0).getCreatedAt();
                }
            } catch (Exception e) {
                logger.warn("获取最后收藏时间失败: itemId={}", itemId, e);
            }

            FavoriteStatsDto statsDto = new FavoriteStatsDto(itemId, favoriteCount, isFavorited, lastFavoriteTime);

            // 缓存统计信息
            cacheService.setStatsCache(itemId, userId, statsDto);

            return ApiResponse.success(statsDto);

        } catch (Exception e) {
            logger.error("获取收藏统计信息失败: itemId={}", itemId, e);
            return ApiResponse.error("获取统计信息失败");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public ApiResponse<PagedResponse<FavoriteItemDto>> searchFavoriteItems(String keyword, int page, int size, Long userId) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<FavoriteItem> itemPage = favoriteItemRepository.searchByKeyword(keyword, pageable);

            List<FavoriteItemDto> itemDtos = itemPage.getContent().stream()
                    .map(item -> {
                        boolean isFavorited = false;
                        if (userId != null) {
                            isFavorited = favoriteRepository.existsByUserIdAndItemId(userId, item.getId());
                        }
                        return new FavoriteItemDto(item, isFavorited);
                    })
                    .collect(Collectors.toList());

            PagedResponse<FavoriteItemDto> response = new PagedResponse<>();
            response.setContent(itemDtos);
            response.setPage(page);
            response.setSize(size);
            response.setTotalElements(itemPage.getTotalElements());
            response.setTotalPages(itemPage.getTotalPages());
            response.setFirst(itemPage.isFirst());
            response.setLast(itemPage.isLast());
            response.setEmpty(itemPage.isEmpty());

            return ApiResponse.success(response);

        } catch (Exception e) {
            logger.error("搜索收藏内容失败: keyword={}", keyword, e);
            return ApiResponse.error("搜索失败");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public ApiResponse<PagedResponse<FavoriteItemDto>> getRecentItems(int page, int size, ItemType itemType, Long userId) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<FavoriteItem> itemPage;

            if (itemType != null) {
                itemPage = favoriteItemRepository.findByItemTypeOrderByCreatedAtDesc(itemType, pageable);
            } else {
                itemPage = favoriteItemRepository.findAllByOrderByCreatedAtDesc(pageable);
            }

            List<FavoriteItemDto> itemDtos = itemPage.getContent().stream()
                    .map(item -> {
                        boolean isFavorited = false;
                        if (userId != null) {
                            isFavorited = favoriteRepository.existsByUserIdAndItemId(userId, item.getId());
                        }
                        return new FavoriteItemDto(item, isFavorited);
                    })
                    .collect(Collectors.toList());

            PagedResponse<FavoriteItemDto> response = new PagedResponse<>();
            response.setContent(itemDtos);
            response.setPage(page);
            response.setSize(size);
            response.setTotalElements(itemPage.getTotalElements());
            response.setTotalPages(itemPage.getTotalPages());
            response.setFirst(itemPage.isFirst());
            response.setLast(itemPage.isLast());
            response.setEmpty(itemPage.isEmpty());

            return ApiResponse.success(response);

        } catch (Exception e) {
            logger.error("获取最近内容失败: itemType={}", itemType, e);
            return ApiResponse.error("获取最近内容失败");
        }
    }

    @Override
    public ApiResponse<FavoriteItemDto> createFavoriteItem(String title, String description, ItemType itemType, String contentUrl, String thumbnailUrl) {
        try {
            FavoriteItem item = new FavoriteItem(title, description, itemType, contentUrl, thumbnailUrl);
            FavoriteItem savedItem = favoriteItemRepository.save(item);

            FavoriteItemDto itemDto = new FavoriteItemDto(savedItem);
            return ApiResponse.success(itemDto);

        } catch (Exception e) {
            logger.error("创建收藏内容失败: title={}", title, e);
            return ApiResponse.error("创建内容失败");
        }
    }

    @Override
    public ApiResponse<FavoriteItemDto> updateFavoriteItem(Long itemId, String title, String description, String contentUrl, String thumbnailUrl) {
        try {
            Optional<FavoriteItem> itemOptional = favoriteItemRepository.findById(itemId);
            if (itemOptional.isEmpty()) {
                return ApiResponse.error("内容不存在");
            }

            FavoriteItem item = itemOptional.get();
            item.setTitle(title);
            item.setDescription(description);
            item.setContentUrl(contentUrl);
            item.setThumbnailUrl(thumbnailUrl);

            FavoriteItem savedItem = favoriteItemRepository.save(item);
            FavoriteItemDto itemDto = new FavoriteItemDto(savedItem);

            return ApiResponse.success(itemDto);

        } catch (Exception e) {
            logger.error("更新收藏内容失败: itemId={}", itemId, e);
            return ApiResponse.error("更新内容失败");
        }
    }

    @Override
    public ApiResponse<String> deleteFavoriteItem(Long itemId) {
        try {
            Optional<FavoriteItem> itemOptional = favoriteItemRepository.findById(itemId);
            if (itemOptional.isEmpty()) {
                return ApiResponse.error("内容不存在");
            }

            // 删除所有相关的收藏记录
            favoriteRepository.deleteByItemId(itemId);

            // 删除内容本身
            favoriteItemRepository.deleteById(itemId);

            // 清除相关缓存
            clearFavoriteCache(itemId);

            logger.info("删除收藏内容: itemId={}", itemId);
            return ApiResponse.success("删除成功");

        } catch (Exception e) {
            logger.error("删除收藏内容失败: itemId={}", itemId, e);
            return ApiResponse.error("删除内容失败");
        }
    }

    /**
     * 检查用户操作速率限制
     *
     * @param userId 用户ID
     * @return 是否允许操作
     */
    private boolean checkRateLimit(Long userId) {
        try {
            String key = RATE_LIMIT_KEY + userId;
            Object currentCountObj = redisService.get(key);

            if (currentCountObj == null) {
                // 第一次操作，设置计数为1
                redisService.set(key, "1", Duration.ofSeconds(RATE_LIMIT_WINDOW_SECONDS));
                return true;
            }

            int count = Integer.parseInt(currentCountObj.toString());
            if (count >= RATE_LIMIT_MAX_REQUESTS) {
                return false; // 超过限制
            }

            // 增加计数
            redisService.increment(key);
            return true;

        } catch (Exception e) {
            logger.warn("检查速率限制失败: userId={}", userId, e);
            return true; // 出错时允许操作
        }
    }

    /**
     * 清除收藏相关缓存
     *
     * @param itemId 内容ID
     */
    private void clearFavoriteCache(Long itemId) {
        try {
            // 使用缓存服务清除相关缓存
            cacheService.evictItemRelatedCache(itemId);

            // 清除Redis中的速率限制和其他临时缓存
            String countKey = FAVORITE_COUNT_CACHE_KEY + itemId;
            redisService.delete(countKey);

        } catch (Exception e) {
            logger.warn("清除缓存失败: itemId={}", itemId, e);
        }
    }

    /**
     * 将Favorite实体转换为FavoriteDto
     *
     * @param favorite 收藏实体
     * @return FavoriteDto
     */
    private FavoriteDto convertToFavoriteDto(Favorite favorite) {
        try {
            Optional<FavoriteItem> itemOptional = favoriteItemRepository.findById(favorite.getItemId());
            FavoriteItem item = itemOptional.orElse(null);
            return new FavoriteDto(favorite, item);
        } catch (Exception e) {
            logger.warn("转换FavoriteDto失败: favoriteId={}", favorite.getId(), e);
            return new FavoriteDto(favorite, null);
        }
    }
}
