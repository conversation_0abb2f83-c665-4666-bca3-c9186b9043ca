// 模拟收藏服务
import type { ApiResponse, PagedResponse, FavoriteDto, FavoriteItemDto, FavoriteStatsDto } from '@/api'

// 模拟数据类型
interface MockFavoriteItem {
  id: number
  title: string
  description: string
  type: 'ARTICLE' | 'VIDEO' | 'IMAGE' | 'LINK'
  url?: string
  imageUrl?: string
  author: string
  createdAt: string
  favoriteCount: number
  viewCount: number
  tags: string[]
}

// 模拟数据存储
class MockFavoriteStorage {
  private static instance: MockFavoriteStorage
  private favorites: Map<string, Set<number>> = new Map() // userId -> Set<itemId>
  private items: Map<number, MockFavoriteItem> = new Map()
  private userFavorites: Map<string, FavoriteDto[]> = new Map()

  static getInstance(): MockFavoriteStorage {
    if (!MockFavoriteStorage.instance) {
      MockFavoriteStorage.instance = new MockFavoriteStorage()
      MockFavoriteStorage.instance.initializeData()
    }
    return MockFavoriteStorage.instance
  }

  private initializeData() {
    // 初始化模拟数据
    const mockItems: MockFavoriteItem[] = [
      {
        id: 1,
        title: 'Vue 3 完整指南',
        description: '深入学习 Vue 3 的所有新特性，包括 Composition API、Teleport、Fragments 等',
        type: 'ARTICLE',
        url: 'https://example.com/vue3-guide',
        imageUrl: 'https://picsum.photos/300/200?random=1',
        author: '前端大师',
        createdAt: '2024-01-15T10:30:00Z',
        favoriteCount: 1250,
        viewCount: 15600,
        tags: ['Vue', 'JavaScript', '前端开发']
      },
      {
        id: 2,
        title: 'TypeScript 最佳实践',
        description: '掌握 TypeScript 的高级特性和最佳实践，提升代码质量',
        type: 'VIDEO',
        url: 'https://example.com/typescript-best-practices',
        imageUrl: 'https://picsum.photos/300/200?random=2',
        author: 'TS专家',
        createdAt: '2024-01-20T14:15:00Z',
        favoriteCount: 980,
        viewCount: 12300,
        tags: ['TypeScript', 'JavaScript', '类型系统']
      },
      {
        id: 3,
        title: '现代 CSS 布局技巧',
        description: '学习 Grid、Flexbox 和其他现代 CSS 布局技术',
        type: 'ARTICLE',
        url: 'https://example.com/modern-css-layout',
        imageUrl: 'https://picsum.photos/300/200?random=3',
        author: 'CSS大神',
        createdAt: '2024-01-25T09:45:00Z',
        favoriteCount: 756,
        viewCount: 9800,
        tags: ['CSS', '布局', '响应式设计']
      },
      {
        id: 4,
        title: 'Node.js 性能优化',
        description: '深入了解 Node.js 性能优化的各种技巧和工具',
        type: 'VIDEO',
        url: 'https://example.com/nodejs-performance',
        imageUrl: 'https://picsum.photos/300/200?random=4',
        author: '后端架构师',
        createdAt: '2024-02-01T16:20:00Z',
        favoriteCount: 642,
        viewCount: 8500,
        tags: ['Node.js', '性能优化', '后端开发']
      },
      {
        id: 5,
        title: 'React Hooks 深度解析',
        description: '全面理解 React Hooks 的工作原理和使用场景',
        type: 'ARTICLE',
        url: 'https://example.com/react-hooks-deep-dive',
        imageUrl: 'https://picsum.photos/300/200?random=5',
        author: 'React专家',
        createdAt: '2024-02-05T11:10:00Z',
        favoriteCount: 1100,
        viewCount: 14200,
        tags: ['React', 'Hooks', '状态管理']
      },
      {
        id: 6,
        title: 'Docker 容器化实战',
        description: '从零开始学习 Docker 容器化技术和最佳实践',
        type: 'VIDEO',
        url: 'https://example.com/docker-practical',
        imageUrl: 'https://picsum.photos/300/200?random=6',
        author: 'DevOps工程师',
        createdAt: '2024-02-10T13:30:00Z',
        favoriteCount: 890,
        viewCount: 11700,
        tags: ['Docker', '容器化', 'DevOps']
      },
      {
        id: 7,
        title: 'GraphQL API 设计',
        description: '学习如何设计和实现高效的 GraphQL API',
        type: 'ARTICLE',
        url: 'https://example.com/graphql-api-design',
        imageUrl: 'https://picsum.photos/300/200?random=7',
        author: 'API设计师',
        createdAt: '2024-02-15T15:45:00Z',
        favoriteCount: 567,
        viewCount: 7300,
        tags: ['GraphQL', 'API设计', '后端开发']
      },
      {
        id: 8,
        title: 'Web 安全防护指南',
        description: '了解常见的 Web 安全威胁和防护措施',
        type: 'ARTICLE',
        url: 'https://example.com/web-security-guide',
        imageUrl: 'https://picsum.photos/300/200?random=8',
        author: '安全专家',
        createdAt: '2024-02-20T10:15:00Z',
        favoriteCount: 1350,
        viewCount: 18900,
        tags: ['Web安全', '网络安全', '防护']
      },
      {
        id: 9,
        title: 'MongoDB 数据建模',
        description: '掌握 MongoDB 的数据建模技巧和查询优化',
        type: 'VIDEO',
        url: 'https://example.com/mongodb-modeling',
        imageUrl: 'https://picsum.photos/300/200?random=9',
        author: '数据库专家',
        createdAt: '2024-02-25T12:00:00Z',
        favoriteCount: 423,
        viewCount: 5600,
        tags: ['MongoDB', '数据库', 'NoSQL']
      },
      {
        id: 10,
        title: 'Webpack 5 配置详解',
        description: '深入了解 Webpack 5 的新特性和配置技巧',
        type: 'ARTICLE',
        url: 'https://example.com/webpack5-config',
        imageUrl: 'https://picsum.photos/300/200?random=10',
        author: '构建工具专家',
        createdAt: '2024-03-01T14:30:00Z',
        favoriteCount: 789,
        viewCount: 10200,
        tags: ['Webpack', '构建工具', '前端工程化']
      }
    ]

    // 存储模拟数据
    mockItems.forEach(item => {
      this.items.set(item.id, item)
    })
  }

  private getCurrentUserId(): string {
    // 从 localStorage 获取当前用户信息
    const userStr = localStorage.getItem('user')
    if (userStr) {
      const user = JSON.parse(userStr)
      return user.id?.toString() || 'anonymous'
    }
    return 'anonymous'
  }

  getUserFavorites(userId: string): Set<number> {
    if (!this.favorites.has(userId)) {
      this.favorites.set(userId, new Set())
    }
    return this.favorites.get(userId)!
  }

  addFavorite(itemId: number): boolean {
    const userId = this.getCurrentUserId()
    const userFavorites = this.getUserFavorites(userId)
    
    if (!userFavorites.has(itemId)) {
      userFavorites.add(itemId)
      
      // 增加收藏数
      const item = this.items.get(itemId)
      if (item) {
        item.favoriteCount++
      }
      
      return true
    }
    return false
  }

  removeFavorite(itemId: number): boolean {
    const userId = this.getCurrentUserId()
    const userFavorites = this.getUserFavorites(userId)
    
    if (userFavorites.has(itemId)) {
      userFavorites.delete(itemId)
      
      // 减少收藏数
      const item = this.items.get(itemId)
      if (item) {
        item.favoriteCount = Math.max(0, item.favoriteCount - 1)
      }
      
      return true
    }
    return false
  }

  isFavorited(itemId: number): boolean {
    const userId = this.getCurrentUserId()
    return this.getUserFavorites(userId).has(itemId)
  }

  getMyFavorites(page: number = 0, size: number = 20, itemType?: string): PagedResponse<FavoriteDto> {
    const userId = this.getCurrentUserId()
    const userFavorites = this.getUserFavorites(userId)
    
    let favoriteItems: FavoriteDto[] = []
    
    for (const itemId of userFavorites) {
      const item = this.items.get(itemId)
      if (item && (!itemType || item.type === itemType)) {
        favoriteItems.push({
          id: Date.now() + Math.random(), // 生成唯一ID
          itemId: item.id,
          itemType: item.type,
          itemTitle: item.title,
          itemDescription: item.description,
          itemUrl: item.url,
          itemImageUrl: item.imageUrl,
          itemAuthor: item.author,
          createdAt: new Date().toISOString(),
          tags: item.tags
        })
      }
    }

    // 按创建时间排序
    favoriteItems.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

    // 分页
    const start = page * size
    const end = start + size
    const content = favoriteItems.slice(start, end)

    return {
      content,
      page,
      size,
      totalElements: favoriteItems.length,
      totalPages: Math.ceil(favoriteItems.length / size),
      first: page === 0,
      last: end >= favoriteItems.length
    }
  }

  getFavoriteRanking(page: number = 0, size: number = 20, itemType?: string): PagedResponse<FavoriteItemDto> {
    let items = Array.from(this.items.values())
    
    if (itemType) {
      items = items.filter(item => item.type === itemType)
    }

    // 按收藏数排序
    items.sort((a, b) => b.favoriteCount - a.favoriteCount)

    // 分页
    const start = page * size
    const end = start + size
    const content = items.slice(start, end).map(item => ({
      id: item.id,
      title: item.title,
      description: item.description,
      type: item.type,
      url: item.url,
      imageUrl: item.imageUrl,
      author: item.author,
      createdAt: item.createdAt,
      favoriteCount: item.favoriteCount,
      viewCount: item.viewCount,
      tags: item.tags,
      isFavorited: this.isFavorited(item.id)
    }))

    return {
      content,
      page,
      size,
      totalElements: items.length,
      totalPages: Math.ceil(items.length / size),
      first: page === 0,
      last: end >= items.length
    }
  }

  getFavoriteStats(itemId: number): FavoriteStatsDto | null {
    const item = this.items.get(itemId)
    if (!item) return null

    return {
      itemId: item.id,
      favoriteCount: item.favoriteCount,
      viewCount: item.viewCount,
      isFavorited: this.isFavorited(item.id)
    }
  }

  searchFavoriteItems(keyword: string, page: number = 0, size: number = 20): PagedResponse<FavoriteItemDto> {
    const items = Array.from(this.items.values())
    const filteredItems = items.filter(item => 
      item.title.toLowerCase().includes(keyword.toLowerCase()) ||
      item.description.toLowerCase().includes(keyword.toLowerCase()) ||
      item.tags.some(tag => tag.toLowerCase().includes(keyword.toLowerCase()))
    )

    // 按相关性排序（简单的匹配度计算）
    filteredItems.sort((a, b) => {
      const aScore = this.calculateRelevanceScore(a, keyword)
      const bScore = this.calculateRelevanceScore(b, keyword)
      return bScore - aScore
    })

    // 分页
    const start = page * size
    const end = start + size
    const content = filteredItems.slice(start, end).map(item => ({
      id: item.id,
      title: item.title,
      description: item.description,
      type: item.type,
      url: item.url,
      imageUrl: item.imageUrl,
      author: item.author,
      createdAt: item.createdAt,
      favoriteCount: item.favoriteCount,
      viewCount: item.viewCount,
      tags: item.tags,
      isFavorited: this.isFavorited(item.id)
    }))

    return {
      content,
      page,
      size,
      totalElements: filteredItems.length,
      totalPages: Math.ceil(filteredItems.length / size),
      first: page === 0,
      last: end >= filteredItems.length
    }
  }

  private calculateRelevanceScore(item: MockFavoriteItem, keyword: string): number {
    let score = 0
    const lowerKeyword = keyword.toLowerCase()
    
    if (item.title.toLowerCase().includes(lowerKeyword)) score += 10
    if (item.description.toLowerCase().includes(lowerKeyword)) score += 5
    item.tags.forEach(tag => {
      if (tag.toLowerCase().includes(lowerKeyword)) score += 3
    })
    
    return score
  }

  getRecentItems(page: number = 0, size: number = 20, itemType?: string): PagedResponse<FavoriteItemDto> {
    let items = Array.from(this.items.values())
    
    if (itemType) {
      items = items.filter(item => item.type === itemType)
    }

    // 按创建时间排序
    items.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

    // 分页
    const start = page * size
    const end = start + size
    const content = items.slice(start, end).map(item => ({
      id: item.id,
      title: item.title,
      description: item.description,
      type: item.type,
      url: item.url,
      imageUrl: item.imageUrl,
      author: item.author,
      createdAt: item.createdAt,
      favoriteCount: item.favoriteCount,
      viewCount: item.viewCount,
      tags: item.tags,
      isFavorited: this.isFavorited(item.id)
    }))

    return {
      content,
      page,
      size,
      totalElements: items.length,
      totalPages: Math.ceil(items.length / size),
      first: page === 0,
      last: end >= items.length
    }
  }
}

// 模拟 API 服务
export class MockFavoriteService {
  private storage = MockFavoriteStorage.getInstance()

  // 模拟网络延迟
  private async delay(ms: number = 300): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  async addFavorite(itemId: number): Promise<ApiResponse<string>> {
    await this.delay()
    
    const success = this.storage.addFavorite(itemId)
    
    return {
      success,
      message: success ? '收藏成功' : '已经收藏过了',
      data: success ? '收藏成功' : '已经收藏过了'
    }
  }

  async removeFavorite(itemId: number): Promise<ApiResponse<string>> {
    await this.delay()
    
    const success = this.storage.removeFavorite(itemId)
    
    return {
      success,
      message: success ? '取消收藏成功' : '未收藏该内容',
      data: success ? '取消收藏成功' : '未收藏该内容'
    }
  }

  async getFavoriteStatus(itemId: number): Promise<ApiResponse<boolean>> {
    await this.delay(100)
    
    return {
      success: true,
      message: '获取收藏状态成功',
      data: this.storage.isFavorited(itemId)
    }
  }

  async getMyFavorites(page: number = 0, size: number = 20, itemType?: string): Promise<ApiResponse<PagedResponse<FavoriteDto>>> {
    await this.delay()
    
    const data = this.storage.getMyFavorites(page, size, itemType)
    
    return {
      success: true,
      message: '获取我的收藏成功',
      data
    }
  }

  async getFavoriteRanking(page: number = 0, size: number = 20, itemType?: string): Promise<ApiResponse<PagedResponse<FavoriteItemDto>>> {
    await this.delay()
    
    const data = this.storage.getFavoriteRanking(page, size, itemType)
    
    return {
      success: true,
      message: '获取收藏排行榜成功',
      data
    }
  }

  async getFavoriteStats(itemId: number): Promise<ApiResponse<FavoriteStatsDto>> {
    await this.delay(100)
    
    const data = this.storage.getFavoriteStats(itemId)
    
    if (data) {
      return {
        success: true,
        message: '获取收藏统计成功',
        data
      }
    } else {
      return {
        success: false,
        message: '内容不存在'
      }
    }
  }

  async searchFavoriteItems(keyword: string, page: number = 0, size: number = 20): Promise<ApiResponse<PagedResponse<FavoriteItemDto>>> {
    await this.delay()
    
    const data = this.storage.searchFavoriteItems(keyword, page, size)
    
    return {
      success: true,
      message: '搜索成功',
      data
    }
  }

  async getRecentItems(page: number = 0, size: number = 20, itemType?: string): Promise<ApiResponse<PagedResponse<FavoriteItemDto>>> {
    await this.delay()
    
    const data = this.storage.getRecentItems(page, size, itemType)
    
    return {
      success: true,
      message: '获取最近内容成功',
      data
    }
  }
}

// 导出单例实例
export const mockFavoriteService = new MockFavoriteService()