# 購物車和支付寶支付功能測試計劃

## 測試環境準備

### 1. 啟動後端服務
```bash
cd backend/backend
mvn spring-boot:run
```
後端服務將在 http://localhost:8080 啟動

### 2. 啟動前端服務
```bash
cd frontend
npm install  # 如果還沒安裝依賴
npm run dev
```
前端服務將在 http://localhost:3000 啟動

### 3. 確認數據庫連接
- MySQL 數據庫：java_springboot_redis_mail_login_test_250708
- Redis 服務：localhost:6379

### 4. 支付寶沙箱配置
- 已配置支付寶沙箱環境
- 需要 ngrok 提供公網回調地址

## 測試用例

### 測試用例1：購物車基本功能
**測試步驟**：
1. 用戶登錄系統
2. 瀏覽商品頁面
3. 點擊"加入購物車"按鈕
4. 進入購物車頁面
5. 驗證商品已添加到購物車
6. 調整商品數量
7. 選中/取消選中商品
8. 刪除商品
9. 清空購物車

**預期結果**：
- 商品成功添加到購物車
- 數量調整正常
- 選中狀態切換正常
- 刪除和清空功能正常

### 測試用例2：訂單創建流程
**測試步驟**：
1. 在購物車中選中商品
2. 點擊"去結算"
3. 填寫收貨信息
4. 確認商品清單
5. 提交訂單
6. 驗證訂單創建成功

**預期結果**：
- 訂單成功創建
- 訂單號生成正確
- 商品信息保存正確
- 庫存正確扣減

### 測試用例3：支付寶支付流程
**測試步驟**：
1. 創建訂單後進入支付頁面
2. 點擊"立即支付"
3. 跳轉到支付寶沙箱環境
4. 使用測試賬號完成支付
5. 返回系統查看支付狀態

**預期結果**：
- 成功跳轉到支付寶
- 支付完成後回調正常
- 訂單狀態更新為已支付
- 支付記錄正確保存

### 測試用例4：訂單管理功能
**測試步驟**：
1. 進入"我的訂單"頁面
2. 查看訂單列表
3. 篩選不同狀態的訂單
4. 查看訂單詳情
5. 取消待付款訂單
6. 確認收貨

**預期結果**：
- 訂單列表正確顯示
- 狀態篩選功能正常
- 訂單操作功能正常

## API 測試

### 購物車 API
- GET /api/cart - 獲取購物車
- POST /api/cart/add - 添加商品到購物車
- PUT /api/cart/update/{cartItemId} - 更新商品數量
- DELETE /api/cart/remove/{cartItemId} - 移除商品
- DELETE /api/cart/clear - 清空購物車

### 訂單 API
- POST /api/orders/create-from-cart - 從購物車創建訂單
- POST /api/orders/create-direct - 直接購買
- GET /api/orders - 獲取訂單列表
- GET /api/orders/{orderId} - 獲取訂單詳情
- PUT /api/orders/{orderId}/cancel - 取消訂單

### 支付 API
- POST /api/payment/alipay/create - 發起支付寶支付
- POST /api/payment/alipay/callback - 支付寶回調
- GET /api/payment/status/{orderId} - 查詢支付狀態

## 數據庫驗證

### 檢查表結構
```sql
-- 購物車表
DESCRIBE carts;
DESCRIBE cart_items;

-- 訂單表
DESCRIBE orders;
DESCRIBE order_items;

-- 支付表
DESCRIBE payments;
```

### 檢查數據完整性
```sql
-- 檢查購物車數據
SELECT * FROM carts WHERE user_id = 1;
SELECT * FROM cart_items WHERE cart_id = 1;

-- 檢查訂單數據
SELECT * FROM orders WHERE user_id = 1;
SELECT * FROM order_items WHERE order_id = 1;

-- 檢查支付數據
SELECT * FROM payments WHERE order_id = 1;
```

## 注意事項

1. **支付寶回調地址**：需要使用 ngrok 提供公網地址
2. **測試賬號**：使用沙箱提供的測試買家賬號
3. **庫存檢查**：確保商品有足夠庫存
4. **用戶認證**：確保用戶已登錄
5. **錯誤處理**：測試各種異常情況

## 測試數據準備

### 創建測試商品
```sql
INSERT INTO products (name, description, category_id, price, stock, status, created_by) 
VALUES ('測試商品1', '用於測試購物車功能', 1, 99.99, 100, 1, 1);
```

### 創建測試用戶
確保有可用的測試用戶賬號進行登錄測試。

## 預期問題和解決方案

1. **跨域問題**：確保前後端跨域配置正確
2. **認證問題**：確保JWT token正確傳遞
3. **支付回調**：確保ngrok正確配置
4. **數據庫連接**：確保MySQL和Redis服務正常

## 測試完成標準

- [ ] 購物車功能完全正常
- [ ] 訂單創建流程無誤
- [ ] 支付寶支付集成成功
- [ ] 訂單狀態流轉正確
- [ ] 所有API響應正常
- [ ] 數據庫數據完整
- [ ] 前端界面交互正常
