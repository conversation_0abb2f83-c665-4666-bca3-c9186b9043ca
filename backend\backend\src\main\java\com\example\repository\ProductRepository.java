package com.example.repository;

import com.example.entity.Product;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 商品數據訪問層
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {
    
    /**
     * 根據狀態查詢商品（排除已刪除）
     */
    @Query("SELECT p FROM Product p WHERE p.status != -1 ORDER BY p.sortOrder ASC, p.createdAt DESC")
    Page<Product> findActiveProducts(Pageable pageable);
    
    /**
     * 根據分類ID查詢商品
     */
    @Query("SELECT p FROM Product p WHERE p.categoryId = :categoryId AND p.status = :status ORDER BY p.sortOrder ASC, p.createdAt DESC")
    Page<Product> findByCategoryIdAndStatus(@Param("categoryId") Long categoryId, @Param("status") Integer status, Pageable pageable);
    
    /**
     * 根據分類ID列表查詢商品（支持多級分類查詢）
     */
    @Query("SELECT p FROM Product p WHERE p.categoryId IN :categoryIds AND p.status = :status ORDER BY p.sortOrder ASC, p.createdAt DESC")
    Page<Product> findByCategoryIdsAndStatus(@Param("categoryIds") List<Long> categoryIds, @Param("status") Integer status, Pageable pageable);
    
    /**
     * 多條件查詢商品（支持動態排序）
     */
    @Query("SELECT p FROM Product p WHERE " +
           "(:name IS NULL OR p.name LIKE %:name%) AND " +
           "(:categoryId IS NULL OR p.categoryId = :categoryId) AND " +
           "(:status IS NULL OR p.status = :status) AND " +
           "(:minPrice IS NULL OR p.price >= :minPrice) AND " +
           "(:maxPrice IS NULL OR p.price <= :maxPrice) AND " +
           "(:brand IS NULL OR p.brand LIKE %:brand%) AND " +
           "(:isRecommended IS NULL OR p.isRecommended = :isRecommended) AND " +
           "(:isHot IS NULL OR p.isHot = :isHot)")
    Page<Product> findProductsWithFilters(
            @Param("name") String name,
            @Param("categoryId") Long categoryId,
            @Param("status") Integer status,
            @Param("minPrice") BigDecimal minPrice,
            @Param("maxPrice") BigDecimal maxPrice,
            @Param("brand") String brand,
            @Param("isRecommended") Integer isRecommended,
            @Param("isHot") Integer isHot,
            Pageable pageable);
    
    /**
     * 查詢推薦商品
     */
    @Query("SELECT p FROM Product p WHERE p.isRecommended = 1 AND p.status = 1 ORDER BY p.sortOrder ASC, p.createdAt DESC")
    Page<Product> findRecommendedProducts(Pageable pageable);
    
    /**
     * 查詢熱門商品
     */
    @Query("SELECT p FROM Product p WHERE p.isHot = 1 AND p.status = 1 ORDER BY p.soldCount DESC, p.sortOrder ASC")
    Page<Product> findHotProducts(Pageable pageable);
    
    /**
     * 查詢最新商品
     */
    @Query("SELECT p FROM Product p WHERE p.status = 1 ORDER BY p.createdAt DESC")
    Page<Product> findLatestProducts(Pageable pageable);
    
    /**
     * 根據銷量排序查詢商品
     */
    @Query("SELECT p FROM Product p WHERE p.status = 1 ORDER BY p.soldCount DESC, p.createdAt DESC")
    Page<Product> findProductsBySoldCount(Pageable pageable);
    
    /**
     * 根據價格排序查詢商品
     */
    @Query("SELECT p FROM Product p WHERE p.status = 1 ORDER BY p.price ASC")
    Page<Product> findProductsByPriceAsc(Pageable pageable);
    
    @Query("SELECT p FROM Product p WHERE p.status = 1 ORDER BY p.price DESC")
    Page<Product> findProductsByPriceDesc(Pageable pageable);
    
    /**
     * 搜索商品（按名稱、描述、品牌）
     */
    @Query("SELECT p FROM Product p WHERE " +
           "(p.name LIKE %:keyword% OR p.description LIKE %:keyword% OR p.brand LIKE %:keyword%) " +
           "AND p.status = 1 " +
           "ORDER BY p.sortOrder ASC, p.createdAt DESC")
    Page<Product> searchProducts(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 檢查商品名稱是否存在
     */
    boolean existsByNameAndIdNot(String name, Long id);
    
    /**
     * 檢查商品名稱是否存在（新增時使用）
     */
    boolean existsByName(String name);
    
    /**
     * 統計分類下的商品數量
     */
    @Query("SELECT COUNT(p) FROM Product p WHERE p.categoryId = :categoryId AND p.status != -1")
    long countByCategoryId(@Param("categoryId") Long categoryId);
    
    /**
     * 統計各狀態商品數量
     */
    @Query("SELECT p.status, COUNT(p) FROM Product p WHERE p.status != -1 GROUP BY p.status")
    List<Object[]> countByStatus();
    
    /**
     * 查詢庫存不足的商品
     */
    @Query("SELECT p FROM Product p WHERE p.stock <= :threshold AND p.status = 1 ORDER BY p.stock ASC")
    List<Product> findLowStockProducts(@Param("threshold") Integer threshold);
    
    /**
     * 更新商品庫存
     */
    @Modifying
    @Query("UPDATE Product p SET p.stock = p.stock - :quantity WHERE p.id = :productId AND p.stock >= :quantity")
    int decreaseStock(@Param("productId") Long productId, @Param("quantity") Integer quantity);
    
    /**
     * 增加商品銷量
     */
    @Modifying
    @Query("UPDATE Product p SET p.soldCount = p.soldCount + :quantity WHERE p.id = :productId")
    int increaseSoldCount(@Param("productId") Long productId, @Param("quantity") Integer quantity);
    
    /**
     * 批量更新商品狀態
     */
    @Modifying
    @Query("UPDATE Product p SET p.status = :status WHERE p.id IN :productIds")
    int batchUpdateStatus(@Param("productIds") List<Long> productIds, @Param("status") Integer status);
    
    /**
     * 查詢相關商品（同分類）
     */
    @Query("SELECT p FROM Product p WHERE p.categoryId = :categoryId AND p.id != :excludeId AND p.status = 1 ORDER BY p.soldCount DESC")
    List<Product> findRelatedProducts(@Param("categoryId") Long categoryId, @Param("excludeId") Long excludeId, Pageable pageable);
    
    /**
     * 根據創建者查詢商品
     */
    @Query("SELECT p FROM Product p WHERE p.createdBy = :createdBy AND p.status != -1 ORDER BY p.createdAt DESC")
    Page<Product> findByCreatedBy(@Param("createdBy") Long createdBy, Pageable pageable);
}
