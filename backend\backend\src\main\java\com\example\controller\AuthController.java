package com.example.controller;

import com.example.dto.*;
import com.example.entity.User;
import com.example.service.EmailService;
import com.example.service.RefreshTokenService;
import com.example.service.UserService;
import com.example.util.JwtUtil;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*")
@Slf4j
public class AuthController {
    
    @Autowired
    private AuthenticationManager authenticationManager;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private EmailService emailService;
    
    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private RefreshTokenService refreshTokenService;
    
    /**
     * 用戶登入
     */
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<Map<String, Object>>> login(@Valid @RequestBody LoginRequest request) {
        try {
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword())
            );
            
            User user = (User) authentication.getPrincipal();
            String accessToken = jwtUtil.generateAccessToken(user);
            String refreshToken = refreshTokenService.generateRefreshToken(user);

            Map<String, Object> data = new HashMap<>();
            data.put("accessToken", accessToken);
            data.put("refreshToken", refreshToken);
            data.put("tokenType", "Bearer");
            data.put("expiresIn", 7200); // 2小時，單位：秒
            data.put("user", getUserInfo(user));
            
            log.info("用戶登入成功: {}", user.getUsername());
            return ResponseEntity.ok(ApiResponse.success("登入成功", data));
            
        } catch (AuthenticationException e) {
            log.warn("用戶登入失敗: {}", request.getUsername());
            return ResponseEntity.badRequest().body(ApiResponse.error("用戶名或密碼錯誤"));
        }
    }
    
    /**
     * 用戶註冊
     */
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<Map<String, Object>>> register(@Valid @RequestBody RegisterRequest request) {
        try {
            User user = userService.register(request);
            String accessToken = jwtUtil.generateAccessToken(user);
            String refreshToken = refreshTokenService.generateRefreshToken(user);

            Map<String, Object> data = new HashMap<>();
            data.put("accessToken", accessToken);
            data.put("refreshToken", refreshToken);
            data.put("tokenType", "Bearer");
            data.put("expiresIn", 7200); // 2小時，單位：秒
            data.put("user", getUserInfo(user));
            
            return ResponseEntity.ok(ApiResponse.success("註冊成功", data));
            
        } catch (Exception e) {
            log.error("用戶註冊失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 發送郵件驗證碼
     */
    @PostMapping("/send-verification-code")
    public ResponseEntity<ApiResponse<String>> sendVerificationCode(@Valid @RequestBody EmailVerificationRequest request) {
        try {
            EmailService.Type type = EmailService.Type.valueOf(request.getType().toUpperCase());

            // 如果是註冊驗證，檢查郵箱是否已被使用
            if (type == EmailService.Type.REGISTRATION && !userService.isEmailAvailable(request.getEmail())) {
                return ResponseEntity.badRequest().body(ApiResponse.error("該郵箱已被註冊"));
            }
            
            emailService.sendVerificationCode(request.getEmail(), type);
            String stats = emailService.getEmailSendStats(request.getEmail());
            
            return ResponseEntity.ok(ApiResponse.success("驗證碼發送成功", stats));
            
        } catch (Exception e) {
            log.error("發送驗證碼失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 檢查用戶名是否可用
     */
    @GetMapping("/check-username")
    public ResponseEntity<ApiResponse<Boolean>> checkUsername(@RequestParam String username) {
        boolean available = userService.isUsernameAvailable(username);
        return ResponseEntity.ok(ApiResponse.success(available));
    }
    
    /**
     * 檢查郵箱是否可用
     */
    @GetMapping("/check-email")
    public ResponseEntity<ApiResponse<Boolean>> checkEmail(@RequestParam String email) {
        boolean available = userService.isEmailAvailable(email);
        return ResponseEntity.ok(ApiResponse.success(available));
    }

    /**
     * 驗證郵件驗證碼
     */
    @PostMapping("/verify-code")
    public ResponseEntity<ApiResponse<Boolean>> verifyCode(@RequestParam String email, @RequestParam String code) {
        boolean verified = emailService.verifyCode(email, code);
        return ResponseEntity.ok(ApiResponse.success(verified));
    }
    
    /**
     * 刷新Access Token
     */
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<Map<String, Object>>> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
        try {
            String refreshToken = request.getRefreshToken();
            String username = jwtUtil.extractUsername(refreshToken);

            // 加載用戶信息
            User user = (User) userService.loadUserByUsername(username);

            // 生成新的Access Token
            String newAccessToken = refreshTokenService.refreshAccessToken(refreshToken, user);

            Map<String, Object> data = new HashMap<>();
            data.put("accessToken", newAccessToken);
            data.put("tokenType", "Bearer");
            data.put("expiresIn", 7200); // 2小時，單位：秒

            log.info("刷新Token成功: {}", username);
            return ResponseEntity.ok(ApiResponse.success("Token刷新成功", data));

        } catch (Exception e) {
            log.error("刷新Token失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("Token刷新失敗: " + e.getMessage()));
        }
    }

    /**
     * 用戶登出（撤銷Refresh Token）
     */
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<String>> logout(@Valid @RequestBody RefreshTokenRequest request) {
        try {
            String refreshToken = request.getRefreshToken();
            String username = jwtUtil.extractUsername(refreshToken);

            // 撤銷Refresh Token
            refreshTokenService.revokeRefreshToken(refreshToken);

            log.info("用戶登出成功: {}", username);
            return ResponseEntity.ok(ApiResponse.success("登出成功"));

        } catch (Exception e) {
            log.error("用戶登出失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("登出失敗: " + e.getMessage()));
        }
    }

    /**
     * 用戶全部設備登出（撤銷所有Refresh Token）
     */
    @PostMapping("/logout-all")
    public ResponseEntity<ApiResponse<String>> logoutAll(@Valid @RequestBody RefreshTokenRequest request) {
        try {
            String refreshToken = request.getRefreshToken();
            String username = jwtUtil.extractUsername(refreshToken);

            // 撤銷用戶所有Refresh Token
            refreshTokenService.revokeAllRefreshTokens(username);

            log.info("用戶全部設備登出成功: {}", username);
            return ResponseEntity.ok(ApiResponse.success("全部設備登出成功"));

        } catch (Exception e) {
            log.error("用戶全部設備登出失敗", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("全部設備登出失敗: " + e.getMessage()));
        }
    }

    /**
     * 獲取用戶信息（不包含敏感信息）
     */
    private Map<String, Object> getUserInfo(User user) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("username", user.getUsername());
        userInfo.put("email", user.getEmail());
        userInfo.put("realName", user.getRealName());
        userInfo.put("phoneNumber", user.getPhoneNumber());
        userInfo.put("emailVerified", user.getEmailVerified());
        userInfo.put("identityVerified", user.getIdentityVerified());
        userInfo.put("identityStatus", user.getIdentityStatus());
        userInfo.put("role", user.getRole());
        userInfo.put("createdAt", user.getCreatedAt());
        return userInfo;
    }
}
