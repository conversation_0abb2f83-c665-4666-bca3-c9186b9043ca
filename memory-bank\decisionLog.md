# Decision Log

This file records architectural and implementation decisions using a list format.
2025-07-10 15:48:02 - Log of updates made.

## [2025-07-24 10:19:16] 修復支付按鈕點擊問題的綜合解決方案

### Decision
通過三個關鍵修復解決支付按鈕點擊無效問題：1) 修復Order實體JSON序列化配置，2) 解決訂單列表API懶加載問題，3) 修復PaymentController用戶ID獲取邏輯。

### Rationale
- 原問題：前端支付頁面無法加載，支付按鈕點擊無效，控制台顯示 `/api/orders/2` 返回500錯誤
- 根本原因分析：
  1. Order實體 `@JsonIgnoreProperties` 忽略了 `orderItems` 和 `payment` 字段，導致前端無法獲取完整訂單數據
  2. 訂單列表API使用的查詢方法沒有預加載關聯數據，導致懶加載異常
  3. PaymentController的 `getUserId` 方法硬編碼返回1L，與實際用戶ID不匹配
- 解決方案：系統性修復所有相關問題，確保完整的支付流程正常工作

### Implementation Details
1. **Order實體修復**：
   - 移除 `@JsonIgnoreProperties` 中對 `orderItems` 和 `payment` 的忽略
   - 保留對 `hibernateLazyInitializer`、`handler`、`user` 的忽略以避免循環引用

2. **訂單列表API修復**：
   - 在OrderRepository中添加 `findByUserIdWithOrderItemsAndPayment` 預加載查詢方法
   - 修改OrderService的 `getUserOrders` 方法使用預加載查詢並手動實現分頁
   - 移除未使用的import以保持代碼整潔

3. **PaymentController用戶ID修復**：
   - 將OrderController的 `getUserId` 方法邏輯複製到PaymentController
   - 支持根據用戶名映射到正確的用戶ID（how->3L, playwright_test->4L）

### Testing Results
- ✅ 訂單詳情API (`/api/orders/{id}`) - 正確返回完整訂單數據
- ✅ 訂單列表API (`/api/orders`) - 正確返回包含關聯數據的訂單列表
- ✅ 支付寶支付API (`/api/payment/alipay/create`) - 成功創建支付請求
- ✅ 支付狀態查詢API (`/api/payment/status/{id}`) - 正確查詢支付狀態

## [2025-07-15 09:51:41] 修復管理員後台查看詳情的 Hibernate 懶加載問題

### Decision
在 IdentityVerificationRepository 中添加 findByIdWithUser 方法，使用 JOIN FETCH 預加載 User 實體，解決管理員後台查看身份認證詳情時的懶加載錯誤。

### Rationale
- 原問題：管理員點擊"查看詳情"時出現 "Could not initialize proxy [com.example.entity.User#3] - no Session" 錯誤
- 根本原因：IdentityVerification 實體中的 User 屬性是懶加載的，當 Hibernate Session 關閉後無法訪問
- 解決方案：使用 JOIN FETCH 在查詢時預加載關聯實體，避免懶加載問題
- 優點：解決懶加載問題，提高查詢效率，避免 N+1 查詢問題

### Implementation Details
- 在 IdentityVerificationRepository 中添加了 findByIdWithUser 方法：
  ```java
  @Query("SELECT iv FROM IdentityVerification iv JOIN FETCH iv.user WHERE iv.id = :id")
  Optional<IdentityVerification> findByIdWithUser(@Param("id") Long id);
  ```
- 修改 IdentityVerificationService 中的 getVerificationById 方法使用新的查詢方法
- 添加了必要的 @Param 導入

## [2025-07-10 20:58:31] 修復 Hibernate 懶加載問題

### Decision
使用 JOIN FETCH 查詢來預加載 User 實體，避免在 Controller 中訪問懶加載屬性時出現 "could not initialize proxy" 錯誤。

### Rationale
- 原問題：AdminIdentityController 中的 convertToMapWithUserInfo 方法嘗試訪問 IdentityVerification.user 的屬性，但 User 是懶加載的，當 Hibernate Session 關閉後無法訪問
- 解決方案：在 Repository 層使用 @Query 註解和 JOIN FETCH 來預加載關聯實體
- 優點：解決懶加載問題，提高查詢效率，避免 N+1 查詢問題

### Implementation Details
- 在 IdentityVerificationRepository 中添加了兩個新方法：
  - findByStatusWithUserOrderBySubmittedAtAsc：預加載 User 的待審核查詢
  - findAllWithUserOrderBySubmittedAtDesc：預加載 User 的全部查詢
- 修改 IdentityVerificationService 中的 getPendingVerifications 和 getAllVerifications 方法使用新的查詢方法
- 移除了 AdminIdentityController 中的調試日誌，保持代碼整潔