package com.example.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

/**
 * 線程池配置類
 * 基於學習的延遲雙刪策略需要的線程池配置
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Configuration
public class ThreadPoolConfig {
    
    /**
     * 創建定時任務線程池
     * 用於延遲雙刪等異步任務
     */
    @Bean
    public ScheduledExecutorService threadPool() {
        return Executors.newScheduledThreadPool(4);
    }
}
