package com.example.exception;

/**
 * 重复收藏异常
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public class DuplicateFavoriteException extends FavoriteException {
    
    public DuplicateFavoriteException(String message) {
        super(message, "DUPLICATE_FAVORITE");
    }
    
    public DuplicateFavoriteException(Long userId, Long itemId) {
        super(String.format("用户 %d 已经收藏了内容 %d", userId, itemId), "DUPLICATE_FAVORITE");
    }
}
