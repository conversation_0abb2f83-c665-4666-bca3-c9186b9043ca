# 项目结构

## 根目录布局```
├── 后端/后端/          # SpringBoot 后端应用程序
├── 前端/               # Vue3 前端应用程序
├── 问题/               # 开发规划和问题跟踪
├── 记忆库/              # 项目背景和决策日志
├── 临时文件/           # 临时文件
├── 测试*.js            # API 测试脚本
└── 项目说明.md         # 主要项目文档```

## 后端结构（“backend/backend/”）```
src/main/java/
├── config/          # 配置类（安全、跨域资源共享等）
├── controller/      # REST API 控制器
├── dto/            # 数据传输对象
├── entity/         # JPA 实体（用户、电子邮件验证等）
├── repository/     # 数据访问层接口
├── service/        # 业务逻辑层
└── util/           # 工具类（JWT、文件处理）
src/main/resources/
└── application.yml  # 应用程序配置文件```

## 前端结构（“前端/”）```
src/
├── api/            # HTTP 客户端及 API 服务函数
├── router/         # Vue Router 配置
├── stores/         # Pinia 数据管理存储
├── views/          # 页面组件
└── components/     # 可复用的 Vue 组件
public/             # 静态资源文件夹
├── index.html      # 主 HTML 模板文件
└── package.json    # 依赖项和脚本文件```

## 主要建筑模式

### 后端模式
- **分层架构**：控制器 → 服务 → 数据库存储 → 实体
- **数据传输对象模式**：为 API 通信单独设计数据传输对象
- **存储库模式**：使用 JPA 存储库进行数据访问
- **服务层**：将业务逻辑与控制器分离

### 前端模式
- **组合式 API**：Vue 3 中的 `<script setup>` 语法
- **存储模式**：Pinia 用于集中式状态管理
- **API 服务层**：集中式的 HTTP 客户端配置
- **基于组件的**：可复用的 Vue 组件

## 文件命名规范
- **后端**：使用 PascalCase 格式命名类（例如：`UserController.java`）
- **前端**：采用 kebab-case 格式命名文件（例如：`user-profile.vue`）
- **API 端点**：遵循 REST 规范（例如：`/api/user/profile`）

## 发展流程
1. 在“issues/”目录中记录的问题，并配有中文说明文档
2. 内存库保存项目背景信息及决策内容
3. 后端优先的开发方式及 API 文档说明
4. 前端与 TypeScript 类型安全的集成