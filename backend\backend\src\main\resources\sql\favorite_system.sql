-- 收藏系统数据库表结构
-- 创建时间: 2025-01-15

-- 创建可收藏内容表
CREATE TABLE favorite_items (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL COMMENT '内容标题',
    description TEXT COMMENT '内容描述',
    item_type ENUM('ARTICLE', 'VIDEO', 'IMAGE', 'LINK', 'OTHER') NOT NULL COMMENT '内容类型',
    content_url VARCHAR(500) COMMENT '内容链接',
    thumbnail_url VARCHAR(500) COMMENT '缩略图链接',
    favorite_count INT DEFAULT 0 COMMENT '收藏数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_favorite_count (favorite_count),
    INDEX idx_item_type (item_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='可收藏内容表';

-- 创建收藏关系表
CREATE TABLE favorites (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    item_id BIGINT NOT NULL COMMENT '内容ID',
    item_type ENUM('ARTICLE', 'VIDEO', 'IMAGE', 'LINK', 'OTHER') NOT NULL COMMENT '内容类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    
    UNIQUE KEY unique_user_item (user_id, item_id) COMMENT '用户和内容的唯一约束',
    INDEX idx_user_id (user_id),
    INDEX idx_item_id (item_id),
    INDEX idx_created_at (created_at),
    INDEX idx_item_type (item_type),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES favorite_items(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收藏关系表';

-- 插入一些测试数据
INSERT INTO favorite_items (title, description, item_type, content_url, thumbnail_url) VALUES
('Spring Boot 入门教程', 'Spring Boot 框架的详细入门教程，适合初学者学习', 'ARTICLE', 'https://example.com/spring-boot-tutorial', 'https://example.com/thumbnails/spring-boot.jpg'),
('Vue3 实战视频', 'Vue3 框架实战开发视频教程，包含完整项目案例', 'VIDEO', 'https://example.com/vue3-video', 'https://example.com/thumbnails/vue3.jpg'),
('Redis 架构图', 'Redis 内存数据库的架构设计图解', 'IMAGE', 'https://example.com/redis-architecture', 'https://example.com/thumbnails/redis.jpg'),
('GitHub 项目链接', '优秀的开源项目推荐', 'LINK', 'https://github.com/example/awesome-project', 'https://example.com/thumbnails/github.jpg'),
('MySQL 优化指南', '数据库性能优化的最佳实践', 'ARTICLE', 'https://example.com/mysql-optimization', 'https://example.com/thumbnails/mysql.jpg');
