package com.example.exception;

/**
 * 操作频率超限异常
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public class RateLimitExceededException extends FavoriteException {
    
    public RateLimitExceededException(String message) {
        super(message, "RATE_LIMIT_EXCEEDED");
    }
    
    public RateLimitExceededException(Long userId, int maxRequests, int windowSeconds) {
        super(String.format("用户 %d 操作过于频繁，每 %d 秒最多允许 %d 次操作", userId, windowSeconds, maxRequests), 
              "RATE_LIMIT_EXCEEDED");
    }
}
