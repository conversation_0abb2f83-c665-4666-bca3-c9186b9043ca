import { test, expect } from '@playwright/test';

// 測試配置
const BASE_URL = 'http://localhost:5174';
const API_BASE_URL = 'http://localhost:8080';

// 測試用戶信息
const TEST_USER = {
  username: 'how',
  password: 'howhowhowtogo'
};

// 測試收貨信息
const TEST_ADDRESS = {
  receiverName: '張三',
  receiverPhone: '13800138000',
  receiverAddress: '北京市朝陽區建國路88號'
};

test.describe('支付功能完整流程測試', () => {
  
  test.beforeEach(async ({ page }) => {
    // 每個測試前都導航到首頁
    await page.goto(BASE_URL);
  });

  test('用戶登錄功能', async ({ page }) => {
    console.log('開始測試用戶登錄...');
    
    // 點擊登錄按鈕
    await page.click('text=登錄');
    
    // 等待登錄頁面加載
    await page.waitForSelector('input[placeholder*="用戶名"]', { timeout: 10000 });
    
    // 填寫登錄信息
    await page.fill('input[placeholder*="用戶名"]', TEST_USER.username);
    await page.fill('input[placeholder*="密碼"]', TEST_USER.password);
    
    // 提交登錄
    await page.click('button:has-text("登錄")');
    
    // 等待登錄成功
    await page.waitForTimeout(2000);
    
    // 驗證登錄成功 - 檢查是否有用戶信息顯示
    const userInfo = page.locator('text=how');
    await expect(userInfo).toBeVisible({ timeout: 10000 });
    
    console.log('✅ 用戶登錄測試通過');
  });

  test('商品瀏覽和添加到購物車', async ({ page }) => {
    console.log('開始測試商品瀏覽和添加到購物車...');
    
    // 先登錄
    await loginUser(page);
    
    // 導航到商品頁面
    await page.goto(`${BASE_URL}/products`);
    
    // 等待商品列表加載
    await page.waitForSelector('.product-grid', { timeout: 10000 });
    
    // 點擊第一個商品
    const firstProduct = page.locator('.product-card').first();
    await expect(firstProduct).toBeVisible();
    await firstProduct.click();
    
    // 等待商品詳情頁加載
    await page.waitForSelector('.product-detail', { timeout: 10000 });
    
    // 驗證商品詳情頁面元素
    await expect(page.locator('.product-title')).toBeVisible();
    await expect(page.locator('.current-price')).toBeVisible();
    
    // 點擊加入購物車
    const addToCartBtn = page.locator('button:has-text("加入購物車")');
    await expect(addToCartBtn).toBeVisible();
    await addToCartBtn.click();
    
    // 等待成功提示
    await page.waitForTimeout(1000);
    
    console.log('✅ 商品添加到購物車測試通過');
  });

  test('購物車頁面功能測試', async ({ page }) => {
    console.log('開始測試購物車頁面功能...');
    
    // 先登錄並添加商品到購物車
    await loginUser(page);
    await addProductToCart(page);
    
    // 導航到購物車頁面
    await page.goto(`${BASE_URL}/cart`);
    
    // 等待購物車加載
    await page.waitForSelector('.cart-container', { timeout: 10000 });
    
    // 驗證購物車中有商品
    const cartItems = page.locator('.cart-item');
    await expect(cartItems).toHaveCount(1);
    
    // 驗證商品信息顯示
    await expect(page.locator('.product-name')).toBeVisible();
    await expect(page.locator('.product-price')).toBeVisible();
    
    // 測試數量調整
    const quantityInput = page.locator('.quantity-input input');
    if (await quantityInput.isVisible()) {
      await quantityInput.fill('2');
      await page.keyboard.press('Enter');
      await page.waitForTimeout(1000);
    }
    
    // 驗證結算按鈕可見
    const checkoutBtn = page.locator('button:has-text("去結算")');
    await expect(checkoutBtn).toBeVisible();
    
    console.log('✅ 購物車頁面功能測試通過');
  });

  test('訂單創建流程測試', async ({ page }) => {
    console.log('開始測試訂單創建流程...');
    
    // 先登錄並添加商品到購物車
    await loginUser(page);
    await addProductToCart(page);
    
    // 導航到購物車頁面
    await page.goto(`${BASE_URL}/cart`);
    await page.waitForSelector('.cart-container', { timeout: 10000 });
    
    // 點擊去結算
    const checkoutBtn = page.locator('button:has-text("去結算")');
    await checkoutBtn.click();
    
    // 等待結算頁面加載
    await page.waitForSelector('.checkout-container', { timeout: 10000 });
    
    // 填寫收貨信息
    await page.fill('input[placeholder*="收貨人姓名"]', TEST_ADDRESS.receiverName);
    await page.fill('input[placeholder*="聯繫電話"]', TEST_ADDRESS.receiverPhone);
    await page.fill('textarea[placeholder*="收貨地址"]', TEST_ADDRESS.receiverAddress);
    
    // 提交訂單
    const submitBtn = page.locator('button:has-text("提交訂單")');
    await submitBtn.click();
    
    // 等待跳轉到支付頁面
    await page.waitForURL(/\/payment\/\d+/, { timeout: 15000 });
    
    console.log('✅ 訂單創建流程測試通過');
  });

  test('支付頁面功能測試', async ({ page }) => {
    console.log('開始測試支付頁面功能...');
    
    // 先創建訂單
    await createTestOrder(page);
    
    // 驗證支付頁面加載
    await page.waitForSelector('.payment-view', { timeout: 10000 });
    
    // 驗證訂單信息顯示
    await expect(page.locator('.order-info')).toBeVisible();
    await expect(page.locator('text=訂單號')).toBeVisible();
    await expect(page.locator('text=總金額')).toBeVisible();
    
    // 驗證支付狀態
    await expect(page.locator('.payment-status')).toBeVisible();
    await expect(page.locator('text=待付款')).toBeVisible();
    
    // 驗證支付按鈕
    const payBtn = page.locator('button:has-text("立即支付")');
    await expect(payBtn).toBeVisible();
    await expect(payBtn).toBeEnabled();
    
    // 測試刷新狀態功能
    const refreshBtn = page.locator('button:has-text("刷新狀態")');
    await refreshBtn.click();
    await page.waitForTimeout(1000);
    
    console.log('✅ 支付頁面功能測試通過');
  });

  test('支付按鈕點擊測試', async ({ page }) => {
    console.log('開始測試支付按鈕點擊功能...');
    
    // 先創建訂單
    await createTestOrder(page);
    
    // 等待支付頁面完全加載
    await page.waitForSelector('.payment-view', { timeout: 10000 });
    
    // 等待支付按鈕可用
    const payBtn = page.locator('button:has-text("立即支付")');
    await expect(payBtn).toBeVisible();
    await expect(payBtn).toBeEnabled();
    
    // 監聽新窗口打開事件（支付寶支付會打開新窗口）
    const [newPage] = await Promise.all([
      page.context().waitForEvent('page'),
      payBtn.click()
    ]);
    
    // 驗證新窗口打開（支付寶支付頁面）
    await newPage.waitForLoadState();
    const newPageUrl = newPage.url();
    console.log('支付頁面URL:', newPageUrl);
    
    // 驗證是否跳轉到支付寶域名
    expect(newPageUrl).toContain('alipay');
    
    // 關閉支付窗口
    await newPage.close();
    
    console.log('✅ 支付按鈕點擊測試通過');
  });

  test('訂單列表頁面測試', async ({ page }) => {
    console.log('開始測試訂單列表頁面...');
    
    // 先創建訂單
    await createTestOrder(page);
    
    // 導航到訂單列表頁面
    await page.goto(`${BASE_URL}/orders`);
    
    // 等待訂單列表加載
    await page.waitForSelector('.orders-container', { timeout: 10000 });
    
    // 驗證訂單存在
    const orderCards = page.locator('.order-card');
    await expect(orderCards.first()).toBeVisible();
    
    // 驗證訂單信息顯示
    await expect(page.locator('.order-number')).toBeVisible();
    await expect(page.locator('.order-status')).toBeVisible();
    await expect(page.locator('.order-amount')).toBeVisible();
    
    // 測試立即支付按鈕
    const payBtnInList = page.locator('button:has-text("立即支付")').first();
    if (await payBtnInList.isVisible()) {
      await payBtnInList.click();
      // 驗證跳轉到支付頁面
      await page.waitForURL(/\/payment\/\d+/, { timeout: 10000 });
    }
    
    console.log('✅ 訂單列表頁面測試通過');
  });

});

// 輔助函數
async function loginUser(page: any) {
  console.log('執行登錄操作...');
  await page.goto(`${BASE_URL}/login`);
  await page.waitForSelector('input[placeholder*="用戶名"]', { timeout: 10000 });
  await page.fill('input[placeholder*="用戶名"]', TEST_USER.username);
  await page.fill('input[placeholder*="密碼"]', TEST_USER.password);
  await page.click('button:has-text("登錄")');
  await page.waitForTimeout(2000);
}

async function addProductToCart(page: any) {
  console.log('添加商品到購物車...');
  await page.goto(`${BASE_URL}/products`);
  await page.waitForSelector('.product-grid', { timeout: 10000 });
  await page.locator('.product-card').first().click();
  await page.waitForSelector('.product-detail', { timeout: 10000 });
  await page.click('button:has-text("加入購物車")');
  await page.waitForTimeout(1000);
}

async function createTestOrder(page: any) {
  console.log('創建測試訂單...');
  await loginUser(page);
  await addProductToCart(page);
  await page.goto(`${BASE_URL}/cart`);
  await page.waitForSelector('.cart-container', { timeout: 10000 });
  await page.click('button:has-text("去結算")');
  await page.waitForSelector('.checkout-container', { timeout: 10000 });
  await page.fill('input[placeholder*="收貨人姓名"]', TEST_ADDRESS.receiverName);
  await page.fill('input[placeholder*="聯繫電話"]', TEST_ADDRESS.receiverPhone);
  await page.fill('textarea[placeholder*="收貨地址"]', TEST_ADDRESS.receiverAddress);
  await page.click('button:has-text("提交訂單")');
  await page.waitForURL(/\/payment\/\d+/, { timeout: 15000 });
}
