<template>
  <div class="my-favorites">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon><Star /></el-icon>
            我的收藏
          </h1>
          <p class="page-subtitle">管理您收藏的精彩内容</p>
        </div>
        
        <div class="header-actions">
          <el-button type="primary" @click="$router.push('/favorite-ranking')">
            <el-icon><TrendCharts /></el-icon>
            收藏排行榜
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filters-section">
      <div class="filters-content">
        <div class="filter-group">
          <label class="filter-label">类型筛选：</label>
          <el-select 
            v-model="selectedType" 
            @change="handleTypeChange" 
            placeholder="全部类型"
            style="width: 140px"
          >
            <el-option label="全部类型" value="" />
            <el-option label="文章" value="ARTICLE" />
            <el-option label="视频" value="VIDEO" />
            <el-option label="图片" value="IMAGE" />
            <el-option label="链接" value="LINK" />
          </el-select>
        </div>
        
        <div class="search-group">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索收藏内容..."
            @keyup.enter="handleSearch"
            @clear="handleClearSearch"
            clearable
            style="width: 300px"
          >
            <template #append>
              <el-button @click="handleSearch" :loading="favoriteStore.loading">
                <el-icon><Search /></el-icon>
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <div class="stats-cards">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><Collection /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ favoriteStore.myFavoritesPage.totalElements }}</div>
            <div class="stat-label">总收藏数</div>
          </div>
        </div>
        
        <div class="stat-card" v-if="isSearchMode">
          <div class="stat-icon">
            <el-icon><Search /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ searchResults.length }}</div>
            <div class="stat-label">搜索结果</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="favoriteStore.loading && favorites.length === 0" class="loading-section">
      <el-skeleton :rows="6" animated />
    </div>

    <!-- 空状态 -->
    <div v-else-if="favorites.length === 0" class="empty-section">
      <el-empty 
        :image-size="120"
        description="还没有收藏任何内容"
      >
        <template #image>
          <el-icon size="120" color="#c0c4cc"><Star /></el-icon>
        </template>
        <el-button type="primary" @click="$router.push('/favorite-ranking')">
          去发现精彩内容
        </el-button>
      </el-empty>
    </div>

    <!-- 收藏列表 -->
    <div v-else class="favorites-section">
      <div class="favorites-grid">
        <div
          v-for="favorite in favorites"
          :key="favorite.id"
          class="favorite-card"
        >
          <!-- 卡片头部 -->
          <div class="card-header">
            <el-tag 
              :type="getTypeTagType(favorite.itemType)" 
              size="small"
              effect="light"
            >
              {{ getTypeLabel(favorite.itemType) }}
            </el-tag>
            <span class="favorite-date">
              {{ formatDate(favorite.createdAt) }}
            </span>
          </div>

          <!-- 卡片内容 -->
          <div class="card-content">
            <div v-if="favorite.itemImageUrl" class="thumbnail">
              <el-image 
                :src="favorite.itemImageUrl" 
                :alt="favorite.itemTitle"
                fit="cover"
                lazy
              >
                <template #error>
                  <div class="image-error">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </div>
            
            <div class="content-info">
              <h3 class="title">
                <a
                  v-if="favorite.itemUrl"
                  :href="favorite.itemUrl"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="title-link"
                >
                  {{ favorite.itemTitle }}
                  <el-icon size="12"><Link /></el-icon>
                </a>
                <span v-else>{{ favorite.itemTitle }}</span>
              </h3>
              
              <p v-if="favorite.itemDescription" class="description">
                {{ favorite.itemDescription }}
              </p>
              
              <div v-if="favorite.itemAuthor" class="author">
                <el-icon><User /></el-icon>
                {{ favorite.itemAuthor }}
              </div>
              
              <div v-if="favorite.tags && favorite.tags.length" class="tags">
                <el-tag 
                  v-for="tag in favorite.tags.slice(0, 3)" 
                  :key="tag"
                  size="small"
                  type="info"
                  effect="plain"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </div>

          <!-- 卡片操作 -->
          <div class="card-actions">
            <FavoriteButton
              :item-id="favorite.itemId"
              :show-count="false"
              :show-text="true"
              size="small"
              @favorite-changed="handleFavoriteChanged"
            />
            
            <el-button
              v-if="favorite.itemUrl"
              size="small"
              type="primary"
              link
              @click="openLink(favorite.itemUrl)"
            >
              <el-icon><View /></el-icon>
              查看
            </el-button>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div v-if="favoriteStore.hasMoreFavorites && !isSearchMode" class="load-more-section">
        <el-button
          @click="loadMore"
          :loading="favoriteStore.loading"
          size="large"
          style="width: 200px"
        >
          {{ favoriteStore.loading ? '加载中...' : '加载更多' }}
        </el-button>
      </div>
    </div>

    <!-- 错误提示 -->
    <el-alert
      v-if="favoriteStore.error"
      :title="favoriteStore.error"
      type="error"
      :closable="true"
      @close="favoriteStore.clearError"
      style="margin-top: 20px"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useFavoriteStore } from '@/stores/favorite'
import FavoriteButton from '@/components/FavoriteButton.vue'

const favoriteStore = useFavoriteStore()

const selectedType = ref('')
const searchKeyword = ref('')
const isSearchMode = ref(false)
const searchResults = ref<any[]>([])

// 計算屬性
const favorites = computed(() => {
  return isSearchMode.value ? searchResults.value : favoriteStore.myFavorites
})

// 方法
const handleTypeChange = async () => {
  if (isSearchMode.value) {
    isSearchMode.value = false
    searchResults.value = []
    searchKeyword.value = ''
  }
  
  await favoriteStore.loadMyFavorites(0, 20, selectedType.value || undefined)
}

const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    isSearchMode.value = false
    searchResults.value = []
    return
  }

  isSearchMode.value = true
  const result = await favoriteStore.searchFavoriteItems(searchKeyword.value.trim())
  if (result) {
    searchResults.value = result.content.map(item => ({
      id: item.id,
      itemId: item.id,
      itemTitle: item.title,
      itemDescription: item.description,
      itemType: item.itemType,
      contentUrl: item.contentUrl,
      thumbnailUrl: item.thumbnailUrl,
      createdAt: item.createdAt
    }))
  }
}

const loadMore = async () => {
  if (isSearchMode.value) return
  
  const nextPage = favoriteStore.myFavoritesPage.current + 1
  await favoriteStore.loadMyFavorites(
    nextPage,
    20,
    selectedType.value || undefined,
    true // append
  )
}

const handleClearSearch = () => {
  isSearchMode.value = false
  searchResults.value = []
  searchKeyword.value = ''
}

const handleFavoriteChanged = (itemId: number, isFavorited: boolean) => {
  // 如果取消收藏，从列表中移除该项目
  if (!isFavorited && !isSearchMode.value) {
    // 重新加载收藏列表以确保数据同步
    favoriteStore.loadMyFavorites(0, 20, selectedType.value || undefined)
  }
}

const openLink = (url: string) => {
  window.open(url, '_blank', 'noopener,noreferrer')
}

const getTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    ARTICLE: '文章',
    VIDEO: '视频',
    IMAGE: '图片',
    LINK: '链接',
    OTHER: '其他'
  }
  return labels[type] || type
}

const getTypeTagType = (type: string): string => {
  const typeMap: Record<string, string> = {
    ARTICLE: 'success',
    VIDEO: 'danger',
    IMAGE: 'warning',
    LINK: 'primary',
    OTHER: 'info'
  }
  return typeMap[type] || 'info'
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分鐘前`
  } else if (hours < 24) {
    return `${hours}小時前`
  } else if (days < 30) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-TW')
  }
}

// 生命週期
onMounted(async () => {
  await favoriteStore.loadMyFavorites()
})
</script>

<style scoped>
.my-favorites {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
}

/* 页面头部 */
.page-header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.title-section {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 32px;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 8px 0;
}

.page-title .el-icon {
  color: #667eea;
}

.page-subtitle {
  color: #718096;
  margin: 0;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 筛选和搜索区域 */
.filters-section {
  max-width: 1200px;
  margin: 0 auto 24px auto;
  padding: 0 24px;
}

.filters-content {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-label {
  font-weight: 500;
  color: #4a5568;
  white-space: nowrap;
}

.search-group {
  flex: 1;
  max-width: 400px;
}

/* 统计信息 */
.stats-section {
  max-width: 1200px;
  margin: 0 auto 24px auto;
  padding: 0 24px;
}

.stats-cards {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 160px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #718096;
}

/* 加载和空状态 */
.loading-section,
.empty-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.loading-section {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.empty-section {
  background: white;
  border-radius: 16px;
  padding: 60px 40px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  text-align: center;
}

/* 收藏列表 */
.favorites-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.favorites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.favorite-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.favorite-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.favorite-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.favorite-date {
  font-size: 12px;
  color: #a0aec0;
}

.card-content {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.thumbnail {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 12px;
  overflow: hidden;
  background: #f7fafc;
}

.thumbnail .el-image {
  width: 100%;
  height: 100%;
}

.image-error {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #cbd5e0;
  font-size: 24px;
}

.content-info {
  flex: 1;
  min-width: 0;
}

.title {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
  color: #2d3748;
}

.title-link {
  color: #2d3748;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: color 0.2s ease;
}

.title-link:hover {
  color: #667eea;
}

.description {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #718096;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.author {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #a0aec0;
  margin-bottom: 12px;
}

.tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

/* 加载更多 */
.load-more-section {
  text-align: center;
  margin: 32px 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    padding: 24px 20px;
  }
  
  .filters-section,
  .stats-section,
  .favorites-section {
    padding: 0 20px;
  }
  
  .favorites-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (max-width: 768px) {
  .page-title {
    font-size: 28px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  
  .filters-content {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .search-group {
    max-width: none;
  }
  
  .stats-cards {
    justify-content: center;
  }
  
  .favorites-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .card-content {
    flex-direction: column;
    gap: 12px;
  }
  
  .thumbnail {
    width: 100%;
    height: 120px;
  }
}

@media (max-width: 480px) {
  .header-content,
  .filters-section,
  .stats-section,
  .favorites-section {
    padding: 0 16px;
  }
  
  .filters-content,
  .favorite-card {
    padding: 16px;
  }
  
  .page-title {
    font-size: 24px;
  }
}
</style>
