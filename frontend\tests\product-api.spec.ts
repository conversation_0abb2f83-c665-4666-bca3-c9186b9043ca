import { test, expect } from '@playwright/test';

test.describe('商品管理系統 API 測試', () => {
  const baseURL = 'http://localhost:8080/api';
  
  test('商品分類樹 API 測試', async ({ request }) => {
    // 測試獲取分類樹
    const response = await request.get(`${baseURL}/product/categories/tree`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(Array.isArray(data.data)).toBe(true);
    
    // 檢查分類樹結構
    if (data.data.length > 0) {
      const firstCategory = data.data[0];
      expect(firstCategory).toHaveProperty('id');
      expect(firstCategory).toHaveProperty('name');
      expect(firstCategory).toHaveProperty('parentId');
      expect(firstCategory).toHaveProperty('level');
      expect(firstCategory).toHaveProperty('status');
    }
  });

  test('商品列表 API 測試', async ({ request }) => {
    // 測試分頁查詢商品
    const response = await request.get(`${baseURL}/products?page=0&size=10`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toHaveProperty('content');
    expect(data.data).toHaveProperty('totalElements');
    expect(data.data).toHaveProperty('totalPages');
    expect(Array.isArray(data.data.content)).toBe(true);
    
    // 檢查商品數據結構
    if (data.data.content.length > 0) {
      const firstProduct = data.data.content[0];
      expect(firstProduct).toHaveProperty('id');
      expect(firstProduct).toHaveProperty('name');
      expect(firstProduct).toHaveProperty('price');
      expect(firstProduct).toHaveProperty('stock');
      expect(firstProduct).toHaveProperty('categoryId');
      expect(firstProduct).toHaveProperty('status');
    }
  });

  test('商品詳情 API 測試', async ({ request }) => {
    // 先獲取商品列表以獲得有效的商品ID
    const listResponse = await request.get(`${baseURL}/products?page=0&size=1`);
    const listData = await listResponse.json();
    
    if (listData.success && listData.data.content.length > 0) {
      const productId = listData.data.content[0].id;
      
      // 測試獲取商品詳情
      const detailResponse = await request.get(`${baseURL}/products/${productId}`);
      expect(detailResponse.status()).toBe(200);
      
      const detailData = await detailResponse.json();
      expect(detailData.success).toBe(true);
      expect(detailData.data).toHaveProperty('id', productId);
      expect(detailData.data).toHaveProperty('name');
      expect(detailData.data).toHaveProperty('description');
      expect(detailData.data).toHaveProperty('price');
      expect(detailData.data).toHaveProperty('stock');
    }
  });

  test('商品搜索 API 測試', async ({ request }) => {
    // 測試關鍵詞搜索
    const response = await request.get(`${baseURL}/products/search?keyword=iPhone&page=0&size=10`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toHaveProperty('content');
    expect(Array.isArray(data.data.content)).toBe(true);
    
    // 檢查搜索結果是否包含關鍵詞
    if (data.data.content.length > 0) {
      const firstResult = data.data.content[0];
      const nameContainsKeyword = firstResult.name.toLowerCase().includes('iphone');
      const descriptionContainsKeyword = firstResult.description && 
        firstResult.description.toLowerCase().includes('iphone');
      const brandContainsKeyword = firstResult.brand && 
        firstResult.brand.toLowerCase().includes('iphone');
      
      expect(nameContainsKeyword || descriptionContainsKeyword || brandContainsKeyword).toBe(true);
    }
  });

  test('按分類查詢商品 API 測試', async ({ request }) => {
    // 先獲取分類列表
    const categoryResponse = await request.get(`${baseURL}/product/categories/tree`);
    const categoryData = await categoryResponse.json();
    
    if (categoryData.success && categoryData.data.length > 0) {
      // 找到一個葉子分類
      const findLeafCategory = (categories: any[]): any => {
        for (const category of categories) {
          if (category.isLeaf === 1) {
            return category;
          }
          if (category.children && category.children.length > 0) {
            const leaf = findLeafCategory(category.children);
            if (leaf) return leaf;
          }
        }
        return null;
      };
      
      const leafCategory = findLeafCategory(categoryData.data);
      
      if (leafCategory) {
        // 測試按分類查詢商品
        const response = await request.get(`${baseURL}/products/category/${leafCategory.id}?page=0&size=10`);
        expect(response.status()).toBe(200);
        
        const data = await response.json();
        expect(data.success).toBe(true);
        expect(Array.isArray(data.data.content)).toBe(true);
        
        // 檢查返回的商品是否屬於指定分類
        if (data.data.content.length > 0) {
          data.data.content.forEach((product: any) => {
            expect(product.categoryId).toBe(leafCategory.id);
          });
        }
      }
    }
  });

  test('推薦商品 API 測試', async ({ request }) => {
    const response = await request.get(`${baseURL}/products/recommended?page=0&size=10`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(Array.isArray(data.data.content)).toBe(true);
    
    // 檢查返回的商品是否都是推薦商品
    if (data.data.content.length > 0) {
      data.data.content.forEach((product: any) => {
        expect(product.isRecommended).toBe(1);
      });
    }
  });

  test('熱門商品 API 測試', async ({ request }) => {
    const response = await request.get(`${baseURL}/products/hot?page=0&size=10`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(Array.isArray(data.data.content)).toBe(true);
    
    // 檢查返回的商品是否都是熱門商品
    if (data.data.content.length > 0) {
      data.data.content.forEach((product: any) => {
        expect(product.isHot).toBe(1);
      });
    }
  });

  test('商品瀏覽次數 API 測試', async ({ request }) => {
    // 先獲取一個商品ID
    const listResponse = await request.get(`${baseURL}/products?page=0&size=1`);
    const listData = await listResponse.json();
    
    if (listData.success && listData.data.content.length > 0) {
      const productId = listData.data.content[0].id;
      
      // 測試獲取瀏覽次數
      const viewCountResponse = await request.get(`${baseURL}/products/${productId}/view-count`);
      expect(viewCountResponse.status()).toBe(200);
      
      const viewCountData = await viewCountResponse.json();
      expect(viewCountData.success).toBe(true);
      expect(typeof viewCountData.data).toBe('number');
      expect(viewCountData.data).toBeGreaterThanOrEqual(0);
    }
  });

  test('多條件篩選 API 測試', async ({ request }) => {
    const params = new URLSearchParams({
      minPrice: '1000',
      maxPrice: '5000',
      isRecommended: '1',
      page: '0',
      size: '10'
    });
    
    const response = await request.get(`${baseURL}/products/filter?${params}`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(Array.isArray(data.data.content)).toBe(true);
    
    // 檢查篩選結果
    if (data.data.content.length > 0) {
      data.data.content.forEach((product: any) => {
        expect(product.price).toBeGreaterThanOrEqual(1000);
        expect(product.price).toBeLessThanOrEqual(5000);
        expect(product.isRecommended).toBe(1);
      });
    }
  });

  test('價格排序 API 測試', async ({ request }) => {
    // 測試價格升序
    const ascResponse = await request.get(`${baseURL}/products/price-sorted?ascending=true&page=0&size=10`);
    expect(ascResponse.status()).toBe(200);
    
    const ascData = await ascResponse.json();
    expect(ascData.success).toBe(true);
    
    // 檢查價格是否按升序排列
    if (ascData.data.content.length > 1) {
      for (let i = 1; i < ascData.data.content.length; i++) {
        expect(ascData.data.content[i].price).toBeGreaterThanOrEqual(
          ascData.data.content[i - 1].price
        );
      }
    }
    
    // 測試價格降序
    const descResponse = await request.get(`${baseURL}/products/price-sorted?ascending=false&page=0&size=10`);
    expect(descResponse.status()).toBe(200);
    
    const descData = await descResponse.json();
    expect(descData.success).toBe(true);
    
    // 檢查價格是否按降序排列
    if (descData.data.content.length > 1) {
      for (let i = 1; i < descData.data.content.length; i++) {
        expect(descData.data.content[i].price).toBeLessThanOrEqual(
          descData.data.content[i - 1].price
        );
      }
    }
  });

  test('相關商品 API 測試', async ({ request }) => {
    // 先獲取一個商品ID
    const listResponse = await request.get(`${baseURL}/products?page=0&size=1`);
    const listData = await listResponse.json();
    
    if (listData.success && listData.data.content.length > 0) {
      const productId = listData.data.content[0].id;
      const categoryId = listData.data.content[0].categoryId;
      
      // 測試獲取相關商品
      const relatedResponse = await request.get(`${baseURL}/products/${productId}/related?limit=5`);
      expect(relatedResponse.status()).toBe(200);
      
      const relatedData = await relatedResponse.json();
      expect(relatedData.success).toBe(true);
      expect(Array.isArray(relatedData.data)).toBe(true);
      
      // 檢查相關商品是否屬於同一分類且不包含當前商品
      if (relatedData.data.length > 0) {
        relatedData.data.forEach((product: any) => {
          expect(product.categoryId).toBe(categoryId);
          expect(product.id).not.toBe(productId);
        });
      }
    }
  });

  test('API 錯誤處理測試', async ({ request }) => {
    // 測試訪問不存在的商品
    const response = await request.get(`${baseURL}/products/99999`);
    expect(response.status()).toBe(200); // 根據實際API設計調整
    
    const data = await response.json();
    expect(data.success).toBe(false);
    expect(data.message).toBeDefined();
  });

  test('API 性能測試', async ({ request }) => {
    const startTime = Date.now();
    
    // 並發請求測試
    const promises = [
      request.get(`${baseURL}/product/categories/tree`),
      request.get(`${baseURL}/products?page=0&size=20`),
      request.get(`${baseURL}/products/recommended?page=0&size=10`),
      request.get(`${baseURL}/products/hot?page=0&size=10`)
    ];
    
    const responses = await Promise.all(promises);
    const endTime = Date.now();
    
    // 檢查所有請求都成功
    responses.forEach(response => {
      expect(response.status()).toBe(200);
    });
    
    // 檢查響應時間
    const totalTime = endTime - startTime;
    expect(totalTime).toBeLessThan(5000); // 5秒內完成所有請求
    
    console.log(`API 並發請求總時間: ${totalTime}ms`);
  });
});
