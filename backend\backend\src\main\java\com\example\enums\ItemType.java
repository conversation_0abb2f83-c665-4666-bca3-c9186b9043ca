package com.example.enums;

/**
 * 可收藏内容类型枚举
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public enum ItemType {
    /**
     * 文章
     */
    ARTICLE("文章"),
    
    /**
     * 视频
     */
    VIDEO("视频"),
    
    /**
     * 图片
     */
    IMAGE("图片"),
    
    /**
     * 链接
     */
    LINK("链接"),
    
    /**
     * 其他
     */
    OTHER("其他");
    
    private final String description;
    
    ItemType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据描述获取枚举值
     * 
     * @param description 描述
     * @return ItemType枚举值
     */
    public static ItemType fromDescription(String description) {
        for (ItemType type : ItemType.values()) {
            if (type.description.equals(description)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的内容类型: " + description);
    }
}
