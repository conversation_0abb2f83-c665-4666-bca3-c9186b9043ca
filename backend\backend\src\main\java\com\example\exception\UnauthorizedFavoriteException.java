package com.example.exception;

/**
 * 无权限操作收藏异常
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public class UnauthorizedFavoriteException extends FavoriteException {
    
    public UnauthorizedFavoriteException(String message) {
        super(message, "UNAUTHORIZED_FAVORITE");
    }
    
    public UnauthorizedFavoriteException(Long userId, Long itemId) {
        super(String.format("用户 %d 无权限操作内容 %d 的收藏", userId, itemId), "UNAUTHORIZED_FAVORITE");
    }
}
