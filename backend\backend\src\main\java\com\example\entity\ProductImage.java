package com.example.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 商品圖片實體類
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Entity
@Table(name = "product_images")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductImage {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 商品ID
     */
    @Column(name = "product_id", nullable = false)
    private Long productId;
    
    /**
     * 圖片URL
     */
    @Column(name = "image_url", nullable = false, length = 500)
    private String imageUrl;
    
    /**
     * 圖片排序號，數字越小越靠前
     */
    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;
    
    /**
     * 是否為主圖：1-是，0-否
     */
    @Column(name = "is_main", nullable = false)
    private Integer isMain = 0;
    
    /**
     * 圖片描述
     */
    @Column(name = "description", length = 200)
    private String description;
    
    /**
     * 圖片大小（字節）
     */
    @Column(name = "file_size")
    private Long fileSize;
    
    /**
     * 圖片寬度（像素）
     */
    @Column(name = "width")
    private Integer width;
    
    /**
     * 圖片高度（像素）
     */
    @Column(name = "height")
    private Integer height;
    
    /**
     * 圖片格式（jpg, png, gif等）
     */
    @Column(name = "format", length = 10)
    private String format;
    
    /**
     * 創建時間
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    /**
     * 更新時間
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    /**
     * 上傳者ID
     */
    @Column(name = "uploaded_by")
    private Long uploadedBy;
    
    // 關聯關係 - 商品
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", insertable = false, updatable = false)
    private Product product;
    
    // 關聯關係 - 上傳者
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "uploaded_by", insertable = false, updatable = false)
    private User uploader;
    
    /**
     * 主圖標識
     */
    public static class MainFlag {
        public static final int NOT_MAIN = 0;  // 非主圖
        public static final int IS_MAIN = 1;   // 主圖
    }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 構造函數 - 創建新圖片記錄
     */
    public ProductImage(Long productId, String imageUrl, Integer sortOrder, Integer isMain, Long uploadedBy) {
        this.productId = productId;
        this.imageUrl = imageUrl;
        this.sortOrder = sortOrder;
        this.isMain = isMain;
        this.uploadedBy = uploadedBy;
    }
    
    /**
     * 構造函數 - 創建主圖記錄
     */
    public ProductImage(Long productId, String imageUrl, Long uploadedBy) {
        this.productId = productId;
        this.imageUrl = imageUrl;
        this.sortOrder = 0;
        this.isMain = MainFlag.IS_MAIN;
        this.uploadedBy = uploadedBy;
    }
}
