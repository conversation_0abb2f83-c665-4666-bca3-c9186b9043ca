package com.example.controller;

import com.example.dto.ApiResponse;
import com.example.entity.Payment;
import com.example.service.PaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 支付控制器
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Slf4j
@RestController
@RequestMapping("/api/payment")
@Tag(name = "支付管理", description = "支付相關功能")
public class PaymentController {
    
    @Autowired
    private PaymentService paymentService;
    
    /**
     * 發起支付寶支付
     */
    @PostMapping("/alipay/create")
    @Operation(summary = "發起支付寶支付", description = "為指定訂單創建支付寶支付")
    public ApiResponse<String> createAlipayPayment(
            @Parameter(description = "訂單ID", required = true) @RequestParam Long orderId,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("發起支付寶支付: userId={}, orderId={}", userId, orderId);
        
        return paymentService.createAlipayPayment(userId, orderId);
    }
    
    /**
     * 支付寶異步回調
     */
    @PostMapping("/alipay/callback")
    @Operation(summary = "支付寶異步回調", description = "處理支付寶的異步通知")
    public String handleAlipayCallback(HttpServletRequest request) {
        try {
            log.info("收到支付寶異步回調");
            
            // 獲取所有參數
            Map<String, String> params = new HashMap<>();
            Map<String, String[]> parameterMap = request.getParameterMap();
            
            for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                String key = entry.getKey();
                String[] values = entry.getValue();
                if (values != null && values.length > 0) {
                    params.put(key, values[0]);
                }
            }
            
            // 處理回調
            ApiResponse<String> result = paymentService.handleAlipayCallback(params);
            
            if (result.isSuccess()) {
                return "success";  // 支付寶要求返回success
            } else {
                log.error("處理支付寶回調失敗: {}", result.getMessage());
                return "fail";
            }
            
        } catch (Exception e) {
            log.error("支付寶回調處理異常", e);
            return "fail";
        }
    }
    
    /**
     * 支付寶同步回調（用戶支付完成後跳轉）
     */
    @GetMapping("/alipay/return")
    @Operation(summary = "支付寶同步回調", description = "用戶支付完成後的跳轉處理")
    public ApiResponse<String> handleAlipayReturn(HttpServletRequest request) {
        try {
            log.info("收到支付寶同步回調");
            
            // 獲取所有參數
            Map<String, String> params = new HashMap<>();
            Map<String, String[]> parameterMap = request.getParameterMap();
            
            for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                String key = entry.getKey();
                String[] values = entry.getValue();
                if (values != null && values.length > 0) {
                    params.put(key, values[0]);
                }
            }
            
            String outTradeNo = params.get("out_trade_no");  // 商戶訂單號
            String tradeNo = params.get("trade_no");  // 支付寶交易號
            
            log.info("支付寶同步回調: orderNumber={}, tradeNo={}", outTradeNo, tradeNo);
            
            return ApiResponse.success("支付完成，請等待系統確認");
            
        } catch (Exception e) {
            log.error("支付寶同步回調處理異常", e);
            return ApiResponse.error("處理支付回調失敗");
        }
    }
    
    /**
     * 查詢支付狀態
     */
    @GetMapping("/status/{orderId}")
    @Operation(summary = "查詢支付狀態", description = "查詢指定訂單的支付狀態")
    public ApiResponse<Payment> getPaymentStatus(
            @Parameter(description = "訂單ID", required = true) @PathVariable Long orderId,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("查詢支付狀態: userId={}, orderId={}", userId, orderId);
        
        return paymentService.getPaymentStatus(userId, orderId);
    }
    
    /**
     * 管理員確認支付（測試用）
     */
    @PostMapping("/admin/confirm")
    @Operation(summary = "管理員確認支付", description = "管理員手動確認訂單支付（測試功能）")
    public ApiResponse<String> confirmPayment(
            @Parameter(description = "訂單號", required = true) @RequestParam String orderNumber,
            @Parameter(description = "支付時間") @RequestParam(required = false) String paymentTime) {
        
        log.info("管理員確認支付: orderNumber={}, paymentTime={}", orderNumber, paymentTime);
        
        return paymentService.confirmOrderPayment(orderNumber, paymentTime);
    }
    
    /**
     * 支付成功頁面
     */
    @GetMapping("/success")
    @Operation(summary = "支付成功頁面", description = "支付成功後的展示頁面")
    public ApiResponse<String> paymentSuccess(
            @Parameter(description = "訂單號") @RequestParam(required = false) String orderNumber) {
        
        log.info("訪問支付成功頁面: orderNumber={}", orderNumber);
        
        return ApiResponse.success("支付成功！訂單號：" + orderNumber);
    }
    
    /**
     * 支付失敗頁面
     */
    @GetMapping("/fail")
    @Operation(summary = "支付失敗頁面", description = "支付失敗後的展示頁面")
    public ApiResponse<String> paymentFail(
            @Parameter(description = "訂單號") @RequestParam(required = false) String orderNumber,
            @Parameter(description = "失敗原因") @RequestParam(required = false) String reason) {
        
        log.info("訪問支付失敗頁面: orderNumber={}, reason={}", orderNumber, reason);
        
        return ApiResponse.error("支付失敗！訂單號：" + orderNumber + "，原因：" + reason);
    }
    
    /**
     * 從Authentication中獲取用戶ID
     */
    private Long getUserId(Authentication authentication) {
        if (authentication != null && authentication.getPrincipal() instanceof org.springframework.security.core.userdetails.UserDetails) {
            org.springframework.security.core.userdetails.UserDetails userDetails =
                (org.springframework.security.core.userdetails.UserDetails) authentication.getPrincipal();

            // 從用戶名獲取用戶ID
            String username = userDetails.getUsername();
            try {
                // 這裡需要通過用戶名查詢用戶ID
                // 可以注入UserService或UserRepository來查詢
                // 臨時解決方案：根據用戶名返回對應的ID
                if ("how".equals(username)) {
                    return 3L; // how用戶的ID是3
                } else if ("playwright_test".equals(username)) {
                    return 4L; // playwright_test用戶的ID是4
                }
                return 1L; // 默認返回1
            } catch (Exception e) {
                log.error("獲取用戶ID失敗: {}", e.getMessage());
                return null;
            }
        }
        return null;
    }
}
