package com.example.repository;

import com.example.entity.Favorite;
import com.example.enums.ItemType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 收藏关系数据访问接口
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Repository
public interface FavoriteRepository extends JpaRepository<Favorite, Long> {
    
    /**
     * 查找用户的收藏列表，按收藏时间倒序排列
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 收藏列表
     */
    Page<Favorite> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);
    
    /**
     * 检查用户是否已收藏某个内容
     * 
     * @param userId 用户ID
     * @param itemId 内容ID
     * @return 是否已收藏
     */
    boolean existsByUserIdAndItemId(Long userId, Long itemId);
    
    /**
     * 查找用户对某个内容的收藏记录
     * 
     * @param userId 用户ID
     * @param itemId 内容ID
     * @return 收藏记录
     */
    Optional<Favorite> findByUserIdAndItemId(Long userId, Long itemId);
    
    /**
     * 删除用户对某个内容的收藏
     * 
     * @param userId 用户ID
     * @param itemId 内容ID
     */
    void deleteByUserIdAndItemId(Long userId, Long itemId);
    
    /**
     * 统计某个内容的收藏数量
     * 
     * @param itemId 内容ID
     * @return 收藏数量
     */
    long countByItemId(Long itemId);
    
    /**
     * 统计用户的总收藏数量
     * 
     * @param userId 用户ID
     * @return 收藏数量
     */
    long countByUserId(Long userId);
    
    /**
     * 查找用户收藏的特定类型内容
     * 
     * @param userId 用户ID
     * @param itemType 内容类型
     * @param pageable 分页参数
     * @return 收藏列表
     */
    Page<Favorite> findByUserIdAndItemTypeOrderByCreatedAtDesc(Long userId, ItemType itemType, Pageable pageable);
    
    /**
     * 获取热门收藏统计数据
     * 查询收藏数量最多的内容
     * 
     * @param pageable 分页参数
     * @return 内容ID和收藏数量的列表
     */
    @Query("SELECT f.itemId, COUNT(f) as count FROM Favorite f GROUP BY f.itemId ORDER BY count DESC")
    List<Object[]> findPopularItems(Pageable pageable);
    
    /**
     * 获取特定类型的热门收藏统计数据
     * 
     * @param itemType 内容类型
     * @param pageable 分页参数
     * @return 内容ID和收藏数量的列表
     */
    @Query("SELECT f.itemId, COUNT(f) as count FROM Favorite f WHERE f.itemType = :itemType GROUP BY f.itemId ORDER BY count DESC")
    List<Object[]> findPopularItemsByType(@Param("itemType") ItemType itemType, Pageable pageable);
    
    /**
     * 查找最近收藏的内容
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近收藏的内容列表
     */
    @Query("SELECT f FROM Favorite f WHERE f.userId = :userId ORDER BY f.createdAt DESC")
    List<Favorite> findRecentFavorites(@Param("userId") Long userId, Pageable pageable);
    
    /**
     * 批量删除用户的收藏记录
     * 
     * @param userId 用户ID
     */
    void deleteByUserId(Long userId);
    
    /**
     * 批量删除某个内容的所有收藏记录
     *
     * @param itemId 内容ID
     */
    void deleteByItemId(Long itemId);

    /**
     * 查找某个内容的收藏记录，按收藏时间倒序
     *
     * @param itemId 内容ID
     * @param pageable 分页参数
     * @return 收藏记录列表
     */
    Page<Favorite> findByItemIdOrderByCreatedAtDesc(Long itemId, Pageable pageable);
}
