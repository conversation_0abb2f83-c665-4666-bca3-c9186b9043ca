package com.example.dto;

import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 分页响应数据传输对象
 * 
 * @param <T> 数据类型
 * <AUTHOR>
 * @since 2025-01-15
 */
public class PagedResponse<T> {
    
    private List<T> content;
    private int page;
    private int size;
    private long totalElements;
    private int totalPages;
    private boolean first;
    private boolean last;
    private boolean empty;
    
    // 构造函数
    public PagedResponse() {
    }
    
    /**
     * 从Spring Data的Page对象创建分页响应
     * 
     * @param page Spring Data的Page对象
     */
    public PagedResponse(Page<T> page) {
        this.content = page.getContent();
        this.page = page.getNumber();
        this.size = page.getSize();
        this.totalElements = page.getTotalElements();
        this.totalPages = page.getTotalPages();
        this.first = page.isFirst();
        this.last = page.isLast();
        this.empty = page.isEmpty();
    }
    
    /**
     * 从Spring Data的Page对象创建分页响应的静态方法
     * 
     * @param page Spring Data的Page对象
     * @param <T> 数据类型
     * @return 分页响应对象
     */
    public static <T> PagedResponse<T> of(Page<T> page) {
        return new PagedResponse<>(page);
    }
    
    /**
     * 创建空的分页响应
     * 
     * @param page 页码
     * @param size 页大小
     * @param <T> 数据类型
     * @return 空的分页响应
     */
    public static <T> PagedResponse<T> empty(int page, int size) {
        PagedResponse<T> response = new PagedResponse<>();
        response.content = List.of();
        response.page = page;
        response.size = size;
        response.totalElements = 0;
        response.totalPages = 0;
        response.first = true;
        response.last = true;
        response.empty = true;
        return response;
    }
    
    // Getter和Setter方法
    public List<T> getContent() {
        return content;
    }
    
    public void setContent(List<T> content) {
        this.content = content;
    }
    
    public int getPage() {
        return page;
    }
    
    public void setPage(int page) {
        this.page = page;
    }
    
    public int getSize() {
        return size;
    }
    
    public void setSize(int size) {
        this.size = size;
    }
    
    public long getTotalElements() {
        return totalElements;
    }
    
    public void setTotalElements(long totalElements) {
        this.totalElements = totalElements;
    }
    
    public int getTotalPages() {
        return totalPages;
    }
    
    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }
    
    public boolean isFirst() {
        return first;
    }
    
    public void setFirst(boolean first) {
        this.first = first;
    }
    
    public boolean isLast() {
        return last;
    }
    
    public void setLast(boolean last) {
        this.last = last;
    }
    
    public boolean isEmpty() {
        return empty;
    }
    
    public void setEmpty(boolean empty) {
        this.empty = empty;
    }
    
    @Override
    public String toString() {
        return "PagedResponse{" +
                "page=" + page +
                ", size=" + size +
                ", totalElements=" + totalElements +
                ", totalPages=" + totalPages +
                ", first=" + first +
                ", last=" + last +
                ", empty=" + empty +
                ", contentSize=" + (content != null ? content.size() : 0) +
                '}';
    }
}
