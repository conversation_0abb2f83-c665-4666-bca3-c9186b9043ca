<template>
  <div class="identity-container">
    <!-- 頂部導航 -->
    <el-header class="header">
      <div class="header-content">
        <el-button @click="$router.go(-1)" link style="color: white;">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1>身份認證</h1>
        <div></div>
      </div>
    </el-header>

    <el-main class="main-content">
      <!-- 認證狀態提示 -->
      <el-alert
        v-if="currentStatus !== 'NOT_SUBMITTED'"
        :title="getStatusTitle()"
        :type="getStatusType()"
        :description="getStatusDescription()"
        show-icon
        :closable="false"
        style="margin-bottom: 20px;"
      />

      <el-row :gutter="20">
        <!-- 身份認證表單 -->
        <el-col :span="16">
          <el-card>
            <template #header>
              <h3>{{ currentStatus === 'NOT_SUBMITTED' ? '提交身份認證' : '身份認證信息' }}</h3>
            </template>
            
            <el-form
              ref="identityFormRef"
              :model="identityForm"
              :rules="identityRules"
              label-width="120px"
              :disabled="!canSubmit"
            >
              <el-form-item label="真實姓名" prop="realName">
                <el-input 
                  v-model="identityForm.realName" 
                  placeholder="請輸入真實姓名"
                />
              </el-form-item>
              
              <el-form-item label="身份證號碼" prop="idCardNumber">
                <el-input 
                  v-model="identityForm.idCardNumber" 
                  placeholder="請輸入身份證號碼"
                  maxlength="18"
                />
              </el-form-item>
              
              <el-form-item label="身份證正面" prop="frontImage">
                <el-upload
                  ref="frontUploadRef"
                  :auto-upload="false"
                  :show-file-list="false"
                  :on-change="handleFrontImageChange"
                  accept="image/*"
                  :disabled="!canSubmit"
                >
                  <div class="upload-area" v-if="!frontImagePreview">
                    <el-icon class="upload-icon"><Plus /></el-icon>
                    <div class="upload-text">點擊上傳身份證正面</div>
                  </div>
                  <div class="image-preview" v-else>
                    <img :src="frontImagePreview" alt="身份證正面" />
                    <div class="image-overlay" v-if="canSubmit">
                      <el-icon><Edit /></el-icon>
                    </div>
                  </div>
                  <template v-if="frontImagePreview && !frontImageFile">
                    <div class="upload-status">
                      <el-icon class="status-icon"><Check /></el-icon>
                      <span>已上傳</span>
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
              
              <el-form-item label="身份證反面" prop="backImage">
                <el-upload
                  ref="backUploadRef"
                  :auto-upload="false"
                  :show-file-list="false"
                  :on-change="handleBackImageChange"
                  accept="image/*"
                  :disabled="!canSubmit"
                >
                  <div class="upload-area" v-if="!backImagePreview">
                    <el-icon class="upload-icon"><Plus /></el-icon>
                    <div class="upload-text">點擊上傳身份證反面</div>
                  </div>
                  <div class="image-preview" v-else>
                    <img :src="backImagePreview" alt="身份證反面" />
                    <div class="image-overlay" v-if="canSubmit">
                      <el-icon><Edit /></el-icon>
                    </div>
                  </div>
                  <template v-if="backImagePreview && !backImageFile">
                    <div class="upload-status">
                      <el-icon class="status-icon"><Check /></el-icon>
                      <span>已上傳</span>
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
              
              <el-form-item v-if="canSubmit">
                <el-button type="primary" @click="submitIdentity" :loading="submitLoading">
                  提交認證
                </el-button>
                <el-button @click="resetForm">
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
        
        <!-- 認證記錄 -->
        <el-col :span="8">
          <el-card>
            <template #header>
              <h3>認證記錄</h3>
            </template>
            
            <div v-if="verificationRecords.length === 0" class="no-records">
              <el-empty description="暫無認證記錄" />
            </div>
            
            <div v-else class="records-list">
              <div 
                v-for="record in verificationRecords" 
                :key="record.id"
                class="record-item"
              >
                <div class="record-header">
                  <el-tag :type="getRecordTagType(record.status)">
                    {{ getRecordStatusText(record.status) }}
                  </el-tag>
                  <span class="record-date">
                    {{ formatDate(record.submittedAt) }}
                  </span>
                </div>
                
                <div class="record-content">
                  <p><strong>姓名：</strong>{{ record.realName }}</p>
                  <p><strong>身份證：</strong>{{ record.idCardNumber }}</p>
                  
                  <div v-if="record.reviewedAt" class="review-info">
                    <p><strong>審核時間：</strong>{{ formatDate(record.reviewedAt) }}</p>
                    <p v-if="record.reviewedBy"><strong>審核人：</strong>{{ record.reviewedBy }}</p>
                    <p v-if="record.reviewComment"><strong>審核意見：</strong>{{ record.reviewComment }}</p>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-main>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules, type UploadFile } from 'element-plus'
import { 
  ArrowLeft, 
  Plus, 
  Edit,
  Check
} from '@element-plus/icons-vue'
import { useUserStore } from '../stores/user'
import { identityAPI } from '../api'

const userStore = useUserStore()
const identityFormRef = ref<FormInstance>()
const frontUploadRef = ref()
const backUploadRef = ref()
const submitLoading = ref(false)

const identityForm = reactive({
  realName: '',
  idCardNumber: ''
})

const frontImageFile = ref<File | null>(null)
const backImageFile = ref<File | null>(null)
const frontImagePreview = ref('')
const backImagePreview = ref('')
const verificationRecords = ref<any[]>([])
const existingFiles = ref({
  frontImage: false,
  backImage: false
})

const currentStatus = computed(() => userStore.user?.identityStatus || 'NOT_SUBMITTED')
const canSubmit = computed(() => currentStatus.value === 'NOT_SUBMITTED' || currentStatus.value === 'REJECTED')

// 移除驗證規則中的必填限制，改為在提交時檢查
const identityRules: FormRules = {
  realName: [
    { required: true, message: '請輸入真實姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名長度在 2 到 20 個字符', trigger: 'blur' }
  ],
  idCardNumber: [
    { required: true, message: '請輸入身份證號碼', trigger: 'blur' },
    { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, 
      message: '請輸入正確的身份證號碼', trigger: 'blur' }
  ]
}

onMounted(() => {
  loadUserData()
  loadVerificationRecords()
})

const loadUserData = () => {
  if (userStore.user) {
    identityForm.realName = userStore.user.realName || ''
    identityForm.idCardNumber = userStore.user.idCardNumber || ''
    
    // 如果已有身份證圖片，顯示預覽
    if (userStore.user.idCardFrontUrl) {
      frontImagePreview.value = `http://localhost:8080/api/files/${userStore.user.idCardFrontUrl}`
      existingFiles.value.frontImage = true
    }
    if (userStore.user.idCardBackUrl) {
      backImagePreview.value = `http://localhost:8080/api/files/${userStore.user.idCardBackUrl}`
      existingFiles.value.backImage = true
    }
  }
}

const loadVerificationRecords = async () => {
  try {
    const response = await identityAPI.getMyVerifications()
    if (response.success) {
      verificationRecords.value = response.data
    }
  } catch (error) {
    console.error('加載認證記錄失敗:', error)
  }
}

const handleFrontImageChange = (file: UploadFile) => {
  if (!file.raw) return
  
  if (file.raw.size > 10 * 1024 * 1024) {
    ElMessage.error('圖片大小不能超過 10MB')
    return
  }
  
  frontImageFile.value = file.raw
  frontImagePreview.value = URL.createObjectURL(file.raw)
  existingFiles.value.frontImage = false
}

const handleBackImageChange = (file: UploadFile) => {
  if (!file.raw) return
  
  if (file.raw.size > 10 * 1024 * 1024) {
    ElMessage.error('圖片大小不能超過 10MB')
    return
  }
  
  backImageFile.value = file.raw
  backImagePreview.value = URL.createObjectURL(file.raw)
  existingFiles.value.backImage = false
}

const submitIdentity = async () => {
  if (!identityFormRef.value) return
  
  // 驗證表單字段
  await identityFormRef.value.validate(async (valid) => {
    if (valid) {
      // 驗證圖片
      if (!frontImageFile.value && !existingFiles.value.frontImage) {
        ElMessage.error('請上傳身份證正面')
        return
      }
      if (!backImageFile.value && !existingFiles.value.backImage) {
        ElMessage.error('請上傳身份證反面')
        return
      }
      
      try {
        await ElMessageBox.confirm('確定要提交身份認證嗎？提交後將無法修改。', '確認提交', {
          confirmButtonText: '確定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        submitLoading.value = true
        
        const formData = new FormData()
        formData.append('realName', identityForm.realName)
        formData.append('idCardNumber', identityForm.idCardNumber)
        
        // 只有當新上傳了圖片時才附加到表單
        if (frontImageFile.value) {
          formData.append('frontImage', frontImageFile.value!)
        }
        
        if (backImageFile.value) {
          formData.append('backImage', backImageFile.value!)
        }
        
        // 添加標記，表明是否使用現有圖片
        if (!frontImageFile.value && existingFiles.value.frontImage) {
          formData.append('useExistingFrontImage', 'true')
        }
        
        if (!backImageFile.value && existingFiles.value.backImage) {
          formData.append('useExistingBackImage', 'true')
        }
        
        const response = await identityAPI.submitVerification(formData)
        
        if (response.success) {
          ElMessage.success('身份認證提交成功，請等待審核')
          await userStore.fetchUserProfile() // 刷新用戶信息
          loadVerificationRecords() // 刷新認證記錄
        } else {
          ElMessage.error(response.message || '提交失敗')
        }
      } catch (error: any) {
        if (error !== 'cancel') {
          ElMessage.error(error.message || '提交失敗')
        }
      } finally {
        submitLoading.value = false
      }
    }
  })
}

const resetForm = () => {
  identityFormRef.value?.resetFields()
  frontImageFile.value = null
  backImageFile.value = null
  frontImagePreview.value = ''
  backImagePreview.value = ''
  existingFiles.value.frontImage = false
  existingFiles.value.backImage = false
  loadUserData()
}

const getStatusTitle = () => {
  switch (currentStatus.value) {
    case 'PENDING': return '身份認證審核中'
    case 'APPROVED': return '身份認證已通過'
    case 'REJECTED': return '身份認證被拒絕'
    default: return ''
  }
}

const getStatusType = () => {
  switch (currentStatus.value) {
    case 'PENDING': return 'warning'
    case 'APPROVED': return 'success'
    case 'REJECTED': return 'error'
    default: return 'info'
  }
}

const getStatusDescription = () => {
  switch (currentStatus.value) {
    case 'PENDING': return '您的身份認證正在審核中，請耐心等待審核結果。'
    case 'APPROVED': return '恭喜！您的身份認證已通過審核。'
    case 'REJECTED': return '很抱歉，您的身份認證未通過審核，請重新提交。'
    default: return ''
  }
}

const getRecordTagType = (status: string) => {
  switch (status) {
    case 'PENDING': return 'warning'
    case 'APPROVED': return 'success'
    case 'REJECTED': return 'danger'
    default: return 'info'
  }
}

const getRecordStatusText = (status: string) => {
  switch (status) {
    case 'PENDING': return '待審核'
    case 'APPROVED': return '已通過'
    case 'REJECTED': return '已拒絕'
    default: return '未知'
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style scoped>
.identity-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.header-content h1 {
  margin: 0;
  font-size: 24px;
}

.main-content {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.upload-area {
  width: 200px;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 8px;
}

.upload-text {
  color: #8c939d;
  font-size: 14px;
}

.image-preview {
  position: relative;
  width: 200px;
  height: 120px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.image-overlay .el-icon {
  color: white;
  font-size: 20px;
}

.upload-status {
  display: flex;
  align-items: center;
  margin-top: 8px;
  color: #67c23a;
}

.status-icon {
  margin-right: 5px;
}

.no-records {
  text-align: center;
  padding: 20px;
}

.records-list {
  max-height: 400px;
  overflow-y: auto;
}

.record-item {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
  background: white;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.record-date {
  font-size: 12px;
  color: #909399;
}

.record-content p {
  margin: 5px 0;
  font-size: 14px;
}

.review-info {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-card__header h3) {
  margin: 0;
  color: #333;
  font-size: 16px;
}
</style>
