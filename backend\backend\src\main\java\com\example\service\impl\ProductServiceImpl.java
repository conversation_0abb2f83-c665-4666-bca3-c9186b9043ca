package com.example.service.impl;

import com.example.dto.ApiResponse;
import com.example.dto.PagedResponse;
import com.example.entity.Product;
import com.example.entity.ProductImage;
import com.example.repository.ProductRepository;
import com.example.repository.ProductImageRepository;
import com.example.repository.ProductCategoryRepository;
import com.example.service.ProductService;
import com.example.service.ProductCategoryService;
import com.example.util.ProductRedisUtil;
import com.example.util.ProductRedisKeyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 商品服務實現類
 * 實現基於Redis的緩存策略和延遲雙刪機制
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@Service
public class ProductServiceImpl implements ProductService {
    
    @Autowired
    private ProductRepository productRepository;
    
    @Autowired
    private ProductImageRepository productImageRepository;
    
    @Autowired
    private ProductCategoryRepository categoryRepository;
    
    @Autowired
    private ProductRedisUtil redisUtil;

    @Autowired
    private ProductCategoryService categoryService;

    @Autowired
    private ScheduledExecutorService threadPool;
    
    @Override
    public ApiResponse<PagedResponse<Product>> getProducts(Pageable pageable) {
        try {
            // 1. 嘗試從緩存獲取
            String cacheKey = ProductRedisKeyUtil.categoryProductListKey(0L,
                    pageable.getPageNumber(), pageable.getPageSize());

            PagedResponse<Product> response = null;
            try {
                Object cachedResult = redisUtil.safeGetObject(cacheKey);
                if (cachedResult != null) {
                    log.debug("從緩存獲取商品列表");
                    return ApiResponse.success((PagedResponse<Product>) cachedResult);
                }
            } catch (Exception cacheEx) {
                log.warn("緩存獲取失敗，將從數據庫查詢: {}", cacheEx.getMessage());
            }

            // 2. 從數據庫查詢
            Page<Product> productPage = productRepository.findActiveProducts(pageable);
            response = PagedResponse.of(productPage);

            // 3. 嘗試緩存結果
            try {
                if (response.getContent().isEmpty()) {
                    redisUtil.cacheEmptyResult(cacheKey);
                } else {
                    redisUtil.setObject(cacheKey, response, ProductRedisUtil.ExpireTime.PRODUCT_LIST);
                }
            } catch (Exception cacheEx) {
                log.warn("緩存保存失敗，但數據庫查詢成功: {}", cacheEx.getMessage());
            }

            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("獲取商品列表失敗", e);
            return ApiResponse.error("獲取商品列表失敗: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<PagedResponse<Product>> getProductsByCategory(Long categoryId, Pageable pageable) {
        try {
            // 1. 嘗試從緩存獲取
            String cacheKey = ProductRedisKeyUtil.categoryProductListKey(categoryId,
                    pageable.getPageNumber(), pageable.getPageSize());
            Object cachedResult = redisUtil.safeGetObject(cacheKey);

            if (cachedResult != null) {
                log.debug("從緩存獲取分類商品列表: categoryId={}", categoryId);
                return ApiResponse.success((PagedResponse<Product>) cachedResult);
            }

            // 2. 獲取分類及其所有子分類ID
            List<Long> categoryIds = categoryService.getCategoryAndDescendantIds(categoryId);

            // 3. 從數據庫查詢（包含子分類的商品）
            Page<Product> productPage = productRepository.findByCategoryIdsAndStatus(
                    categoryIds, Product.Status.ON_SHELF, pageable);
            PagedResponse<Product> response = PagedResponse.of(productPage);

            // 4. 緩存結果
            if (response.getContent().isEmpty()) {
                redisUtil.cacheEmptyResult(cacheKey);
            } else {
                redisUtil.setObject(cacheKey, response, ProductRedisUtil.ExpireTime.PRODUCT_LIST);
            }

            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("獲取分類商品列表失敗: categoryId={}", categoryId, e);
            return ApiResponse.error("獲取分類商品列表失敗: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Product> getProductById(Long id) {
        try {
            // 1. 嘗試從緩存獲取
            String cacheKey = ProductRedisKeyUtil.productDetailKey(id);
            Object cachedProduct = redisUtil.safeGetObject(cacheKey);
            
            if (cachedProduct != null) {
                log.debug("從緩存獲取商品詳情: id={}", id);
                return ApiResponse.success((Product) cachedProduct);
            }
            
            // 2. 從數據庫查詢
            Optional<Product> productOpt = productRepository.findById(id);
            if (productOpt.isEmpty()) {
                // 緩存空結果，防止緩存穿透
                redisUtil.cacheEmptyResult(cacheKey);
                return ApiResponse.error("商品不存在");
            }
            
            Product product = productOpt.get();
            
            // 3. 檢查商品狀態
            if (product.getStatus() == Product.Status.DELETED) {
                redisUtil.cacheEmptyResult(cacheKey);
                return ApiResponse.error("商品不存在");
            }
            
            // 4. 緩存結果
            redisUtil.setObject(cacheKey, product, ProductRedisUtil.ExpireTime.PRODUCT_DETAIL);
            
            return ApiResponse.success(product);
            
        } catch (Exception e) {
            log.error("獲取商品詳情失敗: id={}", id, e);
            return ApiResponse.error("獲取商品詳情失敗: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<PagedResponse<Product>> getHotProducts(Pageable pageable) {
        try {
            // 1. 嘗試從緩存獲取
            String cacheKey = ProductRedisKeyUtil.hotProductListKey(
                    pageable.getPageNumber(), pageable.getPageSize());
            Object cachedResult = redisUtil.safeGetObject(cacheKey);
            
            if (cachedResult != null) {
                log.debug("從緩存獲取熱門商品列表");
                return ApiResponse.success((PagedResponse<Product>) cachedResult);
            }
            
            // 2. 從數據庫查詢
            Page<Product> productPage = productRepository.findHotProducts(pageable);
            PagedResponse<Product> response = PagedResponse.of(productPage);
            
            // 3. 緩存結果
            if (response.getContent().isEmpty()) {
                redisUtil.cacheEmptyResult(cacheKey);
            } else {
                redisUtil.setObject(cacheKey, response, ProductRedisUtil.ExpireTime.HOT_PRODUCTS);
            }
            
            return ApiResponse.success(response);
            
        } catch (Exception e) {
            log.error("獲取熱門商品失敗", e);
            return ApiResponse.error("獲取熱門商品失敗: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<PagedResponse<Product>> searchProductsByKeyword(String keyword, Pageable pageable) {
        try {
            // 1. 嘗試從緩存獲取
            String cacheKey = ProductRedisKeyUtil.searchResultKey(keyword, 
                    pageable.getPageNumber(), pageable.getPageSize());
            Object cachedResult = redisUtil.safeGetObject(cacheKey);
            
            if (cachedResult != null) {
                log.debug("從緩存獲取搜索結果: keyword={}", keyword);
                return ApiResponse.success((PagedResponse<Product>) cachedResult);
            }
            
            // 2. 從數據庫搜索
            Page<Product> productPage = productRepository.searchProducts(keyword, pageable);
            PagedResponse<Product> response = PagedResponse.of(productPage);
            
            // 3. 緩存結果
            if (response.getContent().isEmpty()) {
                redisUtil.cacheEmptyResult(cacheKey);
            } else {
                redisUtil.setObject(cacheKey, response, ProductRedisUtil.ExpireTime.SEARCH_RESULT);
            }
            
            return ApiResponse.success(response);
            
        } catch (Exception e) {
            log.error("搜索商品失敗: keyword={}", keyword, e);
            return ApiResponse.error("搜索商品失敗: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public ApiResponse<Product> createProduct(Product product, Long createdBy) {
        try {
            // 1. 數據驗證
            if (productRepository.existsByName(product.getName())) {
                return ApiResponse.error("商品名稱已存在");
            }
            
            // 2. 檢查分類是否存在且為葉子節點
            if (!categoryRepository.existsById(product.getCategoryId())) {
                return ApiResponse.error("商品分類不存在");
            }
            
            // 3. 第一次刪除緩存
            clearProductCache(product.getCategoryId());
            
            // 4. 設置商品屬性
            product.setCreatedBy(createdBy);
            product.setUpdatedBy(createdBy);
            
            // 5. 保存商品
            Product savedProduct = productRepository.save(product);
            
            // 6. 延遲雙刪
            scheduleDelayedCacheClear(product.getCategoryId());
            
            return ApiResponse.success(savedProduct);
            
        } catch (Exception e) {
            log.error("創建商品失敗", e);
            return ApiResponse.error("創建商品失敗: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public ApiResponse<Product> updateProduct(Long id, Product product, Long updatedBy) {
        try {
            // 1. 檢查商品是否存在
            Optional<Product> existingOpt = productRepository.findById(id);
            if (existingOpt.isEmpty()) {
                return ApiResponse.error("商品不存在");
            }
            
            Product existing = existingOpt.get();
            Long oldCategoryId = existing.getCategoryId();
            
            // 2. 檢查名稱重複
            if (productRepository.existsByNameAndIdNot(product.getName(), id)) {
                return ApiResponse.error("商品名稱已存在");
            }
            
            // 3. 第一次刪除緩存
            clearProductCache(oldCategoryId);
            if (!oldCategoryId.equals(product.getCategoryId())) {
                clearProductCache(product.getCategoryId());
            }
            
            // 4. 更新屬性
            existing.setName(product.getName());
            existing.setDescription(product.getDescription());
            existing.setCategoryId(product.getCategoryId());
            existing.setPrice(product.getPrice());
            existing.setOriginalPrice(product.getOriginalPrice());
            existing.setStock(product.getStock());
            existing.setBrand(product.getBrand());
            existing.setModel(product.getModel());
            existing.setTags(product.getTags());
            existing.setWeight(product.getWeight());
            existing.setUpdatedBy(updatedBy);
            
            // 5. 保存更新
            Product updatedProduct = productRepository.save(existing);
            
            // 6. 延遲雙刪
            scheduleDelayedCacheClear(oldCategoryId);
            if (!oldCategoryId.equals(product.getCategoryId())) {
                scheduleDelayedCacheClear(product.getCategoryId());
            }
            
            return ApiResponse.success(updatedProduct);
            
        } catch (Exception e) {
            log.error("更新商品失敗: id={}", id, e);
            return ApiResponse.error("更新商品失敗: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<String> increaseViewCount(Long productId) {
        try {
            String viewCountKey = ProductRedisKeyUtil.productViewCountKey(productId);
            long count = redisUtil.increment(viewCountKey);
            
            // 設置過期時間（如果是新key）
            if (count == 1) {
                redisUtil.expire(viewCountKey, 24 * 60 * 60); // 24小時
            }
            
            return ApiResponse.success("瀏覽次數已更新: " + count);
            
        } catch (Exception e) {
            log.error("增加瀏覽次數失敗: productId={}", productId, e);
            return ApiResponse.error("增加瀏覽次數失敗: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<Long> getViewCount(Long productId) {
        try {
            String viewCountKey = ProductRedisKeyUtil.productViewCountKey(productId);
            Object count = redisUtil.getObject(viewCountKey);
            return ApiResponse.success(count != null ? Long.valueOf(count.toString()) : 0L);
        } catch (Exception e) {
            log.error("獲取瀏覽次數失敗: productId={}", productId, e);
            return ApiResponse.error("獲取瀏覽次數失敗: " + e.getMessage());
        }
    }
    
    /**
     * 清除商品相關緩存
     */
    private void clearProductCache(Long categoryId) {
        try {
            // 清除分類商品列表緩存
            redisUtil.deletePattern(ProductRedisKeyUtil.categoryProductCachePattern(categoryId));
            // 清除熱門商品緩存
            redisUtil.deletePattern(ProductRedisKeyUtil.allProductCachePattern());
            log.debug("清除商品緩存: categoryId={}", categoryId);
        } catch (Exception e) {
            log.error("清除商品緩存失敗: categoryId={}", categoryId, e);
        }
    }
    
    /**
     * 延遲雙刪 - 基於學習的延遲雙刪策略
     */
    private void scheduleDelayedCacheClear(Long categoryId) {
        threadPool.schedule(() -> {
            try {
                clearProductCache(categoryId);
                log.debug("執行延遲雙刪: categoryId={}", categoryId);
            } catch (Exception e) {
                log.error("延遲雙刪失敗: categoryId={}", categoryId, e);
            }
        }, 10, TimeUnit.SECONDS);
    }
    
    @Override
    public ApiResponse<PagedResponse<Product>> searchProducts(String name, Long categoryId, Integer status, BigDecimal minPrice, BigDecimal maxPrice, String brand, Integer isRecommended, Integer isHot, Pageable pageable) {
        try {
            // 1. 生成緩存Key（包含排序信息）
            String sortInfo = pageable.getSort().toString(); // 獲取排序信息
            String filterHash = ProductRedisKeyUtil.generateFilterHash(
                    name, categoryId, status, minPrice, maxPrice, brand, isRecommended, isHot, sortInfo);
            String cacheKey = ProductRedisKeyUtil.filterResultKey(filterHash,
                    pageable.getPageNumber(), pageable.getPageSize());

            PagedResponse<Product> response = null;
            try {
                Object cachedResult = redisUtil.safeGetObject(cacheKey);
                if (cachedResult != null) {
                    log.debug("從緩存獲取多條件篩選結果");
                    return ApiResponse.success((PagedResponse<Product>) cachedResult);
                }
            } catch (Exception cacheEx) {
                log.warn("緩存獲取失敗，將從數據庫查詢: {}", cacheEx.getMessage());
            }

            // 2. 從數據庫查詢
            Page<Product> productPage = productRepository.findProductsWithFilters(
                    name, categoryId, status, minPrice, maxPrice, brand, isRecommended, isHot, pageable);
            response = PagedResponse.of(productPage);

            // 3. 嘗試緩存結果
            try {
                if (response.getContent().isEmpty()) {
                    redisUtil.cacheEmptyResult(cacheKey);
                } else {
                    redisUtil.setObject(cacheKey, response, ProductRedisUtil.ExpireTime.PRODUCT_LIST);
                }
            } catch (Exception cacheEx) {
                log.warn("緩存保存失敗，但數據庫查詢成功: {}", cacheEx.getMessage());
            }

            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("多條件篩選商品失敗", e);
            return ApiResponse.error("多條件篩選商品失敗: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<PagedResponse<Product>> getRecommendedProducts(Pageable pageable) {
        try {
            // 1. 嘗試從緩存獲取
            String cacheKey = ProductRedisKeyUtil.recommendedProductListKey(
                    pageable.getPageNumber(), pageable.getPageSize());

            PagedResponse<Product> response = null;
            try {
                Object cachedResult = redisUtil.safeGetObject(cacheKey);
                if (cachedResult != null) {
                    log.debug("從緩存獲取推薦商品列表");
                    return ApiResponse.success((PagedResponse<Product>) cachedResult);
                }
            } catch (Exception cacheEx) {
                log.warn("緩存獲取失敗，將從數據庫查詢: {}", cacheEx.getMessage());
            }

            // 2. 從數據庫查詢
            Page<Product> productPage = productRepository.findRecommendedProducts(pageable);
            response = PagedResponse.of(productPage);

            // 3. 嘗試緩存結果
            try {
                if (response.getContent().isEmpty()) {
                    redisUtil.cacheEmptyResult(cacheKey);
                } else {
                    redisUtil.setObject(cacheKey, response, ProductRedisUtil.ExpireTime.PRODUCT_LIST);
                }
            } catch (Exception cacheEx) {
                log.warn("緩存保存失敗，但數據庫查詢成功: {}", cacheEx.getMessage());
            }

            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("獲取推薦商品列表失敗", e);
            return ApiResponse.error("獲取推薦商品列表失敗: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<PagedResponse<Product>> getLatestProducts(Pageable pageable) {
        try {
            // 1. 嘗試從緩存獲取
            String cacheKey = ProductRedisKeyUtil.latestProductListKey(
                    pageable.getPageNumber(), pageable.getPageSize());

            PagedResponse<Product> response = null;
            try {
                Object cachedResult = redisUtil.safeGetObject(cacheKey);
                if (cachedResult != null) {
                    log.debug("從緩存獲取最新商品列表");
                    return ApiResponse.success((PagedResponse<Product>) cachedResult);
                }
            } catch (Exception cacheEx) {
                log.warn("緩存獲取失敗，將從數據庫查詢: {}", cacheEx.getMessage());
            }

            // 2. 從數據庫查詢
            Page<Product> productPage = productRepository.findLatestProducts(pageable);
            response = PagedResponse.of(productPage);

            // 3. 嘗試緩存結果
            try {
                if (response.getContent().isEmpty()) {
                    redisUtil.cacheEmptyResult(cacheKey);
                } else {
                    redisUtil.setObject(cacheKey, response, ProductRedisUtil.ExpireTime.PRODUCT_LIST);
                }
            } catch (Exception cacheEx) {
                log.warn("緩存保存失敗，但數據庫查詢成功: {}", cacheEx.getMessage());
            }

            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("獲取最新商品列表失敗", e);
            return ApiResponse.error("獲取最新商品列表失敗: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<PagedResponse<Product>> getProductsBySoldCount(Pageable pageable) {
        try {
            // 1. 嘗試從緩存獲取
            String cacheKey = ProductRedisKeyUtil.productSortedListKey("soldCount", "desc",
                    pageable.getPageNumber(), pageable.getPageSize());

            PagedResponse<Product> response = null;
            try {
                Object cachedResult = redisUtil.safeGetObject(cacheKey);
                if (cachedResult != null) {
                    log.debug("從緩存獲取銷量排序商品列表");
                    return ApiResponse.success((PagedResponse<Product>) cachedResult);
                }
            } catch (Exception cacheEx) {
                log.warn("緩存獲取失敗，將從數據庫查詢: {}", cacheEx.getMessage());
            }

            // 2. 從數據庫查詢
            Page<Product> productPage = productRepository.findProductsBySoldCount(pageable);
            response = PagedResponse.of(productPage);

            // 3. 嘗試緩存結果
            try {
                if (response.getContent().isEmpty()) {
                    redisUtil.cacheEmptyResult(cacheKey);
                } else {
                    redisUtil.setObject(cacheKey, response, ProductRedisUtil.ExpireTime.PRODUCT_LIST);
                }
            } catch (Exception cacheEx) {
                log.warn("緩存保存失敗，但數據庫查詢成功: {}", cacheEx.getMessage());
            }

            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("獲取銷量排序商品列表失敗", e);
            return ApiResponse.error("獲取銷量排序商品列表失敗: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<PagedResponse<Product>> getProductsByPrice(boolean ascending, Pageable pageable) {
        try {
            // 1. 嘗試從緩存獲取
            String sortDirection = ascending ? "asc" : "desc";
            String cacheKey = ProductRedisKeyUtil.productSortedListKey("price", sortDirection,
                    pageable.getPageNumber(), pageable.getPageSize());

            PagedResponse<Product> response = null;
            try {
                Object cachedResult = redisUtil.safeGetObject(cacheKey);
                if (cachedResult != null) {
                    log.debug("從緩存獲取價格排序商品列表: ascending={}", ascending);
                    return ApiResponse.success((PagedResponse<Product>) cachedResult);
                }
            } catch (Exception cacheEx) {
                log.warn("緩存獲取失敗，將從數據庫查詢: {}", cacheEx.getMessage());
            }

            // 2. 從數據庫查詢
            Page<Product> productPage;
            if (ascending) {
                productPage = productRepository.findProductsByPriceAsc(pageable);
            } else {
                productPage = productRepository.findProductsByPriceDesc(pageable);
            }
            response = PagedResponse.of(productPage);

            // 3. 嘗試緩存結果
            try {
                if (response.getContent().isEmpty()) {
                    redisUtil.cacheEmptyResult(cacheKey);
                } else {
                    redisUtil.setObject(cacheKey, response, ProductRedisUtil.ExpireTime.PRODUCT_LIST);
                }
            } catch (Exception cacheEx) {
                log.warn("緩存保存失敗，但數據庫查詢成功: {}", cacheEx.getMessage());
            }

            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("獲取價格排序商品列表失敗: ascending={}", ascending, e);
            return ApiResponse.error("獲取價格排序商品列表失敗: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse<List<Product>> getRelatedProducts(Long productId, int limit) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> deleteProduct(Long id, Long deletedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> batchDeleteProducts(List<Long> ids, Long deletedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> toggleProductStatus(Long id, Integer status, Long updatedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> batchUpdateProductStatus(List<Long> ids, Integer status, Long updatedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> setRecommended(Long id, Integer isRecommended, Long updatedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> setHot(Long id, Integer isHot, Long updatedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> updateProductSort(Long id, Integer sortOrder, Long updatedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> uploadProductImage(Long productId, MultipartFile file, Integer sortOrder, Integer isMain, Long uploadedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<List<String>> batchUploadProductImages(Long productId, List<MultipartFile> files, Long uploadedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> deleteProductImage(Long productId, Long imageId, Long deletedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> setMainImage(Long productId, Long imageId, Long updatedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> updateImageSort(Long imageId, Integer sortOrder, Long updatedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> decreaseStock(Long productId, Integer quantity) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> increaseStock(Long productId, Integer quantity) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> updateStock(Long productId, Integer stock, Long updatedBy) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> increaseSoldCount(Long productId, Integer quantity) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<List<Product>> getLowStockProducts(Integer threshold) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<Object> getProductStatistics() {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<Object> getProductStatusStatistics() {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<Long> getProductCountByCategory(Long categoryId) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> refreshProductCache(Long productId) {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> clearAllProductCache() {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<String> warmupHotProductsCache() {
        return ApiResponse.error("功能開發中");
    }
    
    @Override
    public ApiResponse<Boolean> checkProductNameExists(String name, Long excludeId) {
        return ApiResponse.error("功能開發中");
    }
}
