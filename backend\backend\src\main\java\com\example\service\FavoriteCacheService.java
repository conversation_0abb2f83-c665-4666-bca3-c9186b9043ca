package com.example.service;

import com.example.config.FavoriteCacheConfig;
import com.example.dto.FavoriteItemDto;
import com.example.dto.FavoriteStatsDto;
import com.example.dto.PagedResponse;
import com.example.enums.ItemType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

import java.util.concurrent.Callable;

/**
 * 收藏功能缓存服务
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
public class FavoriteCacheService {
    
    private static final Logger logger = LoggerFactory.getLogger(FavoriteCacheService.class);
    
    @Autowired
    private CacheManager cacheManager;
    
    /**
     * 获取收藏排行榜缓存
     */
    public PagedResponse<FavoriteItemDto> getRankingCache(int page, int size, ItemType itemType) {
        try {
            Cache cache = cacheManager.getCache(FavoriteCacheConfig.FAVORITE_RANKING_CACHE);
            if (cache != null) {
                String key = buildRankingCacheKey(page, size, itemType);
                Cache.ValueWrapper wrapper = cache.get(key);
                if (wrapper != null) {
                    @SuppressWarnings("unchecked")
                    PagedResponse<FavoriteItemDto> result = (PagedResponse<FavoriteItemDto>) wrapper.get();
                    logger.debug("命中排行榜缓存: {}", key);
                    return result;
                }
            }
        } catch (Exception e) {
            logger.warn("获取排行榜缓存失败: page={}, size={}, itemType={}", page, size, itemType, e);
        }
        return null;
    }
    
    /**
     * 设置收藏排行榜缓存
     */
    public void setRankingCache(int page, int size, ItemType itemType, PagedResponse<FavoriteItemDto> data) {
        try {
            Cache cache = cacheManager.getCache(FavoriteCacheConfig.FAVORITE_RANKING_CACHE);
            if (cache != null) {
                String key = buildRankingCacheKey(page, size, itemType);
                cache.put(key, data);
                logger.debug("设置排行榜缓存: {}", key);
            }
        } catch (Exception e) {
            logger.warn("设置排行榜缓存失败: page={}, size={}, itemType={}", page, size, itemType, e);
        }
    }
    
    /**
     * 获取收藏统计缓存
     */
    public FavoriteStatsDto getStatsCache(Long itemId, Long userId) {
        try {
            Cache cache = cacheManager.getCache(FavoriteCacheConfig.FAVORITE_STATS_CACHE);
            if (cache != null) {
                String key = buildStatsCacheKey(itemId, userId);
                Cache.ValueWrapper wrapper = cache.get(key);
                if (wrapper != null) {
                    FavoriteStatsDto result = (FavoriteStatsDto) wrapper.get();
                    logger.debug("命中统计缓存: {}", key);
                    return result;
                }
            }
        } catch (Exception e) {
            logger.warn("获取统计缓存失败: itemId={}, userId={}", itemId, userId, e);
        }
        return null;
    }
    
    /**
     * 设置收藏统计缓存
     */
    public void setStatsCache(Long itemId, Long userId, FavoriteStatsDto stats) {
        try {
            Cache cache = cacheManager.getCache(FavoriteCacheConfig.FAVORITE_STATS_CACHE);
            if (cache != null) {
                String key = buildStatsCacheKey(itemId, userId);
                cache.put(key, stats);
                logger.debug("设置统计缓存: {}", key);
            }
        } catch (Exception e) {
            logger.warn("设置统计缓存失败: itemId={}, userId={}", itemId, userId, e);
        }
    }
    
    /**
     * 获取收藏计数缓存
     */
    public Integer getCountCache(Long itemId) {
        try {
            Cache cache = cacheManager.getCache(FavoriteCacheConfig.FAVORITE_COUNT_CACHE);
            if (cache != null) {
                String key = "item:" + itemId;
                Cache.ValueWrapper wrapper = cache.get(key);
                if (wrapper != null) {
                    Integer result = (Integer) wrapper.get();
                    logger.debug("命中计数缓存: {}", key);
                    return result;
                }
            }
        } catch (Exception e) {
            logger.warn("获取计数缓存失败: itemId={}", itemId, e);
        }
        return null;
    }
    
    /**
     * 设置收藏计数缓存
     */
    public void setCountCache(Long itemId, Integer count) {
        try {
            Cache cache = cacheManager.getCache(FavoriteCacheConfig.FAVORITE_COUNT_CACHE);
            if (cache != null) {
                String key = "item:" + itemId;
                cache.put(key, count);
                logger.debug("设置计数缓存: {}", key);
            }
        } catch (Exception e) {
            logger.warn("设置计数缓存失败: itemId={}, count={}", itemId, count, e);
        }
    }
    
    /**
     * 清除与特定内容相关的所有缓存
     */
    public void evictItemRelatedCache(Long itemId) {
        try {
            // 清除排行榜缓存
            Cache rankingCache = cacheManager.getCache(FavoriteCacheConfig.FAVORITE_RANKING_CACHE);
            if (rankingCache != null) {
                rankingCache.clear(); // 简化处理，清除整个排行榜缓存
            }
            
            // 清除统计缓存
            Cache statsCache = cacheManager.getCache(FavoriteCacheConfig.FAVORITE_STATS_CACHE);
            if (statsCache != null) {
                // 这里应该清除所有与该itemId相关的统计缓存
                // 由于无法精确匹配，简化处理为清除整个缓存
                statsCache.clear();
            }
            
            // 清除计数缓存
            Cache countCache = cacheManager.getCache(FavoriteCacheConfig.FAVORITE_COUNT_CACHE);
            if (countCache != null) {
                String key = "item:" + itemId;
                countCache.evict(key);
            }
            
            logger.debug("清除内容相关缓存: itemId={}", itemId);
        } catch (Exception e) {
            logger.warn("清除内容相关缓存失败: itemId={}", itemId, e);
        }
    }
    
    /**
     * 清除用户相关缓存
     */
    public void evictUserRelatedCache(Long userId) {
        try {
            Cache userFavoritesCache = cacheManager.getCache(FavoriteCacheConfig.USER_FAVORITES_CACHE);
            if (userFavoritesCache != null) {
                // 清除用户收藏列表缓存
                userFavoritesCache.clear(); // 简化处理
            }
            
            logger.debug("清除用户相关缓存: userId={}", userId);
        } catch (Exception e) {
            logger.warn("清除用户相关缓存失败: userId={}", userId, e);
        }
    }
    
    /**
     * 使用缓存或执行回调
     */
    public <T> T getOrSet(String cacheName, String key, Callable<T> valueLoader) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                Cache.ValueWrapper wrapper = cache.get(key);
                if (wrapper != null) {
                    @SuppressWarnings("unchecked")
                    T result = (T) wrapper.get();
                    logger.debug("命中缓存: {}:{}", cacheName, key);
                    return result;
                }
                
                // 缓存未命中，执行回调并缓存结果
                T result = valueLoader.call();
                if (result != null) {
                    cache.put(key, result);
                    logger.debug("设置缓存: {}:{}", cacheName, key);
                }
                return result;
            }
        } catch (Exception e) {
            logger.warn("缓存操作失败: {}:{}", cacheName, key, e);
        }
        
        // 缓存失败，直接执行回调
        try {
            return valueLoader.call();
        } catch (Exception e) {
            logger.error("执行回调失败: {}:{}", cacheName, key, e);
            return null;
        }
    }
    
    /**
     * 构建排行榜缓存键
     */
    private String buildRankingCacheKey(int page, int size, ItemType itemType) {
        return String.format("ranking:%s:%d:%d", 
                itemType != null ? itemType.name() : "ALL", page, size);
    }
    
    /**
     * 构建统计缓存键
     */
    private String buildStatsCacheKey(Long itemId, Long userId) {
        return String.format("stats:%d:%s", itemId, userId != null ? userId : "anonymous");
    }
}
