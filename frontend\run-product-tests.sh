#!/bin/bash

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}========================================"
echo -e "商品排序和篩選功能測試"
echo -e "========================================${NC}"

echo ""
echo -e "${CYAN}🔍 檢查環境...${NC}"

# 檢查 Node.js
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js 未安裝，請先安裝 Node.js${NC}"
    exit 1
fi

# 檢查 npm
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ npm 未安裝${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js 和 npm 已安裝${NC}"

# 檢查 Playwright
if ! npx playwright --version &> /dev/null; then
    echo -e "${YELLOW}📦 安裝 Playwright...${NC}"
    npm install -D @playwright/test
    npx playwright install
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Playwright 安裝失敗${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}✅ Playwright 已準備就緒${NC}"

echo ""
echo -e "${CYAN}🚀 開始運行測試...${NC}"
echo ""

# 運行測試
npx playwright test tests/product-sorting-filtering.spec.ts --reporter=list

if [ $? -ne 0 ]; then
    echo ""
    echo -e "${RED}❌ 測試執行失敗${NC}"
    echo ""
    echo -e "${YELLOW}🔧 故障排除建議:${NC}"
    echo -e "${YELLOW}1. 確保前端服務正在運行 (npm run dev)${NC}"
    echo -e "${YELLOW}2. 確保後端服務正在運行 (Spring Boot)${NC}"
    echo -e "${YELLOW}3. 確保數據庫連接正常${NC}"
    echo -e "${YELLOW}4. 檢查網絡連接${NC}"
    echo ""
    exit 1
else
    echo ""
    echo -e "${GREEN}🎉 所有測試完成！${NC}"
    echo ""
fi

# 詢問是否查看報告
echo -e "${YELLOW}📊 是否查看詳細測試報告？(y/N)${NC}"
read -r choice
if [[ "$choice" =~ ^[Yy]$ ]]; then
    npx playwright show-report
fi
