package com.example.service;

import com.example.util.BatchOptimizer;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 性能監控服務
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PerformanceMonitorService {

    private final MeterRegistry meterRegistry;
    private final RedisTemplate<String, Object> redisTemplate;
    private final BatchOptimizer batchOptimizer;

    // 性能指標
    private Timer databaseQueryTimer;
    private Timer redisOperationTimer;
    private Counter cacheHitCounter;
    private Counter cacheMissCounter;
    private Counter slowQueryCounter;

    // 慢查詢記錄
    private final Map<String, Long> slowQueries = new ConcurrentHashMap<>();
    private static final long SLOW_QUERY_THRESHOLD = 1000; // 1秒

    @PostConstruct
    public void init() {
        // 初始化性能指標
        databaseQueryTimer = Timer.builder("database.query.duration")
                .description("Database query execution time")
                .register(meterRegistry);

        redisOperationTimer = Timer.builder("redis.operation.duration")
                .description("Redis operation execution time")
                .register(meterRegistry);

        cacheHitCounter = Counter.builder("cache.hit")
                .description("Cache hit count")
                .register(meterRegistry);

        cacheMissCounter = Counter.builder("cache.miss")
                .description("Cache miss count")
                .register(meterRegistry);

        slowQueryCounter = Counter.builder("database.slow.query")
                .description("Slow query count")
                .register(meterRegistry);

        // 初始化批處理優化器
        batchOptimizer.init();

        log.info("性能監控服務已啟動");
    }

    /**
     * 記錄數據庫查詢時間
     */
    public void recordDatabaseQuery(String queryName, Duration duration) {
        databaseQueryTimer.record(duration);
        
        // 記錄慢查詢
        if (duration.toMillis() > SLOW_QUERY_THRESHOLD) {
            slowQueryCounter.increment();
            slowQueries.put(queryName + "_" + LocalDateTime.now(), duration.toMillis());
            log.warn("慢查詢檢測: {} 耗時 {}ms", queryName, duration.toMillis());
        }
    }

    /**
     * 記錄Redis操作時間
     */
    public void recordRedisOperation(String operation, Duration duration) {
        redisOperationTimer.record(duration);
    }

    /**
     * 記錄緩存命中
     */
    public void recordCacheHit() {
        cacheHitCounter.increment();
    }

    /**
     * 記錄緩存未命中
     */
    public void recordCacheMiss() {
        cacheMissCounter.increment();
    }

    /**
     * 獲取性能統計信息
     */
    public Map<String, Object> getPerformanceStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 數據庫查詢統計
        stats.put("database_query_count", databaseQueryTimer.count());
        stats.put("database_query_avg_time", databaseQueryTimer.mean(java.util.concurrent.TimeUnit.MILLISECONDS));
        stats.put("database_query_max_time", databaseQueryTimer.max(java.util.concurrent.TimeUnit.MILLISECONDS));
        
        // Redis操作統計
        stats.put("redis_operation_count", redisOperationTimer.count());
        stats.put("redis_operation_avg_time", redisOperationTimer.mean(java.util.concurrent.TimeUnit.MILLISECONDS));
        
        // 緩存統計
        double cacheHits = cacheHitCounter.count();
        double cacheMisses = cacheMissCounter.count();
        double totalCacheRequests = cacheHits + cacheMisses;
        double hitRate = totalCacheRequests > 0 ? (cacheHits / totalCacheRequests) * 100 : 0;
        
        stats.put("cache_hit_count", cacheHits);
        stats.put("cache_miss_count", cacheMisses);
        stats.put("cache_hit_rate", String.format("%.2f%%", hitRate));
        
        // 慢查詢統計
        stats.put("slow_query_count", slowQueryCounter.count());
        stats.put("recent_slow_queries", slowQueries);
        
        // 批處理統計
        stats.put("batch_statistics", batchOptimizer.getStatistics());
        
        return stats;
    }

    /**
     * 獲取Redis連接信息
     */
    public Map<String, Object> getRedisStats() {
        Map<String, Object> stats = new HashMap<>();
        try {
            // 獲取Redis信息
            Object info = redisTemplate.execute(connection -> {
                return connection.info();
            });
            stats.put("redis_info", info);
        } catch (Exception e) {
            log.error("獲取Redis統計信息失敗", e);
            stats.put("error", e.getMessage());
        }
        return stats;
    }

    /**
     * 清理舊的慢查詢記錄
     */
    public void cleanupSlowQueries() {
        if (slowQueries.size() > 100) {
            // 保留最近的50條記錄
            slowQueries.entrySet().removeIf(entry -> 
                slowQueries.size() > 50);
        }
    }

    /**
     * 重置性能統計
     */
    public void resetStats() {
        slowQueries.clear();
        log.info("性能統計已重置");
    }
}
