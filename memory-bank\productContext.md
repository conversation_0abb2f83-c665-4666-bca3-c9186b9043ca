# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-07-10 15:48:02 - Log of updates made will be appended as footnotes to the end of this file.

## Project Goal

Vue3 + TypeScript + SpringBoot + Redis 用戶認證系統，提供完整的用戶註冊、登入、郵件驗證和身份認證功能

## Key Features

* 用戶註冊/登入系統：支持 @d1y.me 郵箱域名註冊，JWT 認證
* 郵件驗證功能：5分鐘最多2次，一天最多5次限制
* 身份認證系統：上傳身份證正反面，人工審核機制
* 管理員審核後台：管理員可查看待審核申請並進行審核
* 文件上傳功能：支持圖片上傳，10MB 限制
* Redis 緩存：驗證碼存儲和會話管理
* 用戶關注系統：關注/取消關注、關注列表、粉絲列表、用戶推薦、共同關注功能
* 分層菜單系統：支持多級菜單結構，Redis緩存優化，權限控制和動態加載
* 商品管理系統：商品分類樹形結構、商品CRUD、Redis緩存優化、圖片管理、庫存統計
* 商品排序和篩選系統：支持默認排序、價格排序、銷量排序、推薦商品篩選、熱門商品篩選，包含完整的前後端實現和自動化測試
* 購物車和支付系統：完整的電商購物車功能、支付寶沙箱支付、訂單管理、支付狀態跟蹤

## Overall Architecture

* 前端：Vue3 + TypeScript + Vite + Element Plus + Pinia
* 後端：SpringBoot 3.2.12 + JPA + MySQL + Redis + Spring Security + JWT
* 數據庫：MySQL (用戶數據) + Redis (緩存和驗證碼)
* 郵件服務：Spring Boot Starter Mail

[2025-07-24 13:30:44] - New feature: 完善商品排序和篩選功能，包括後端API實現、前端邏輯優化和Playwright自動化測試
[2025-07-23 21:02:23] - New feature: 完成購物車和支付寶支付功能的完整開發，包括後端實體、服務、控制器，前端頁面，以及測試腳本和部署指南
[2025-07-17 14:54:17] - New feature: 完成商品管理系統後端開發，包括實體類、Repository、Service、Controller和數據庫表結構
[2025-07-16 11:25:51] - New feature: 完成分層菜單Redis緩存系統實現，包含後端API、前端組件、數據庫表結構和緩存機制
[2025-07-15 09:43:39] - New feature: 完成用戶關注系統實現 - API控制器、前端集成、數據同步和測試驗證
[2025-07-10 15:59:47] - New feature: 完善身份認證系統 - 恢復SecurityConfig配置，完成所有身份認證API端點和管理員審核功能