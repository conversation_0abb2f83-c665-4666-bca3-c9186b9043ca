package com.example;

import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.Properties;

public class SimpleEmailTest {
    
    public static void main(String[] args) {
        try {
            // 創建郵件發送器
            JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
            mailSender.setHost("smtp.gmail.com");
            mailSender.setPort(587);
            mailSender.setUsername("<EMAIL>");
            mailSender.setPassword("pddi avwc nxuw eqam");
            
            Properties props = mailSender.getJavaMailProperties();
            props.put("mail.transport.protocol", "smtp");
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.debug", "true");
            
            // 創建郵件消息
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom("<EMAIL>");
            message.setTo("<EMAIL>");
            message.setSubject("測試郵件");
            message.setText("這是一封測試郵件，驗證碼：123456\n\n驗證碼5分鐘內有效，請及時使用。");
            
            // 發送郵件
            System.out.println("正在發送郵件到 <EMAIL>...");
            mailSender.send(message);
            System.out.println("郵件發送成功！");
            
        } catch (Exception e) {
            System.err.println("郵件發送失敗：" + e.getMessage());
            e.printStackTrace();
        }
    }
}
