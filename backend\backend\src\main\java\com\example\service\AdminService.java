package com.example.service;

import com.example.entity.Admin;
import com.example.repository.AdminRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
@Slf4j
public class AdminService implements UserDetailsService {
    
    @Autowired
    private AdminRepository adminRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        return adminRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("管理員不存在: " + username));
    }
    
    /**
     * 根據用戶名查找管理員
     */
    public Optional<Admin> findByUsername(String username) {
        return adminRepository.findByUsername(username);
    }
    
    /**
     * 根據郵箱查找管理員
     */
    public Optional<Admin> findByEmail(String email) {
        return adminRepository.findByEmail(email);
    }
    
    /**
     * 創建管理員
     */
    @Transactional
    public Admin createAdmin(String username, String email, String password, String realName) {
        // 檢查用戶名是否已存在
        if (adminRepository.existsByUsername(username)) {
            throw new RuntimeException("管理員用戶名已存在");
        }
        
        // 檢查郵箱是否已存在
        if (adminRepository.existsByEmail(email)) {
            throw new RuntimeException("管理員郵箱已被使用");
        }
        
        Admin admin = new Admin();
        admin.setUsername(username);
        admin.setEmail(email);
        admin.setPassword(passwordEncoder.encode(password));
        admin.setRealName(realName);
        admin.setEnabled(true);
        admin.setRole(Admin.Role.ADMIN);
        
        Admin savedAdmin = adminRepository.save(admin);
        log.info("管理員創建成功: {}", savedAdmin.getUsername());
        
        return savedAdmin;
    }
    
    /**
     * 更新管理員信息
     */
    @Transactional
    public Admin updateAdmin(Admin admin) {
        return adminRepository.save(admin);
    }
    
    /**
     * 更新最後登入時間
     */
    @Transactional
    public void updateLastLoginTime(String username) {
        Optional<Admin> adminOpt = adminRepository.findByUsername(username);
        if (adminOpt.isPresent()) {
            Admin admin = adminOpt.get();
            admin.setLastLoginAt(LocalDateTime.now());
            adminRepository.save(admin);
            log.info("更新管理員最後登入時間: {}", username);
        }
    }
    
    /**
     * 重置管理員密碼
     */
    @Transactional
    public void resetPassword(Long adminId, String newPassword) {
        Optional<Admin> adminOpt = adminRepository.findById(adminId);
        if (adminOpt.isPresent()) {
            Admin admin = adminOpt.get();
            admin.setPassword(passwordEncoder.encode(newPassword));
            adminRepository.save(admin);
            log.info("管理員密碼重置成功: {}", admin.getUsername());
        } else {
            throw new RuntimeException("管理員不存在");
        }
    }
}
