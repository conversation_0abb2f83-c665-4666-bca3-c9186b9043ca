const BASE_URL = 'http://localhost:8080';

// 測試用戶登入並獲取token
async function login() {
  try {
    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'how',
        password: 'howhowhowtogo'
      })
    });
    
    const result = await response.json();
    console.log('登入結果:', result);
    
    if (result.success && result.data && result.data.accessToken) {
      return result.data.accessToken;
    }
    
    throw new Error('登入失敗');
  } catch (error) {
    console.error('登入錯誤:', error);
    return null;
  }
}

// 測試獲取訂單詳情
async function testGetOrderDetail(token, orderId) {
  try {
    console.log(`\n=== 測試獲取訂單詳情 (ID: ${orderId}) ===`);
    
    const response = await fetch(`${BASE_URL}/api/orders/${orderId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('狀態碼:', response.status);
    
    const result = await response.json();
    console.log('響應結果:', JSON.stringify(result, null, 2));
    
    if (result.success && result.data) {
      console.log('✅ 訂單詳情獲取成功');
      console.log('訂單號:', result.data.orderNumber);
      console.log('總金額:', result.data.totalAmount);
      console.log('狀態:', result.data.status);
      console.log('訂單項目數量:', result.data.orderItems ? result.data.orderItems.length : 0);
      console.log('支付記錄:', result.data.payment ? '存在' : '不存在');
      return result.data;
    } else {
      console.log('❌ 訂單詳情獲取失敗:', result.message);
      return null;
    }
  } catch (error) {
    console.error('❌ 請求錯誤:', error);
    return null;
  }
}

// 測試獲取訂單列表
async function testGetOrderList(token) {
  try {
    console.log('\n=== 測試獲取訂單列表 ===');
    
    const response = await fetch(`${BASE_URL}/api/orders?page=0&size=10`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('狀態碼:', response.status);
    
    const result = await response.json();
    console.log('響應結果:', JSON.stringify(result, null, 2));
    
    if (result.success && result.data) {
      console.log('✅ 訂單列表獲取成功');
      console.log('總數量:', result.data.totalElements);
      console.log('當前頁訂單數:', result.data.content.length);
      return result.data.content;
    } else {
      console.log('❌ 訂單列表獲取失敗:', result.message);
      return [];
    }
  } catch (error) {
    console.error('❌ 請求錯誤:', error);
    return [];
  }
}

// 主測試函數
async function main() {
  console.log('開始測試訂單API...\n');
  
  // 1. 登入獲取token
  const token = await login();
  if (!token) {
    console.log('❌ 無法獲取token，測試終止');
    return;
  }
  
  console.log('✅ 登入成功，token已獲取');
  
  // 2. 測試獲取訂單列表
  const orders = await testGetOrderList(token);
  
  // 3. 測試獲取具體訂單詳情
  if (orders.length > 0) {
    const firstOrder = orders[0];
    await testGetOrderDetail(token, firstOrder.id);
  } else {
    // 如果沒有訂單，測試已知的訂單ID
    await testGetOrderDetail(token, 2);
  }
  
  console.log('\n測試完成！');
}

// 執行測試
main().catch(console.error);
