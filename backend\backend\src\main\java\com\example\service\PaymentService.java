package com.example.service;

import com.alipay.api.AlipayClient;
import com.alipay.api.domain.AlipayTradePagePayModel;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.example.config.AlipayConfig;
import com.example.dto.ApiResponse;
import com.example.entity.Order;
import com.example.entity.Payment;
import com.example.repository.OrderRepository;
import com.example.repository.PaymentRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

/**
 * 支付服務類
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Slf4j
@Service
@Transactional
public class PaymentService {
    
    @Autowired
    private AlipayClient alipayClient;
    
    @Autowired
    private AlipayConfig alipayConfig;
    
    @Autowired
    private PaymentRepository paymentRepository;
    
    @Autowired
    private OrderRepository orderRepository;
    
    /**
     * 發起支付寶支付
     */
    public ApiResponse<String> createAlipayPayment(Long userId, Long orderId) {
        try {
            // 檢查訂單
            Optional<Order> orderOpt = orderRepository.findById(orderId);
            if (orderOpt.isEmpty()) {
                return ApiResponse.error("訂單不存在");
            }
            
            Order order = orderOpt.get();
            if (!order.getUserId().equals(userId)) {
                return ApiResponse.error("無權限操作此訂單");
            }
            
            if (order.getStatus() != Order.Status.PENDING_PAYMENT) {
                return ApiResponse.error("訂單狀態不正確");
            }
            
            // 檢查支付記錄
            Optional<Payment> paymentOpt = paymentRepository.findByOrderId(orderId);
            if (paymentOpt.isEmpty()) {
                return ApiResponse.error("支付記錄不存在");
            }
            
            Payment payment = paymentOpt.get();
            if (payment.getPaymentStatus() == Payment.PaymentStatus.SUCCESS) {
                return ApiResponse.error("訂單已支付");
            }
            
            // 創建支付寶支付請求
            AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
            request.setNotifyUrl(alipayConfig.getNotifyUrl());
            request.setReturnUrl(alipayConfig.getReturnUrl());
            
            // 設置業務參數
            AlipayTradePagePayModel model = new AlipayTradePagePayModel();
            model.setOutTradeNo(order.getOrderNumber());  // 商戶訂單號
            model.setTotalAmount(order.getTotalAmount().toString());  // 支付金額
            model.setSubject("商品訂單-" + order.getOrderNumber());  // 訂單標題
            model.setProductCode("FAST_INSTANT_TRADE_PAY");  // 產品碼
            model.setTimeoutExpress("10m");  // 超時時間
            
            request.setBizModel(model);
            
            // 調用支付寶API
            AlipayTradePagePayResponse response = alipayClient.pageExecute(request);
            
            if (response.isSuccess()) {
                log.info("支付寶支付請求成功: orderNumber={}", order.getOrderNumber());
                return ApiResponse.success(response.getBody());
            } else {
                log.error("支付寶支付請求失敗: orderNumber={}, error={}", order.getOrderNumber(), response.getMsg());
                return ApiResponse.error("發起支付失敗: " + response.getMsg());
            }
            
        } catch (Exception e) {
            log.error("創建支付寶支付失敗: userId={}, orderId={}", userId, orderId, e);
            return ApiResponse.error("發起支付失敗: " + e.getMessage());
        }
    }
    
    /**
     * 處理支付寶回調
     */
    public ApiResponse<String> handleAlipayCallback(Map<String, String> params) {
        try {
            log.info("收到支付寶回調: {}", params);

            // 驗證回調參數完整性
            if (!validateCallbackParams(params)) {
                log.error("支付寶回調參數不完整: {}", params);
                return ApiResponse.error("回調參數不完整");
            }

            // 獲取關鍵參數
            String tradeStatus = params.get("trade_status");
            String outTradeNo = params.get("out_trade_no");  // 商戶訂單號
            String tradeNo = params.get("trade_no");  // 支付寶交易號
            String buyerLogonId = params.get("buyer_logon_id");  // 買家支付寶賬號
            String gmtPayment = params.get("gmt_payment");  // 支付時間
            String totalAmount = params.get("total_amount");  // 支付金額

            // 驗證支付狀態
            if (!"TRADE_SUCCESS".equals(tradeStatus) && !"TRADE_FINISHED".equals(tradeStatus)) {
                log.warn("支付狀態不是成功: tradeStatus={}, outTradeNo={}", tradeStatus, outTradeNo);
                return handleFailedPayment(outTradeNo, tradeStatus);
            }
            
            // 查找訂單
            Optional<Order> orderOpt = orderRepository.findByOrderNumber(outTradeNo);
            if (orderOpt.isEmpty()) {
                log.error("訂單不存在: orderNumber={}", outTradeNo);
                return ApiResponse.error("訂單不存在");
            }
            
            Order order = orderOpt.get();
            
            // 檢查訂單狀態
            if (order.getStatus() != Order.Status.PENDING_PAYMENT) {
                log.warn("訂單狀態不正確: orderNumber={}, status={}", outTradeNo, order.getStatus());
                return ApiResponse.success("success");  // 已處理過，返回成功
            }
            
            // 查找支付記錄
            Optional<Payment> paymentOpt = paymentRepository.findByOrderId(order.getId());
            if (paymentOpt.isEmpty()) {
                log.error("支付記錄不存在: orderId={}", order.getId());
                return ApiResponse.error("支付記錄不存在");
            }
            
            Payment payment = paymentOpt.get();
            
            // 更新支付記錄
            payment.markPaymentSuccess(tradeNo, buyerLogonId, params.toString());
            paymentRepository.save(payment);
            
            // 更新訂單狀態
            order.setStatus(Order.Status.PAID);
            order.setPaidAt(LocalDateTime.now());
            order.setPaidAmount(order.getTotalAmount());
            orderRepository.save(order);
            
            log.info("支付成功處理完成: orderNumber={}, tradeNo={}", outTradeNo, tradeNo);
            return ApiResponse.success("success");
            
        } catch (Exception e) {
            log.error("處理支付寶回調失敗", e);
            return ApiResponse.error("處理回調失敗: " + e.getMessage());
        }
    }
    
    /**
     * 查詢支付狀態
     */
    public ApiResponse<Payment> getPaymentStatus(Long userId, Long orderId) {
        try {
            // 檢查訂單
            Optional<Order> orderOpt = orderRepository.findById(orderId);
            if (orderOpt.isEmpty()) {
                return ApiResponse.error("訂單不存在");
            }
            
            Order order = orderOpt.get();
            if (!order.getUserId().equals(userId)) {
                return ApiResponse.error("無權限查看此訂單");
            }
            
            // 查找支付記錄
            Optional<Payment> paymentOpt = paymentRepository.findByOrderIdWithOrder(orderId);
            if (paymentOpt.isEmpty()) {
                return ApiResponse.error("支付記錄不存在");
            }
            
            return ApiResponse.success(paymentOpt.get());
        } catch (Exception e) {
            log.error("查詢支付狀態失敗: userId={}, orderId={}", userId, orderId, e);
            return ApiResponse.error("查詢支付狀態失敗: " + e.getMessage());
        }
    }
    
    /**
     * 確認訂單支付（管理員功能）
     */
    public ApiResponse<String> confirmOrderPayment(String orderNumber, String paymentTime) {
        try {
            // 查找訂單
            Optional<Order> orderOpt = orderRepository.findByOrderNumber(orderNumber);
            if (orderOpt.isEmpty()) {
                return ApiResponse.error("訂單不存在");
            }
            
            Order order = orderOpt.get();
            
            // 查找支付記錄
            Optional<Payment> paymentOpt = paymentRepository.findByOrderId(order.getId());
            if (paymentOpt.isEmpty()) {
                return ApiResponse.error("支付記錄不存在");
            }
            
            Payment payment = paymentOpt.get();
            
            // 更新支付記錄
            payment.setPaymentStatus(Payment.PaymentStatus.SUCCESS);
            payment.setPaidAt(LocalDateTime.now());
            payment.setCallbackAt(LocalDateTime.now());
            paymentRepository.save(payment);
            
            // 更新訂單狀態
            order.setStatus(Order.Status.PAID);
            order.setPaidAt(LocalDateTime.now());
            order.setPaidAmount(order.getTotalAmount());
            orderRepository.save(order);
            
            log.info("管理員確認訂單支付成功: orderNumber={}", orderNumber);
            return ApiResponse.success("訂單支付確認成功");

        } catch (Exception e) {
            log.error("確認訂單支付失敗: orderNumber={}", orderNumber, e);
            return ApiResponse.error("確認支付失敗: " + e.getMessage());
        }
    }

    /**
     * 驗證支付寶回調參數完整性
     */
    private boolean validateCallbackParams(Map<String, String> params) {
        // 檢查必要參數是否存在
        String[] requiredParams = {
            "trade_status", "out_trade_no", "trade_no",
            "total_amount", "gmt_payment"
        };

        for (String param : requiredParams) {
            if (!params.containsKey(param) || params.get(param) == null || params.get(param).trim().isEmpty()) {
                log.error("缺少必要參數: {}", param);
                return false;
            }
        }

        // 驗證金額格式
        try {
            String totalAmount = params.get("total_amount");
            Double.parseDouble(totalAmount);
        } catch (NumberFormatException e) {
            log.error("金額格式不正確: {}", params.get("total_amount"));
            return false;
        }

        return true;
    }

    /**
     * 處理支付失敗的情況
     */
    private ApiResponse<String> handleFailedPayment(String outTradeNo, String tradeStatus) {
        try {
            // 查找訂單
            Optional<Order> orderOpt = orderRepository.findByOrderNumber(outTradeNo);
            if (orderOpt.isEmpty()) {
                log.error("處理失敗支付時訂單不存在: orderNumber={}", outTradeNo);
                return ApiResponse.error("訂單不存在");
            }

            Order order = orderOpt.get();

            // 根據支付狀態處理
            switch (tradeStatus) {
                case "TRADE_CLOSED":
                    // 交易關閉，取消訂單
                    order.setStatus(Order.Status.CANCELLED);
                    orderRepository.save(order);

                    // 更新支付記錄
                    Optional<Payment> paymentOpt = paymentRepository.findByOrderId(order.getId());
                    if (paymentOpt.isPresent()) {
                        Payment payment = paymentOpt.get();
                        payment.setPaymentStatus(Payment.PaymentStatus.FAILED);
                        payment.setCallbackAt(LocalDateTime.now());
                        paymentRepository.save(payment);
                    }

                    log.info("支付關閉，訂單已取消: orderNumber={}", outTradeNo);
                    break;

                case "WAIT_BUYER_PAY":
                    // 等待買家付款，不做處理
                    log.info("等待買家付款: orderNumber={}", outTradeNo);
                    break;

                default:
                    log.warn("未知的支付狀態: tradeStatus={}, orderNumber={}", tradeStatus, outTradeNo);
                    break;
            }

            return ApiResponse.success("success");
        } catch (Exception e) {
            log.error("處理失敗支付異常: orderNumber={}, tradeStatus={}", outTradeNo, tradeStatus, e);
            return ApiResponse.error("處理失敗");
        }
    }

    /**
     * 支付回調重試機制
     */
    public ApiResponse<String> retryPaymentCallback(String orderNumber) {
        try {
            log.info("重試支付回調: orderNumber={}", orderNumber);

            // 查找訂單
            Optional<Order> orderOpt = orderRepository.findByOrderNumber(orderNumber);
            if (orderOpt.isEmpty()) {
                return ApiResponse.error("訂單不存在");
            }

            Order order = orderOpt.get();

            // 檢查訂單狀態
            if (order.getStatus() != Order.Status.PENDING_PAYMENT) {
                return ApiResponse.success("訂單狀態已更新，無需重試");
            }

            // 查詢支付寶交易狀態
            // 這裡可以調用支付寶的查詢接口來確認支付狀態
            // 暫時模擬處理
            log.info("查詢支付寶交易狀態: orderNumber={}", orderNumber);

            return ApiResponse.success("重試完成");
        } catch (Exception e) {
            log.error("重試支付回調失敗: orderNumber={}", orderNumber, e);
            return ApiResponse.error("重試失敗: " + e.getMessage());
        }
    }
}
