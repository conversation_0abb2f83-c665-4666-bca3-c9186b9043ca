<template>
  <div class="follow-page">
    <el-container>
      <el-header>
        <h1>社交關注</h1>
        <div class="stats">
          <el-statistic title="關注數" :value="stats.followingCount" />
          <el-statistic title="粉絲數" :value="stats.followersCount" />
        </div>
      </el-header>
      
      <el-main>
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <!-- 推薦用戶 -->
          <el-tab-pane label="推薦用戶" name="recommend">
            <div class="tab-content">
              <el-button @click="loadRecommendedUsers" :loading="loading.recommend">
                刷新推薦
              </el-button>
              <UserList 
                :users="recommendedUsers" 
                @followChanged="handleFollowChanged"
              />
            </div>
          </el-tab-pane>
          
          <!-- 我的關注 -->
          <el-tab-pane label="我的關注" name="following">
            <div class="tab-content">
              <UserList 
                :users="followingList" 
                :totalCount="followingTotal"
                :currentPage="followingPage"
                :showPagination="true"
                @pageChange="handleFollowingPageChange"
                @followChanged="handleFollowChanged"
              />
            </div>
          </el-tab-pane>
          
          <!-- 我的粉絲 -->
          <el-tab-pane label="我的粉絲" name="followers">
            <div class="tab-content">
              <UserList 
                :users="followersList" 
                :totalCount="followersTotal"
                :currentPage="followersPage"
                :showPagination="true"
                @pageChange="handleFollowersPageChange"
                @followChanged="handleFollowChanged"
              />
            </div>
          </el-tab-pane>
          
          <!-- 共同關注 -->
          <el-tab-pane label="共同關注" name="mutual">
            <div class="tab-content">
              <el-form inline>
                <el-form-item label="選擇用戶:">
                  <el-select
                    v-model="selectedUserId"
                    placeholder="請選擇用戶"
                    @change="handleUserSelect"
                    :loading="loading.following"
                    clearable
                    filterable
                  >
                    <el-option
                      v-for="user in followingList"
                      :key="user.id"
                      :label="user.username"
                      :value="user.id"
                    >
                      <span>{{ user.username }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-form>
              
              <UserList 
                v-if="selectedUserId"
                :users="mutualFollows" 
                :totalCount="mutualTotal"
                :currentPage="mutualPage"
                :showPagination="true"
                @pageChange="handleMutualPageChange"
                @followChanged="handleFollowChanged"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { followAPI } from '../api'
import { ElMessage } from 'element-plus'
import UserList from '../components/UserList.vue'

const activeTab = ref('recommend')
const selectedUserId = ref<number>()

// 計算屬性：獲取選中用戶的名稱
const selectedUserName = computed(() => {
  if (!selectedUserId.value) return ''
  const user = followingList.value.find(u => u.id === selectedUserId.value)
  return user ? user.username : ''
})

// 統計數據
const stats = reactive({
  followingCount: 0,
  followersCount: 0
})

// 加載狀態
const loading = reactive({
  recommend: false,
  following: false,
  followers: false,
  mutual: false,
  stats: false
})

// 推薦用戶
const recommendedUsers = ref([])

// 關注列表
const followingList = ref([])
const followingTotal = ref(0)
const followingPage = ref(1)

// 粉絲列表
const followersList = ref([])
const followersTotal = ref(0)
const followersPage = ref(1)

// 共同關注
const mutualFollows = ref([])
const mutualTotal = ref(0)
const mutualPage = ref(1)

// 加載統計數據
const loadStats = async () => {
  loading.stats = true
  try {
    const response = await followAPI.getFollowStats()
    if (response.success) {
      stats.followingCount = response.data?.followingCount || 0
      stats.followersCount = response.data?.followersCount || 0
    }
  } catch (error) {
    console.error('加載統計數據失敗:', error)
  } finally {
    loading.stats = false
  }
}

// 加載推薦用戶
const loadRecommendedUsers = async () => {
  loading.recommend = true
  try {
    const response = await followAPI.getRecommendedUsers(10)
    if (response.success) {
      recommendedUsers.value = response.data || []
    }
  } catch (error) {
    console.error('加載推薦用戶失敗:', error)
    ElMessage.error('加載推薦用戶失敗')
  } finally {
    loading.recommend = false
  }
}

// 加載關注列表
const loadFollowingList = async (page: number = 1) => {
  loading.following = true
  try {
    const response = await followAPI.getFollowingList(page - 1, 20)

    if (response.success) {
      followingList.value = response.data?.data || []
      followingTotal.value = response.data?.totalCount || 0
      followingPage.value = page
    } else {
      ElMessage.error(response.message || '加載關注列表失敗')
    }
  } catch (error) {
    console.error('加載關注列表失敗:', error)
    ElMessage.error('加載關注列表失敗')
  } finally {
    loading.following = false
  }
}

// 加載粉絲列表
const loadFollowersList = async (page: number = 1) => {
  loading.followers = true
  try {
    const response = await followAPI.getFollowersList(page - 1, 20)
    if (response.success) {
      followersList.value = response.data?.data || []
      followersTotal.value = response.data?.totalCount || 0
      followersPage.value = page
    }
  } catch (error) {
    console.error('加載粉絲列表失敗:', error)
    ElMessage.error('加載粉絲列表失敗')
  } finally {
    loading.followers = false
  }
}

// 加載共同關注
const loadMutualFollows = async (page: number = 1) => {
  if (!selectedUserId.value) return

  loading.mutual = true
  try {
    const response = await followAPI.getMutualFollows(selectedUserId.value, page - 1, 20)

    if (response.success) {
      mutualFollows.value = response.data?.data || []
      mutualTotal.value = response.data?.count || 0
      mutualPage.value = page
    } else {
      ElMessage.error(response.message || '加載共同關注失敗')
    }
  } catch (error) {
    console.error('加載共同關注失敗:', error)
    ElMessage.error('加載共同關注失敗')
  } finally {
    loading.mutual = false
  }
}

// 標籤切換處理
const handleTabChange = (tabName: string) => {
  switch (tabName) {
    case 'recommend':
      if (recommendedUsers.value.length === 0) {
        loadRecommendedUsers()
      }
      break
    case 'following':
      if (followingList.value.length === 0) {
        loadFollowingList()
      }
      break
    case 'followers':
      if (followersList.value.length === 0) {
        loadFollowersList()
      }
      break
    case 'mutual':
      // 切換到共同關注標籤時，確保關注列表已加載（用於下拉選擇）
      if (followingList.value.length === 0) {
        loadFollowingList()
      }
      // 清空之前的共同關注數據和選擇
      selectedUserId.value = undefined
      mutualFollows.value = []
      mutualTotal.value = 0
      mutualPage.value = 1
      break
  }
}

// 分頁處理
const handleFollowingPageChange = (page: number) => {
  loadFollowingList(page)
}

const handleFollowersPageChange = (page: number) => {
  loadFollowersList(page)
}

const handleMutualPageChange = (page: number) => {
  loadMutualFollows(page)
}

// 處理用戶選擇
const handleUserSelect = (userId: number) => {
  if (userId) {
    // 重置分頁和數據
    mutualFollows.value = []
    mutualTotal.value = 0
    mutualPage.value = 1
    // 加載共同關注
    loadMutualFollows(1)
  }
}

// 關注狀態變化處理
const handleFollowChanged = (userId: number, isFollowing: boolean) => {
  // 更新統計數據
  loadStats()
  
  // 如果當前在關注列表頁面，重新加載
  if (activeTab.value === 'following') {
    loadFollowingList(followingPage.value)
  }
}

onMounted(() => {
  loadStats()
  loadRecommendedUsers()
})
</script>

<style scoped>
.follow-page {
  padding: 20px;
}

.el-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.stats {
  display: flex;
  gap: 40px;
}

.tab-content {
  padding: 20px 0;
}

.el-form {
  margin-bottom: 20px;
}
</style>
