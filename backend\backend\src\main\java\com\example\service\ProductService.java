package com.example.service;

import com.example.dto.ApiResponse;
import com.example.dto.PagedResponse;
import com.example.entity.Product;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品服務接口
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
public interface ProductService {
    
    /**
     * 分頁查詢商品（帶緩存）
     */
    ApiResponse<PagedResponse<Product>> getProducts(Pageable pageable);
    
    /**
     * 根據分類查詢商品（帶緩存）
     */
    ApiResponse<PagedResponse<Product>> getProductsByCategory(Long categoryId, Pageable pageable);
    
    /**
     * 多條件查詢商品
     */
    ApiResponse<PagedResponse<Product>> searchProducts(
            String name, Long categoryId, Integer status,
            BigDecimal minPrice, BigDecimal maxPrice, String brand,
            Integer isRecommended, Integer isHot, Pageable pageable);
    
    /**
     * 關鍵詞搜索商品（帶緩存）
     */
    ApiResponse<PagedResponse<Product>> searchProductsByKeyword(String keyword, Pageable pageable);
    
    /**
     * 獲取商品詳情（帶緩存）
     */
    ApiResponse<Product> getProductById(Long id);
    
    /**
     * 獲取推薦商品（帶緩存）
     */
    ApiResponse<PagedResponse<Product>> getRecommendedProducts(Pageable pageable);
    
    /**
     * 獲取熱門商品（帶緩存）
     */
    ApiResponse<PagedResponse<Product>> getHotProducts(Pageable pageable);
    
    /**
     * 獲取最新商品
     */
    ApiResponse<PagedResponse<Product>> getLatestProducts(Pageable pageable);
    
    /**
     * 根據銷量排序查詢商品
     */
    ApiResponse<PagedResponse<Product>> getProductsBySoldCount(Pageable pageable);
    
    /**
     * 根據價格排序查詢商品
     */
    ApiResponse<PagedResponse<Product>> getProductsByPrice(boolean ascending, Pageable pageable);
    
    /**
     * 獲取相關商品（同分類）
     */
    ApiResponse<List<Product>> getRelatedProducts(Long productId, int limit);
    
    /**
     * 創建商品
     * 實現緩存清理策略
     */
    ApiResponse<Product> createProduct(Product product, Long createdBy);
    
    /**
     * 更新商品
     * 實現緩存一致性處理
     */
    ApiResponse<Product> updateProduct(Long id, Product product, Long updatedBy);
    
    /**
     * 刪除商品（軟刪除）
     */
    ApiResponse<String> deleteProduct(Long id, Long deletedBy);
    
    /**
     * 批量刪除商品
     */
    ApiResponse<String> batchDeleteProducts(List<Long> ids, Long deletedBy);
    
    /**
     * 上架/下架商品
     */
    ApiResponse<String> toggleProductStatus(Long id, Integer status, Long updatedBy);
    
    /**
     * 批量更新商品狀態
     */
    ApiResponse<String> batchUpdateProductStatus(List<Long> ids, Integer status, Long updatedBy);
    
    /**
     * 設置推薦商品
     */
    ApiResponse<String> setRecommended(Long id, Integer isRecommended, Long updatedBy);
    
    /**
     * 設置熱門商品
     */
    ApiResponse<String> setHot(Long id, Integer isHot, Long updatedBy);
    
    /**
     * 更新商品排序
     */
    ApiResponse<String> updateProductSort(Long id, Integer sortOrder, Long updatedBy);
    
    /**
     * 上傳商品圖片
     */
    ApiResponse<String> uploadProductImage(Long productId, MultipartFile file, 
                                          Integer sortOrder, Integer isMain, Long uploadedBy);
    
    /**
     * 批量上傳商品圖片
     */
    ApiResponse<List<String>> batchUploadProductImages(Long productId, List<MultipartFile> files, Long uploadedBy);
    
    /**
     * 刪除商品圖片
     */
    ApiResponse<String> deleteProductImage(Long productId, Long imageId, Long deletedBy);
    
    /**
     * 設置商品主圖
     */
    ApiResponse<String> setMainImage(Long productId, Long imageId, Long updatedBy);
    
    /**
     * 更新圖片排序
     */
    ApiResponse<String> updateImageSort(Long imageId, Integer sortOrder, Long updatedBy);
    
    /**
     * 庫存管理
     */
    
    /**
     * 減少庫存（下單時使用）
     */
    ApiResponse<String> decreaseStock(Long productId, Integer quantity);
    
    /**
     * 增加庫存（退貨時使用）
     */
    ApiResponse<String> increaseStock(Long productId, Integer quantity);
    
    /**
     * 更新庫存
     */
    ApiResponse<String> updateStock(Long productId, Integer stock, Long updatedBy);
    
    /**
     * 增加銷量
     */
    ApiResponse<String> increaseSoldCount(Long productId, Integer quantity);
    
    /**
     * 查詢庫存不足商品
     */
    ApiResponse<List<Product>> getLowStockProducts(Integer threshold);
    
    /**
     * 統計功能
     */
    
    /**
     * 獲取商品統計信息
     */
    ApiResponse<Object> getProductStatistics();
    
    /**
     * 統計各狀態商品數量
     */
    ApiResponse<Object> getProductStatusStatistics();
    
    /**
     * 統計分類下商品數量
     */
    ApiResponse<Long> getProductCountByCategory(Long categoryId);
    
    /**
     * 增加商品瀏覽次數
     */
    ApiResponse<String> increaseViewCount(Long productId);
    
    /**
     * 獲取商品瀏覽次數
     */
    ApiResponse<Long> getViewCount(Long productId);
    
    /**
     * 緩存管理
     */
    
    /**
     * 刷新商品緩存
     */
    ApiResponse<String> refreshProductCache(Long productId);
    
    /**
     * 清除所有商品緩存
     */
    ApiResponse<String> clearAllProductCache();
    
    /**
     * 預熱熱門商品緩存
     */
    ApiResponse<String> warmupHotProductsCache();
    
    /**
     * 檢查商品名稱是否存在
     */
    ApiResponse<Boolean> checkProductNameExists(String name, Long excludeId);
}
