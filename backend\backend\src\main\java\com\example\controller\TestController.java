package com.example.controller;

import com.example.dto.ApiResponse;
import com.example.service.EmailService;
import com.example.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
@CrossOrigin(origins = "*")
@Slf4j
public class TestController {

    @Autowired
    private EmailService emailService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 根路徑測試
     */
    @GetMapping
    public ApiResponse<String> test() {
        return ApiResponse.success("測試 API 正常運行");
    }

    /**
     * 測試郵件發送功能
     */
    @PostMapping("/send-email")
    public ApiResponse<String> testSendEmail(@RequestParam String email) {
        try {
            log.info("開始測試發送郵件到: {}", email);
            
            boolean success = emailService.sendVerificationCode(email, EmailService.Type.REGISTRATION);
            
            if (success) {
                String stats = emailService.getEmailSendStats(email);
                log.info("郵件發送成功: {}", email);
                return ApiResponse.success("郵件發送成功！" + stats);
            } else {
                log.error("郵件發送失敗: {}", email);
                return ApiResponse.error("郵件發送失敗");
            }
            
        } catch (Exception e) {
            log.error("郵件發送異常: {}", email, e);
            return ApiResponse.error("郵件發送失敗: " + e.getMessage());
        }
    }
    
    /**
     * 測試系統狀態
     */
    @GetMapping("/status")
    public ApiResponse<String> testStatus() {
        return ApiResponse.success("系統運行正常");
    }
    
    /**
     * 測試 Redis 連接
     */ 
    @GetMapping("/redis")
    public ApiResponse<String> testRedis() {
        try {
            // 這裡可以添加 Redis 測試邏輯
            return ApiResponse.success("Redis 連接正常");
        } catch (Exception e) {
            return ApiResponse.error("Redis 連接失敗: " + e.getMessage());
        }
    }
}
