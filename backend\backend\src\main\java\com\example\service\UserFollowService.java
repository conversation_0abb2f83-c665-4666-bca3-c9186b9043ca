package com.example.service;

import com.example.entity.User;
import com.example.entity.UserFollow;
import com.example.repository.UserFollowRepository;
import com.example.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserFollowService {
    
    @Autowired
    private UserFollowRepository userFollowRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private RedisService redisService;
    
    /**
     * 關注用戶
     */
    @Transactional
    public boolean followUser(Long followerId, Long followingId) {
        // 檢查參數有效性
        if (followerId.equals(followingId)) {
            throw new RuntimeException("不能關注自己");
        }
        
        // 檢查被關注用戶是否存在
        if (!userRepository.existsById(followingId)) {
            throw new RuntimeException("被關注的用戶不存在");
        }
        
        // 檢查是否已經關注
        if (userFollowRepository.existsByFollowerIdAndFollowingId(followerId, followingId)) {
            return false; // 已經關注了
        }
        
        try {
            // 保存到數據庫
            UserFollow userFollow = new UserFollow(followerId, followingId);
            userFollowRepository.save(userFollow);
            
            // 更新Redis緩存
            redisService.addFollowing(followerId, followingId);
            
            log.info("用戶關注成功: follower={}, following={}", followerId, followingId);
            return true;
        } catch (Exception e) {
            log.error("關注用戶失敗: follower={}, following={}", followerId, followingId, e);
            throw new RuntimeException("關注失敗");
        }
    }
    
    /**
     * 取消關注用戶
     */
    @Transactional
    public boolean unfollowUser(Long followerId, Long followingId) {
        try {
            // 從數據庫刪除
            userFollowRepository.deleteByFollowerIdAndFollowingId(followerId, followingId);
            
            // 更新Redis緩存
            redisService.removeFollowing(followerId, followingId);
            
            log.info("取消關注成功: follower={}, following={}", followerId, followingId);
            return true;
        } catch (Exception e) {
            log.error("取消關注失敗: follower={}, following={}", followerId, followingId, e);
            throw new RuntimeException("取消關注失敗");
        }
    }
    
    /**
     * 檢查是否已關注
     */
    public boolean isFollowing(Long followerId, Long followingId) {
        // 優先從Redis查詢
        if (redisService.isFollowing(followerId, followingId)) {
            return true;
        }
        
        // Redis中沒有，從數據庫查詢並同步到Redis
        boolean exists = userFollowRepository.existsByFollowerIdAndFollowingId(followerId, followingId);
        if (exists) {
            redisService.addFollowing(followerId, followingId);
        }
        return exists;
    }
    
    /**
     * 獲取用戶關注列表
     */
    public List<User> getFollowingList(Long userId, int page, int size) {
        try {
            // 優先從Redis獲取ID列表
            Set<String> followingIds = redisService.getFollowingList(userId);
            
            if (followingIds.isEmpty()) {
                // Redis中沒有數據，從數據庫查詢並同步到Redis
                syncUserFollowDataToRedis(userId);
                followingIds = redisService.getFollowingList(userId);
            }
            
            if (followingIds.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 轉換為Long類型並分頁
            List<Long> userIds = followingIds.stream()
                    .map(Long::valueOf)
                    .skip((long) page * size)
                    .limit(size)
                    .collect(Collectors.toList());
            
            // 查詢用戶信息
            return userRepository.findAllById(userIds);
        } catch (Exception e) {
            log.error("獲取關注列表失敗: userId={}", userId, e);
            // 降級到數據庫查詢
            List<UserFollow> follows = userFollowRepository.findFollowingByFollowerIdWithUser(userId);
            return follows.stream()
                    .skip((long) page * size)
                    .limit(size)
                    .map(UserFollow::getFollowing)
                    .collect(Collectors.toList());
        }
    }
    
    /**
     * 獲取用戶粉絲列表
     */
    public List<User> getFollowersList(Long userId, int page, int size) {
        try {
            // 優先從Redis獲取ID列表
            Set<String> followerIds = redisService.getFollowersList(userId);
            
            if (followerIds.isEmpty()) {
                // Redis中沒有數據，從數據庫查詢並同步到Redis
                syncUserFollowDataToRedis(userId);
                followerIds = redisService.getFollowersList(userId);
            }
            
            if (followerIds.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 轉換為Long類型並分頁
            List<Long> userIds = followerIds.stream()
                    .map(Long::valueOf)
                    .skip((long) page * size)
                    .limit(size)
                    .collect(Collectors.toList());
            
            // 查詢用戶信息
            return userRepository.findAllById(userIds);
        } catch (Exception e) {
            log.error("獲取粉絲列表失敗: userId={}", userId, e);
            // 降級到數據庫查詢
            List<UserFollow> followers = userFollowRepository.findFollowersByFollowingIdWithUser(userId);
            return followers.stream()
                    .skip((long) page * size)
                    .limit(size)
                    .map(UserFollow::getFollower)
                    .collect(Collectors.toList());
        }
    }
    
    /**
     * 獲取關注數量
     */
    public long getFollowingCount(Long userId) {
        long redisCount = redisService.getFollowingCount(userId);
        if (redisCount > 0) {
            return redisCount;
        }
        
        // Redis中沒有數據，從數據庫查詢
        long dbCount = userFollowRepository.countByFollowerId(userId);
        if (dbCount > 0) {
            syncUserFollowDataToRedis(userId);
        }
        return dbCount;
    }
    
    /**
     * 獲取粉絲數量
     */
    public long getFollowersCount(Long userId) {
        long redisCount = redisService.getFollowersCount(userId);
        if (redisCount > 0) {
            return redisCount;
        }
        
        // Redis中沒有數據，從數據庫查詢
        long dbCount = userFollowRepository.countByFollowingId(userId);
        if (dbCount > 0) {
            syncUserFollowDataToRedis(userId);
        }
        return dbCount;
    }
    
    /**
     * 獲取兩個用戶的共同關注
     */
    public List<User> getMutualFollowing(Long userId1, Long userId2, int page, int size) {
        try {
            // 從數據庫查詢共同關注的用戶ID
            List<Long> mutualIds = userFollowRepository.findMutualFollowingIds(userId1, userId2);

            if (mutualIds.isEmpty()) {
                return new ArrayList<>();
            }

            // 分頁處理
            List<Long> pagedIds = mutualIds.stream()
                    .skip((long) page * size)
                    .limit(size)
                    .collect(Collectors.toList());

            // 查詢用戶信息
            return userRepository.findAllById(pagedIds);
        } catch (Exception e) {
            log.error("獲取共同關注失敗: userId1={}, userId2={}", userId1, userId2, e);
            return new ArrayList<>();
        }
    }

    /**
     * 獲取兩個用戶的共同關注數量
     */
    public long getMutualFollowingCount(Long userId1, Long userId2) {
        try {
            List<Long> mutualIds = userFollowRepository.findMutualFollowingIds(userId1, userId2);
            return mutualIds.size();
        } catch (Exception e) {
            log.error("獲取共同關注數量失敗: userId1={}, userId2={}", userId1, userId2, e);
            return 0;
        }
    }

    /**
     * 推薦用戶（基於共同關注的協同過濾算法）
     */
    public List<User> recommendUsers(Long userId, int limit) {
        try {
            // 獲取用戶已關注的用戶ID列表
            List<Long> followingIds = userFollowRepository.findFollowingIdsByFollowerId(userId);
            Set<Long> followingSet = new HashSet<>(followingIds);
            followingSet.add(userId); // 排除自己

            // 基於共同關注的推薦算法
            Map<Long, Integer> recommendScore = new HashMap<>();

            // 遍歷用戶關注的每個人
            for (Long followingId : followingIds) {
                // 獲取這個人的關注列表
                List<Long> theirFollowing = userFollowRepository.findFollowingIdsByFollowerId(followingId);

                // 計算推薦分數
                for (Long candidateId : theirFollowing) {
                    if (!followingSet.contains(candidateId)) {
                        recommendScore.put(candidateId, recommendScore.getOrDefault(candidateId, 0) + 1);
                    }
                }
            }

            // 如果基於關注的推薦不足，添加一些隨機用戶
            if (recommendScore.size() < limit) {
                addRandomUsers(recommendScore, followingSet, limit - recommendScore.size());
            }

            // 按分數排序並取前N個
            List<Long> recommendedIds = recommendScore.entrySet().stream()
                    .sorted(Map.Entry.<Long, Integer>comparingByValue().reversed())
                    .limit(limit)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            // 查詢用戶信息
            return userRepository.findAllById(recommendedIds);
        } catch (Exception e) {
            log.error("推薦用戶失敗: userId={}", userId, e);
            return getRandomUsers(userId, limit);
        }
    }

    /**
     * 添加隨機用戶到推薦列表
     */
    private void addRandomUsers(Map<Long, Integer> recommendScore, Set<Long> excludeIds, int count) {
        try {
            // 簡單的隨機推薦：獲取最近註冊的用戶
            List<User> recentUsers = userRepository.findAll().stream()
                    .filter(user -> !excludeIds.contains(user.getId()))
                    .sorted((u1, u2) -> u2.getCreatedAt().compareTo(u1.getCreatedAt()))
                    .limit(count * 2) // 多取一些以防重複
                    .collect(Collectors.toList());

            // 隨機選擇
            Collections.shuffle(recentUsers);
            recentUsers.stream()
                    .limit(count)
                    .forEach(user -> recommendScore.put(user.getId(), 0));
        } catch (Exception e) {
            log.error("添加隨機用戶失敗", e);
        }
    }

    /**
     * 獲取隨機用戶（降級方案）
     */
    private List<User> getRandomUsers(Long userId, int limit) {
        try {
            List<User> allUsers = userRepository.findAll().stream()
                    .filter(user -> !user.getId().equals(userId))
                    .collect(Collectors.toList());

            Collections.shuffle(allUsers);
            return allUsers.stream().limit(limit).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("獲取隨機用戶失敗", e);
            return new ArrayList<>();
        }
    }

    /**
     * 同步用戶關注數據到Redis
     */
    private void syncUserFollowDataToRedis(Long userId) {
        try {
            // 獲取關注列表
            List<Long> followingIds = userFollowRepository.findFollowingIdsByFollowerId(userId);
            Set<String> followingIdStrings = followingIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toSet());

            // 獲取粉絲列表
            List<Long> followerIds = userFollowRepository.findFollowerIdsByFollowingId(userId);
            Set<String> followerIdStrings = followerIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toSet());

            // 同步到Redis
            redisService.initUserFollowData(userId, followingIdStrings, followerIdStrings);

            log.info("同步用戶關注數據到Redis成功: userId={}, following={}, followers={}",
                    userId, followingIds.size(), followerIds.size());
        } catch (Exception e) {
            log.error("同步用戶關注數據到Redis失敗: userId={}", userId, e);
        }
    }
}
