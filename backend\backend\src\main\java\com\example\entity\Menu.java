package com.example.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

/**
 * 菜單實體類
 * 支持多級菜單結構，使用自關聯實現父子關係
 */
@Entity
@Table(name = "menus")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Menu {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 菜單名稱
     */
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    /**
     * 菜單路徑/URL
     */
    @Column(name = "path", length = 200)
    private String path;
    
    /**
     * 菜單圖標
     */
    @Column(name = "icon", length = 50)
    private String icon;
    
    /**
     * 菜單描述
     */
    @Column(name = "description", length = 500)
    private String description;
    
    /**
     * 排序順序
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    /**
     * 是否啟用
     */
    @Column(name = "enabled")
    private Boolean enabled = true;
    
    /**
     * 菜單類型：MENU(菜單), BUTTON(按鈕), LINK(外部鏈接)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "menu_type")
    private MenuType menuType = MenuType.MENU;
    
    /**
     * 權限標識
     */
    @Column(name = "permission", length = 100)
    private String permission;
    
    /**
     * 父菜單ID
     */
    @Column(name = "parent_id")
    private Long parentId;
    
    /**
     * 父菜單（自關聯）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", insertable = false, updatable = false)
    @JsonIgnore
    private Menu parent;
    
    /**
     * 子菜單列表（自關聯）
     */
    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("sortOrder ASC")
    @JsonIgnore
    private List<Menu> children = new ArrayList<>();
    
    /**
     * 創建時間
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    /**
     * 更新時間
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 菜單類型枚舉
     */
    public enum MenuType {
        MENU,    // 菜單
        BUTTON,  // 按鈕
        LINK     // 外部鏈接
    }
    
    /**
     * 判斷是否為根菜單
     */
    public boolean isRoot() {
        return parentId == null;
    }
    
    /**
     * 判斷是否有子菜單
     */
    public boolean hasChildren() {
        return children != null && !children.isEmpty();
    }
}
