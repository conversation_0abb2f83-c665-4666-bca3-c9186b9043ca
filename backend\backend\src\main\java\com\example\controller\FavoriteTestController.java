package com.example.controller;

import com.example.dto.ApiResponse;
import com.example.entity.FavoriteItem;
import com.example.enums.ItemType;
import com.example.repository.FavoriteItemRepository;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 收藏功能测试控制器
 * 用于创建测试数据和测试API
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/api/favorite-test")
@CrossOrigin(origins = "*")
public class FavoriteTestController {
    
    private static final Logger logger = LoggerFactory.getLogger(FavoriteTestController.class);
    
    @Autowired
    private FavoriteItemRepository favoriteItemRepository;
    
    /**
     * 创建测试数据
     */
    @PostMapping("/create-test-data")
    public ResponseEntity<ApiResponse<String>> createTestData() {
        try {
            // 检查是否已有测试数据
            long existingCount = favoriteItemRepository.count();
            if (existingCount > 0) {
                return ResponseEntity.ok(ApiResponse.success("测试数据已存在，共 " + existingCount + " 条记录"));
            }
            
            List<FavoriteItem> testItems = new ArrayList<>();
            
            // 创建文章类型的测试数据
            testItems.add(new FavoriteItem(
                "Spring Boot 完整教程",
                "从零开始学习Spring Boot框架，包含实战项目案例",
                ItemType.ARTICLE,
                "https://example.com/spring-boot-tutorial",
                "https://example.com/thumbnails/spring-boot.jpg"
            ));
            
            testItems.add(new FavoriteItem(
                "Vue3 组合式API详解",
                "深入理解Vue3的组合式API，提升开发效率",
                ItemType.ARTICLE,
                "https://example.com/vue3-composition-api",
                "https://example.com/thumbnails/vue3.jpg"
            ));
            
            testItems.add(new FavoriteItem(
                "MySQL性能优化指南",
                "数据库性能优化的最佳实践和技巧分享",
                ItemType.ARTICLE,
                "https://example.com/mysql-optimization",
                "https://example.com/thumbnails/mysql.jpg"
            ));
            
            // 创建视频类型的测试数据
            testItems.add(new FavoriteItem(
                "Java并发编程实战",
                "深入浅出讲解Java并发编程的核心概念和实践",
                ItemType.VIDEO,
                "https://example.com/java-concurrency-video",
                "https://example.com/thumbnails/java-concurrency.jpg"
            ));
            
            testItems.add(new FavoriteItem(
                "React Hooks完整教程",
                "从基础到高级，全面掌握React Hooks的使用",
                ItemType.VIDEO,
                "https://example.com/react-hooks-video",
                "https://example.com/thumbnails/react-hooks.jpg"
            ));
            
            // 创建图片类型的测试数据
            testItems.add(new FavoriteItem(
                "系统架构设计图",
                "微服务架构设计的经典案例图解",
                ItemType.IMAGE,
                "https://example.com/architecture-diagram",
                "https://example.com/thumbnails/architecture.jpg"
            ));
            
            testItems.add(new FavoriteItem(
                "数据库ER图设计",
                "电商系统数据库设计的ER图示例",
                ItemType.IMAGE,
                "https://example.com/database-er-diagram",
                "https://example.com/thumbnails/database-er.jpg"
            ));
            
            // 创建链接类型的测试数据
            testItems.add(new FavoriteItem(
                "GitHub优秀开源项目",
                "精选的GitHub开源项目推荐列表",
                ItemType.LINK,
                "https://github.com/awesome-projects",
                "https://example.com/thumbnails/github.jpg"
            ));
            
            testItems.add(new FavoriteItem(
                "前端开发工具集合",
                "提升前端开发效率的工具和资源汇总",
                ItemType.LINK,
                "https://example.com/frontend-tools",
                "https://example.com/thumbnails/frontend-tools.jpg"
            ));
            
            // 创建其他类型的测试数据
            testItems.add(new FavoriteItem(
                "编程学习路线图",
                "完整的编程学习路径规划和建议",
                ItemType.OTHER,
                "https://example.com/programming-roadmap",
                "https://example.com/thumbnails/roadmap.jpg"
            ));
            
            // 保存测试数据
            favoriteItemRepository.saveAll(testItems);
            
            logger.info("成功创建 {} 条测试数据", testItems.size());
            return ResponseEntity.ok(ApiResponse.success("成功创建 " + testItems.size() + " 条测试数据"));
            
        } catch (Exception e) {
            logger.error("创建测试数据失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("创建测试数据失败: " + e.getMessage()));
        }
    }
    
    /**
     * 清除测试数据
     */
    @DeleteMapping("/clear-test-data")
    public ResponseEntity<ApiResponse<String>> clearTestData() {
        try {
            long count = favoriteItemRepository.count();
            favoriteItemRepository.deleteAll();
            
            logger.info("成功清除 {} 条测试数据", count);
            return ResponseEntity.ok(ApiResponse.success("成功清除 " + count + " 条测试数据"));
            
        } catch (Exception e) {
            logger.error("清除测试数据失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("清除测试数据失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取测试数据统计
     */
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<Object>> getTestStats() {
        try {
            long totalItems = favoriteItemRepository.count();
            
            // 按类型统计
            long articleCount = favoriteItemRepository.countByItemType(ItemType.ARTICLE);
            long videoCount = favoriteItemRepository.countByItemType(ItemType.VIDEO);
            long imageCount = favoriteItemRepository.countByItemType(ItemType.IMAGE);
            long linkCount = favoriteItemRepository.countByItemType(ItemType.LINK);
            long otherCount = favoriteItemRepository.countByItemType(ItemType.OTHER);
            
            Map<String, Long> stats = new HashMap<>();
            stats.put("total", totalItems);
            stats.put("article", articleCount);
            stats.put("video", videoCount);
            stats.put("image", imageCount);
            stats.put("link", linkCount);
            stats.put("other", otherCount);
            
            return ResponseEntity.ok(ApiResponse.success(stats));
            
        } catch (Exception e) {
            logger.error("获取测试数据统计失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取统计失败: " + e.getMessage()));
        }
    }
}
