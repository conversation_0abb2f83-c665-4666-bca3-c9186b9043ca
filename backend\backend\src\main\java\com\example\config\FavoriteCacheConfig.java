package com.example.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 收藏功能缓存配置
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Configuration
@EnableCaching
public class FavoriteCacheConfig {
    
    // 缓存名称常量
    public static final String FAVORITE_RANKING_CACHE = "favorite:ranking";
    public static final String FAVORITE_STATS_CACHE = "favorite:stats";
    public static final String FAVORITE_COUNT_CACHE = "favorite:count";
    public static final String USER_FAVORITES_CACHE = "user:favorites";
    public static final String RECENT_ITEMS_CACHE = "recent:items";
    
    /**
     * Redis缓存管理器配置
     */
    @Bean
    @Primary
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        // 默认缓存配置
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30)) // 默认30分钟过期
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues(); // 不缓存null值
        
        // 不同缓存的个性化配置
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // 收藏排行榜缓存 - 5分钟过期
        cacheConfigurations.put(FAVORITE_RANKING_CACHE, 
                defaultConfig.entryTtl(Duration.ofMinutes(5)));
        
        // 收藏统计缓存 - 10分钟过期
        cacheConfigurations.put(FAVORITE_STATS_CACHE, 
                defaultConfig.entryTtl(Duration.ofMinutes(10)));
        
        // 收藏计数缓存 - 15分钟过期
        cacheConfigurations.put(FAVORITE_COUNT_CACHE, 
                defaultConfig.entryTtl(Duration.ofMinutes(15)));
        
        // 用户收藏列表缓存 - 20分钟过期
        cacheConfigurations.put(USER_FAVORITES_CACHE, 
                defaultConfig.entryTtl(Duration.ofMinutes(20)));
        
        // 最近内容缓存 - 1小时过期
        cacheConfigurations.put(RECENT_ITEMS_CACHE, 
                defaultConfig.entryTtl(Duration.ofHours(1)));
        
        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }
    
    /**
     * 本地缓存管理器（作为备用）
     */
    @Bean("localCacheManager")
    public CacheManager localCacheManager() {
        return new ConcurrentMapCacheManager(
                FAVORITE_RANKING_CACHE,
                FAVORITE_STATS_CACHE,
                FAVORITE_COUNT_CACHE,
                USER_FAVORITES_CACHE,
                RECENT_ITEMS_CACHE
        );
    }
}
