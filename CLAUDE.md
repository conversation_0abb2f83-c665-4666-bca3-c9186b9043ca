# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Backend (Spring Boot)
```bash
# Start backend server
cd backend/backend
mvn spring-boot:run

# Run tests
mvn test

# Build project
mvn clean install

# Skip tests during build
mvn clean install -DskipTests
```

### Frontend (Vue.js)
```bash
# Start development server
cd frontend
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run E2E tests
npm run test

# Run specific test suites
npm run test:product
npm run test:api
```

## Project Architecture

### Full-Stack Architecture
- **Backend**: Spring Boot 3.2.12 with Java 21
- **Frontend**: Vue 3 + TypeScript + Element Plus
- **Database**: MySQL 8.0 with JPA/Hibernate
- **Cache**: Redis for session management and rate limiting
- **Authentication**: JWT with access/refresh token pattern
- **Email**: Spring Mail with verification code system

### Backend Architecture

#### Core Package Structure
- `config/`: Security configuration, JWT filters, Redis, and threading
- `controller/`: REST endpoints with separate user and admin controllers
- `service/`: Business logic layer with mixed interface/implementation pattern
- `repository/`: Spring Data JPA repositories
- `entity/`: JPA entities with proper relationships
- `dto/`: Data transfer objects for API communication
- `util/`: Utility classes (JWT, Redis keys, etc.)

#### Authentication System
- **Dual Authentication**: Separate `UserService` and `AdminService` both implementing `UserDetailsService`
- **JWT Implementation**: Access tokens (2h) + refresh tokens (7d) with Redis storage
- **Security Filter**: `JwtAuthenticationFilter` handles token validation
- **Composite Authentication**: `CompositeAuthenticationProvider` supports both user types

#### Service Layer Patterns
- **Interface-based services**: `FavoriteService`, `ProductService` with separate implementations
- **Direct implementation services**: `UserService`, `AdminService`, `EmailService`
- **Centralized caching**: `RedisService` manages all Redis operations
- **Transaction boundaries**: Proper `@Transactional` usage across services

#### Key Features
- **Email verification**: Rate-limited verification codes (2 per 5min, 5 per day)
- **Identity verification**: File upload system for ID document verification
- **Social features**: User following system with Redis-backed counters
- **Menu system**: Hierarchical menus with Redis caching
- **Product catalog**: Multi-level categories with image support

### Frontend Architecture

#### Core Structure
- `api/`: Axios-based API client with interceptors
- `stores/`: Pinia stores for state management
- `router/`: Vue Router with authentication guards
- `views/`: Page components
- `components/`: Reusable Vue components

#### Key Stores
- `user.ts`: User authentication and profile management
- `admin.ts`: Admin authentication and management
- `favorite.ts`: Favorite items management
- `menu.ts`: Menu system state
- `product.ts`: Product catalog state

## Database Schema

### Main Tables
- `users`: User accounts with identity verification status
- `admins`: Administrator accounts
- `identity_verifications`: ID verification requests with file uploads
- `favorites`: User favorite items
- `menus`: Hierarchical menu structure
- `products`: Product catalog with categories
- `user_follows`: Social following relationships

### Key Relationships
- One-to-many: User → Identity Verifications
- One-to-many: User → Favorites
- Many-to-many: User → User (following)
- Self-referencing: Menu → Menu (parent-child)

## Configuration

### Required Environment
- Java 21
- Node.js 18+
- MySQL 8.0
- Redis 6.0+

### Configuration Files
- `application.yml`: Database, Redis, JWT, and email settings
- `package.json`: Frontend dependencies and scripts
- `pom.xml`: Backend dependencies and Maven configuration

### Key Configuration Points
- **Database**: Connection string in `application.yml`
- **Redis**: Host/port configuration for caching
- **JWT**: Secret key and token expiration settings
- **Email**: SMTP configuration for verification codes
- **File uploads**: Upload directory and size limits

## Development Patterns

### Service Layer Best Practices
- Use `@Transactional` for database operations
- Implement proper exception handling with custom exceptions
- Leverage Redis caching for performance-critical operations
- Follow rate limiting patterns for sensitive operations

### Security Considerations
- JWT tokens stored in Redis with proper expiration
- Rate limiting on email operations
- File upload validation and secure storage
- Role-based access control for admin endpoints

### API Design
- Consistent `ApiResponse<T>` wrapper for all endpoints
- Proper HTTP status codes and error messages
- Validation using Bean Validation annotations
- Separate endpoints for user and admin operations

## Testing

### Backend Testing
- Unit tests for service layer with Spring Boot Test
- Integration tests with TestContainers (if configured)
- Security tests for authentication flows

### Frontend Testing
- E2E tests with Playwright
- Component tests for Vue components
- API integration tests

## Deployment Notes

### Backend Deployment
- Requires MySQL database with proper character set
- Redis server for caching and session management
- File upload directory with write permissions
- Email server configuration for verification codes

### Frontend Deployment
- Static files served from `dist/` after build
- Environment-specific API endpoints
- CORS configuration for cross-origin requests

## Common Issues

### Authentication Issues
- Check JWT secret key configuration
- Verify Redis connection for token storage
- Ensure proper CORS settings for frontend

### Email Issues
- Verify SMTP configuration in `application.yml`
- Check email rate limiting in Redis
- Validate email templates and sending logic

### Database Issues
- Ensure proper MySQL charset configuration
- Check JPA dialect settings
- Verify foreign key constraints

## API Documentation

Swagger UI available at: `http://localhost:8080/swagger-ui/index.html` when backend is running.