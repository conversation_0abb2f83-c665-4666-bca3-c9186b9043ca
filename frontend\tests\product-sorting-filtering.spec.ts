import { test, expect, Page } from '@playwright/test'

/**
 * 商品排序和篩選功能測試
 * 測試所有排序方式和篩選條件
 */

// 測試配置
const BASE_URL = 'http://localhost:5173'
const PRODUCTS_URL = `${BASE_URL}/products`

// 等待時間配置
const WAIT_TIME = {
  SHORT: 1000,
  MEDIUM: 2000,
  LONG: 3000
}

// 測試用戶配置
const TEST_USER = {
  username: 'how',
  password: 'howhowhowtogo'
}

test.describe('商品排序和篩選功能測試', () => {
  
  test.beforeEach(async ({ page }) => {
    // 每個測試前都導航到商品頁面
    await page.goto(PRODUCTS_URL)
    await page.waitForLoadState('networkidle')
    
    // 等待商品列表加載
    await page.waitForSelector('.product-list', { timeout: 10000 })
    await page.waitForTimeout(WAIT_TIME.SHORT)
  })

  test('默認排序功能測試', async ({ page }) => {
    console.log('測試默認排序功能...')
    
    // 確保默認排序按鈕是激活狀態
    const defaultSortBtn = page.locator('button:has-text("默認排序")')
    await expect(defaultSortBtn).toHaveClass(/el-button--primary/)
    
    // 檢查是否有商品顯示
    const productCards = page.locator('.product-card')
    await expect(productCards.first()).toBeVisible()
    
    // 記錄第一個商品的名稱作為基準
    const firstProductName = await productCards.first().locator('.product-name').textContent()
    console.log('默認排序第一個商品:', firstProductName)
    
    expect(firstProductName).toBeTruthy()
  })

  test('價格升序排序測試', async ({ page }) => {
    console.log('測試價格升序排序...')
    
    // 點擊價格升序按鈕
    await page.click('button:has-text("價格 ↑")')
    await page.waitForTimeout(WAIT_TIME.MEDIUM)
    
    // 檢查按鈕狀態
    const priceAscBtn = page.locator('button:has-text("價格 ↑")')
    await expect(priceAscBtn).toHaveClass(/el-button--primary/)
    
    // 獲取前幾個商品的價格並驗證升序
    const productCards = page.locator('.product-card')
    const count = Math.min(3, await productCards.count())
    
    const prices: number[] = []
    for (let i = 0; i < count; i++) {
      const priceText = await productCards.nth(i).locator('.current-price').textContent()
      const price = parseFloat(priceText?.replace('¥', '') || '0')
      prices.push(price)
      console.log(`商品 ${i + 1} 價格: ¥${price}`)
    }
    
    // 驗證價格是升序排列
    for (let i = 1; i < prices.length; i++) {
      expect(prices[i]).toBeGreaterThanOrEqual(prices[i - 1])
    }
  })

  test('價格降序排序測試', async ({ page }) => {
    console.log('測試價格降序排序...')
    
    // 點擊價格降序按鈕
    await page.click('button:has-text("價格 ↓")')
    await page.waitForTimeout(WAIT_TIME.MEDIUM)
    
    // 檢查按鈕狀態
    const priceDescBtn = page.locator('button:has-text("價格 ↓")')
    await expect(priceDescBtn).toHaveClass(/el-button--primary/)
    
    // 獲取前幾個商品的價格並驗證降序
    const productCards = page.locator('.product-card')
    const count = Math.min(3, await productCards.count())
    
    const prices: number[] = []
    for (let i = 0; i < count; i++) {
      const priceText = await productCards.nth(i).locator('.current-price').textContent()
      const price = parseFloat(priceText?.replace('¥', '') || '0')
      prices.push(price)
      console.log(`商品 ${i + 1} 價格: ¥${price}`)
    }
    
    // 驗證價格是降序排列
    for (let i = 1; i < prices.length; i++) {
      expect(prices[i]).toBeLessThanOrEqual(prices[i - 1])
    }
  })

  test('銷量優先排序測試', async ({ page }) => {
    console.log('測試銷量優先排序...')
    
    // 點擊銷量優先按鈕
    await page.click('button:has-text("銷量優先")')
    await page.waitForTimeout(WAIT_TIME.MEDIUM)
    
    // 檢查按鈕狀態
    const salesBtn = page.locator('button:has-text("銷量優先")')
    await expect(salesBtn).toHaveClass(/el-button--primary/)
    
    // 檢查是否有商品顯示
    const productCards = page.locator('.product-card')
    await expect(productCards.first()).toBeVisible()
    
    // 記錄商品信息
    const count = Math.min(3, await productCards.count())
    for (let i = 0; i < count; i++) {
      const productName = await productCards.nth(i).locator('.product-name').textContent()
      console.log(`銷量排序商品 ${i + 1}: ${productName}`)
    }
  })

  test('推薦商品篩選測試', async ({ page }) => {
    console.log('測試推薦商品篩選...')
    
    // 點擊推薦商品標籤
    const recommendedTag = page.locator('.el-check-tag:has-text("推薦商品")')
    await recommendedTag.click()
    await page.waitForTimeout(WAIT_TIME.MEDIUM)
    
    // 檢查標籤是否被選中
    await expect(recommendedTag).toHaveClass(/is-checked/)
    
    // 檢查是否有商品顯示
    const productCards = page.locator('.product-card')
    if (await productCards.count() > 0) {
      await expect(productCards.first()).toBeVisible()
      
      // 檢查商品是否有推薦標籤
      const recommendedBadges = page.locator('.product-tags .el-tag:has-text("推薦")')
      const badgeCount = await recommendedBadges.count()
      console.log(`找到 ${badgeCount} 個推薦商品標籤`)
      
      if (badgeCount > 0) {
        await expect(recommendedBadges.first()).toBeVisible()
      }
    } else {
      console.log('沒有找到推薦商品')
    }
  })

  test('熱門商品篩選測試', async ({ page }) => {
    console.log('測試熱門商品篩選...')
    
    // 點擊熱門商品標籤
    const hotTag = page.locator('.el-check-tag:has-text("熱門商品")')
    await hotTag.click()
    await page.waitForTimeout(WAIT_TIME.MEDIUM)
    
    // 檢查標籤是否被選中
    await expect(hotTag).toHaveClass(/is-checked/)
    
    // 檢查是否有商品顯示
    const productCards = page.locator('.product-card')
    if (await productCards.count() > 0) {
      await expect(productCards.first()).toBeVisible()
      
      // 檢查商品是否有熱門標籤
      const hotBadges = page.locator('.product-tags .el-tag:has-text("熱門")')
      const badgeCount = await hotBadges.count()
      console.log(`找到 ${badgeCount} 個熱門商品標籤`)
      
      if (badgeCount > 0) {
        await expect(hotBadges.first()).toBeVisible()
      }
    } else {
      console.log('沒有找到熱門商品')
    }
  })

  test('搜索功能測試', async ({ page }) => {
    console.log('測試搜索功能...')
    
    // 在搜索框中輸入關鍵詞
    const searchInput = page.locator('.search-box input')
    await searchInput.fill('iPhone')
    
    // 點擊搜索按鈕
    await page.click('.search-box button:has-text("搜索")')
    await page.waitForTimeout(WAIT_TIME.MEDIUM)
    
    // 檢查搜索結果
    const productCards = page.locator('.product-card')
    if (await productCards.count() > 0) {
      const firstProductName = await productCards.first().locator('.product-name').textContent()
      console.log('搜索結果第一個商品:', firstProductName)
      
      // 驗證搜索結果包含關鍵詞（不區分大小寫）
      expect(firstProductName?.toLowerCase()).toContain('iphone')
    } else {
      console.log('沒有找到搜索結果')
    }
  })

  test('組合篩選測試', async ({ page }) => {
    console.log('測試組合篩選功能...')
    
    // 先選擇推薦商品
    const recommendedTag = page.locator('.el-check-tag:has-text("推薦商品")')
    await recommendedTag.click()
    await page.waitForTimeout(WAIT_TIME.SHORT)
    
    // 再選擇價格升序排序
    await page.click('button:has-text("價格 ↑")')
    await page.waitForTimeout(WAIT_TIME.MEDIUM)
    
    // 檢查兩個條件都生效
    await expect(recommendedTag).toHaveClass(/is-checked/)
    const priceAscBtn = page.locator('button:has-text("價格 ↑")')
    await expect(priceAscBtn).toHaveClass(/el-button--primary/)
    
    // 檢查是否有商品顯示
    const productCards = page.locator('.product-card')
    if (await productCards.count() > 0) {
      console.log('組合篩選成功，找到商品')
    } else {
      console.log('組合篩選沒有找到商品')
    }
  })

  test('清除篩選條件測試', async ({ page }) => {
    console.log('測試清除篩選條件...')
    
    // 先設置一些篩選條件
    await page.click('button:has-text("價格 ↑")')
    const recommendedTag = page.locator('.el-check-tag:has-text("推薦商品")')
    await recommendedTag.click()
    await page.waitForTimeout(WAIT_TIME.SHORT)
    
    // 檢查清除按鈕是否存在並點擊
    const clearBtn = page.locator('button:has-text("清除篩選")')
    if (await clearBtn.isVisible()) {
      await clearBtn.click()
      await page.waitForTimeout(WAIT_TIME.SHORT)
      
      // 檢查是否回到默認狀態
      const defaultSortBtn = page.locator('button:has-text("默認排序")')
      await expect(defaultSortBtn).toHaveClass(/el-button--primary/)
      
      // 檢查推薦標籤是否取消選中
      await expect(recommendedTag).not.toHaveClass(/is-checked/)
    }
  })

  test('分頁功能測試', async ({ page }) => {
    console.log('測試分頁功能...')
    
    // 檢查是否有分頁組件
    const pagination = page.locator('.pagination-container')
    if (await pagination.isVisible()) {
      // 檢查總數顯示
      const totalText = page.locator('.el-pagination__total')
      if (await totalText.isVisible()) {
        const totalContent = await totalText.textContent()
        console.log('商品總數:', totalContent)
      }
      
      // 如果有下一頁，測試翻頁
      const nextBtn = page.locator('.el-pagination .btn-next')
      if (await nextBtn.isEnabled()) {
        await nextBtn.click()
        await page.waitForTimeout(WAIT_TIME.MEDIUM)
        console.log('成功翻到下一頁')
      }
    } else {
      console.log('沒有分頁組件（商品數量較少）')
    }
  })
})
