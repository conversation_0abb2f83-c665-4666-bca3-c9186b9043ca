package com.example.service;

import com.example.dto.RegisterRequest;
import com.example.entity.User;
import com.example.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
@Slf4j
public class UserService implements UserDetailsService {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private EmailService emailService;
    
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("用戶不存在: " + username));
    }
    
    /**
     * 用戶註冊
     */
    @Transactional
    public User register(RegisterRequest request) {
        // 驗證用戶名是否已存在
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new RuntimeException("用戶名已存在");
        }
        
        // 驗證郵箱是否已存在
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("郵箱已被註冊");
        }
        
        // 驗證郵件驗證碼
        if (!emailService.verifyCode(request.getEmail(), request.getVerificationCode())) {
            throw new RuntimeException("驗證碼錯誤或已過期");
        }
        
        // 創建用戶
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setEmailVerified(true); // 註冊時已驗證郵箱
        user.setEnabled(true);
        user.setRole(User.Role.USER);
        
        User savedUser = userRepository.save(user);
        log.info("用戶註冊成功: {}", savedUser.getUsername());
        
        return savedUser;
    }
    
    /**
     * 根據用戶名查找用戶
     */
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }
    
    /**
     * 根據郵箱查找用戶
     */
    public Optional<User> findByEmail(String email) {
        return userRepository.findByEmail(email);
    }
    
    /**
     * 更新用戶信息
     */
    @Transactional
    public User updateUser(User user) {
        return userRepository.save(user);
    }
    
    /**
     * 檢查用戶名是否可用
     */
    public boolean isUsernameAvailable(String username) {
        return !userRepository.existsByUsername(username);
    }
    
    /**
     * 檢查郵箱是否可用
     */
    public boolean isEmailAvailable(String email) {
        return !userRepository.existsByEmail(email);
    }
    
    /**
     * 檢查身份證號是否已被使用
     */
    public boolean isIdCardNumberAvailable(String idCardNumber) {
        return !userRepository.existsByIdCardNumber(idCardNumber);
    }
    
    /**
     * 更新用戶身份認證狀態
     */
    @Transactional
    public void updateIdentityVerificationStatus(Long userId, User.IdentityStatus status) {
        Optional<User> userOpt = userRepository.findById(userId);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            user.setIdentityStatus(status);
            user.setIdentityVerified(status == User.IdentityStatus.APPROVED);
            userRepository.save(user);
            log.info("更新用戶身份認證狀態: userId={}, status={}", userId, status);
        }
    }
}
