package com.example.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付記錄實體類
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Entity
@Table(name = "payments")
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "order"})
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Payment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 訂單ID
     */
    @Column(name = "order_id", nullable = false, unique = true)
    private Long orderId;
    
    /**
     * 支付方式：1-支付寶，2-微信，3-銀行卡
     */
    @Column(name = "payment_method", nullable = false)
    private Integer paymentMethod;
    
    /**
     * 支付狀態：0-待支付，1-支付成功，2-支付失敗，-1-已退款
     */
    @Column(name = "payment_status", nullable = false)
    private Integer paymentStatus = 0;
    
    /**
     * 支付金額
     */
    @Column(name = "payment_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal paymentAmount;
    
    /**
     * 第三方支付交易號（支付寶交易號等）
     */
    @Column(name = "third_party_trade_no", length = 64)
    private String thirdPartyTradeNo;
    
    /**
     * 第三方支付流水號
     */
    @Column(name = "third_party_serial_no", length = 64)
    private String thirdPartySerialNo;
    
    /**
     * 支付寶買家賬號
     */
    @Column(name = "buyer_account", length = 100)
    private String buyerAccount;
    
    /**
     * 支付時間
     */
    @Column(name = "paid_at")
    private LocalDateTime paidAt;
    
    /**
     * 支付回調時間
     */
    @Column(name = "callback_at")
    private LocalDateTime callbackAt;
    
    /**
     * 支付回調內容（JSON格式）
     */
    @Column(name = "callback_content", columnDefinition = "TEXT")
    private String callbackContent;
    
    /**
     * 退款金額
     */
    @Column(name = "refund_amount", precision = 10, scale = 2)
    private BigDecimal refundAmount;
    
    /**
     * 退款時間
     */
    @Column(name = "refunded_at")
    private LocalDateTime refundedAt;
    
    /**
     * 退款原因
     */
    @Column(name = "refund_reason", length = 200)
    private String refundReason;
    
    /**
     * 創建時間
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    /**
     * 更新時間
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // 關聯關係 - 訂單
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", insertable = false, updatable = false)
    private Order order;
    
    /**
     * 支付方式枚舉
     */
    public static class PaymentMethod {
        public static final int ALIPAY = 1;     // 支付寶
        public static final int WECHAT = 2;     // 微信支付
        public static final int BANK_CARD = 3;  // 銀行卡
    }
    
    /**
     * 支付狀態枚舉
     */
    public static class PaymentStatus {
        public static final int REFUNDED = -1;  // 已退款
        public static final int PENDING = 0;    // 待支付
        public static final int SUCCESS = 1;    // 支付成功
        public static final int FAILED = 2;     // 支付失敗
    }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 構造函數 - 創建新支付記錄
     */
    public Payment(Long orderId, Integer paymentMethod, BigDecimal paymentAmount) {
        this.orderId = orderId;
        this.paymentMethod = paymentMethod;
        this.paymentAmount = paymentAmount;
        this.paymentStatus = PaymentStatus.PENDING;
    }
    
    /**
     * 標記支付成功
     */
    public void markPaymentSuccess(String thirdPartyTradeNo, String buyerAccount, String callbackContent) {
        this.paymentStatus = PaymentStatus.SUCCESS;
        this.thirdPartyTradeNo = thirdPartyTradeNo;
        this.buyerAccount = buyerAccount;
        this.callbackContent = callbackContent;
        this.paidAt = LocalDateTime.now();
        this.callbackAt = LocalDateTime.now();
    }
}
