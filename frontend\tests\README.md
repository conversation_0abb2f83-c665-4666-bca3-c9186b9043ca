# 商品排序和篩選功能測試

本目錄包含商品排序和篩選功能的自動化測試腳本。

## 測試覆蓋範圍

### 排序功能
- ✅ **默認排序** - 測試默認商品排序顯示
- ✅ **價格升序** - 測試按價格從低到高排序
- ✅ **價格降序** - 測試按價格從高到低排序  
- ✅ **銷量優先** - 測試按銷量從高到低排序

### 篩選功能
- ✅ **推薦商品** - 測試推薦商品篩選
- ✅ **熱門商品** - 測試熱門商品篩選
- ✅ **搜索功能** - 測試商品名稱搜索
- ✅ **組合篩選** - 測試多個條件組合篩選

### 其他功能
- ✅ **清除篩選** - 測試清除所有篩選條件
- ✅ **分頁功能** - 測試商品列表分頁

## 測試文件

- `product-sorting-filtering.spec.ts` - 主要測試文件
- `test-runner.js` - Node.js 測試運行器
- `run-product-tests.bat` - Windows 批處理腳本
- `run-product-tests.sh` - Linux/macOS Shell 腳本

## 運行測試

### 前提條件

1. **前端服務運行**
   ```bash
   cd frontend
   npm run dev
   ```
   確保前端服務在 `http://localhost:5173` 運行

2. **後端服務運行**
   - 啟動 Spring Boot 後端服務
   - 確保服務在 `http://localhost:8080` 運行
   - 確保數據庫連接正常

3. **測試數據**
   - 確保數據庫中有足夠的商品數據
   - 確保有推薦商品和熱門商品數據

### 運行方式

#### 方式一：使用 npm 腳本

```bash
# 運行排序篩選測試
npm run test:sorting

# 帶瀏覽器界面運行
npm run test:sorting:headed

# 調試模式運行
npm run test:sorting:debug
```

#### 方式二：使用批處理腳本

**Windows:**
```cmd
run-product-tests.bat
```

**Linux/macOS:**
```bash
chmod +x run-product-tests.sh
./run-product-tests.sh
```

#### 方式三：使用 Node.js 運行器

```bash
node tests/test-runner.js

# 帶瀏覽器界面
node tests/test-runner.js --headed
```

#### 方式四：直接使用 Playwright

```bash
# 基本運行
npx playwright test tests/product-sorting-filtering.spec.ts

# 帶瀏覽器界面
npx playwright test tests/product-sorting-filtering.spec.ts --headed

# 調試模式
npx playwright test tests/product-sorting-filtering.spec.ts --debug

# 生成報告
npx playwright test tests/product-sorting-filtering.spec.ts --reporter=html
```

## 測試配置

### 環境配置
- **前端地址**: `http://localhost:5173`
- **測試超時**: 30秒
- **重試次數**: 1次
- **瀏覽器**: Chromium (默認)

### 測試數據要求
- 至少需要 10+ 個商品
- 需要有不同價格範圍的商品
- 需要有推薦商品 (isRecommended = 1)
- 需要有熱門商品 (isHot = 1)
- 需要有銷量數據 (soldCount > 0)

## 故障排除

### 常見問題

1. **測試超時**
   - 檢查前端和後端服務是否正常運行
   - 檢查網絡連接
   - 增加等待時間

2. **找不到元素**
   - 檢查前端頁面是否正確加載
   - 檢查 CSS 選擇器是否正確
   - 檢查頁面結構是否有變化

3. **API 調用失敗**
   - 檢查後端服務狀態
   - 檢查數據庫連接
   - 檢查 API 端點是否正確

4. **數據不匹配**
   - 檢查測試數據是否充足
   - 檢查商品狀態是否正確
   - 檢查緩存是否需要清除

### 調試技巧

1. **使用 headed 模式**
   ```bash
   npm run test:sorting:headed
   ```

2. **使用調試模式**
   ```bash
   npm run test:sorting:debug
   ```

3. **查看詳細日誌**
   ```bash
   npx playwright test tests/product-sorting-filtering.spec.ts --reporter=list
   ```

4. **生成測試報告**
   ```bash
   npx playwright test tests/product-sorting-filtering.spec.ts --reporter=html
   npx playwright show-report
   ```

## 測試結果

測試完成後會顯示：
- ✅ 通過的測試數量
- ❌ 失敗的測試數量
- ⏱️ 執行時間
- 📊 詳細測試報告（可選）

## 持續集成

可以將這些測試集成到 CI/CD 流程中：

```yaml
# GitHub Actions 示例
- name: Run Product Sorting Tests
  run: |
    npm install
    npm run dev &
    sleep 10
    npm run test:sorting
```

## 貢獻指南

添加新測試時請：
1. 遵循現有的測試結構
2. 添加適當的等待時間
3. 包含錯誤處理
4. 更新此文檔

## 聯繫方式

如有問題請聯繫開發團隊或提交 Issue。
