import { test, expect } from '@playwright/test';

test.describe('商品管理系統測試', () => {
  
  test.beforeEach(async ({ page }) => {
    // 假設需要登錄才能訪問商品頁面
    await page.goto('/login');
    
    // 模擬登錄過程（根據實際登錄流程調整）
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // 等待登錄完成
    await page.waitForURL('/app/home');
  });

  test('商品列表頁面基本功能測試', async ({ page }) => {
    // 導航到商品列表頁面
    await page.goto('/products');
    
    // 檢查頁面標題
    await expect(page.locator('h1')).toContainText('商品列表');
    
    // 檢查分類樹是否存在
    await expect(page.locator('.product-category-tree')).toBeVisible();
    
    // 檢查商品網格是否存在
    await expect(page.locator('.product-grid')).toBeVisible();
    
    // 檢查是否有商品卡片
    const productCards = page.locator('.product-card');
    await expect(productCards.first()).toBeVisible();
    
    // 檢查分頁組件
    await expect(page.locator('.pagination-container')).toBeVisible();
  });

  test('商品分類樹功能測試', async ({ page }) => {
    await page.goto('/products');
    
    // 等待分類樹加載
    await page.waitForSelector('.product-category-tree');
    
    // 檢查"全部分類"選項
    const allCategoriesItem = page.locator('.category-item.root-item');
    await expect(allCategoriesItem).toBeVisible();
    await expect(allCategoriesItem).toContainText('全部分類');
    
    // 檢查分類樹節點
    const treeNodes = page.locator('.el-tree-node');
    await expect(treeNodes.first()).toBeVisible();
    
    // 點擊第一個分類節點
    const firstCategory = treeNodes.first().locator('.tree-node');
    await firstCategory.click();
    
    // 檢查URL是否更新了分類參數
    await page.waitForFunction(() => {
      return window.location.search.includes('category=');
    });
    
    // 檢查商品列表是否更新
    await page.waitForSelector('.product-card');
  });

  test('商品搜索功能測試', async ({ page }) => {
    await page.goto('/products');
    
    // 等待頁面加載
    await page.waitForSelector('.filter-toolbar');
    
    // 測試搜索功能
    const searchInput = page.locator('.search-box input');
    await searchInput.fill('iPhone');
    await page.keyboard.press('Enter');
    
    // 等待搜索結果
    await page.waitForTimeout(1000);
    
    // 檢查搜索結果
    const productCards = page.locator('.product-card');
    const firstCard = productCards.first();
    await expect(firstCard).toBeVisible();
    
    // 檢查商品名稱是否包含搜索關鍵詞
    const productName = firstCard.locator('.product-name');
    await expect(productName).toContainText('iPhone', { ignoreCase: true });
  });

  test('商品篩選功能測試', async ({ page }) => {
    await page.goto('/products');
    
    // 等待頁面加載
    await page.waitForSelector('.filter-toolbar');
    
    // 測試價格排序
    await page.click('button:has-text("價格 ↑")');
    await page.waitForTimeout(1000);
    
    // 檢查排序是否生效（檢查第一個商品的價格）
    const firstProductPrice = page.locator('.product-card').first().locator('.current-price');
    await expect(firstProductPrice).toBeVisible();
    
    // 測試推薦商品篩選
    await page.click('.el-check-tag:has-text("推薦商品")');
    await page.waitForTimeout(1000);
    
    // 檢查是否只顯示推薦商品
    const recommendedTags = page.locator('.product-tags .el-tag:has-text("推薦")');
    await expect(recommendedTags.first()).toBeVisible();
  });

  test('商品詳情頁面測試', async ({ page }) => {
    await page.goto('/products');
    
    // 等待商品列表加載
    await page.waitForSelector('.product-card');
    
    // 點擊第一個商品
    const firstProduct = page.locator('.product-card').first();
    await firstProduct.click();
    
    // 等待跳轉到商品詳情頁
    await page.waitForURL(/\/products\/\d+/);
    
    // 檢查商品詳情頁面元素
    await expect(page.locator('.product-detail')).toBeVisible();
    await expect(page.locator('.product-images')).toBeVisible();
    await expect(page.locator('.product-info')).toBeVisible();
    await expect(page.locator('.product-title')).toBeVisible();
    await expect(page.locator('.current-price')).toBeVisible();
    
    // 檢查商品規格信息
    await expect(page.locator('.product-specs')).toBeVisible();
    
    // 檢查操作按鈕
    await expect(page.locator('button:has-text("立即購買")')).toBeVisible();
    await expect(page.locator('button:has-text("加入購物車")')).toBeVisible();
    await expect(page.locator('button:has-text("收藏")')).toBeVisible();
    
    // 檢查商品詳情標籤頁
    await expect(page.locator('.el-tabs')).toBeVisible();
    await expect(page.locator('.el-tab-pane[name="description"]')).toBeVisible();
    await expect(page.locator('.el-tab-pane[name="specs"]')).toBeVisible();
  });

  test('商品詳情頁面交互測試', async ({ page }) => {
    // 直接訪問一個商品詳情頁（假設商品ID為1）
    await page.goto('/products/1');
    
    // 等待頁面加載
    await page.waitForSelector('.product-detail');
    
    // 測試數量選擇器
    const quantityInput = page.locator('.el-input-number input');
    await quantityInput.fill('3');
    
    // 測試收藏按鈕
    const favoriteButton = page.locator('button:has-text("收藏")');
    await favoriteButton.click();
    
    // 檢查收藏狀態變化
    await expect(page.locator('button:has-text("已收藏")')).toBeVisible();
    
    // 測試標籤頁切換
    await page.click('.el-tab-pane[name="specs"] .el-tabs__item');
    await expect(page.locator('.specs-content')).toBeVisible();
    
    // 測試圖片切換（如果有多張圖片）
    const thumbnails = page.locator('.thumbnail');
    if (await thumbnails.count() > 1) {
      await thumbnails.nth(1).click();
      // 檢查主圖是否更新
      await page.waitForTimeout(500);
    }
  });

  test('商品列表分頁功能測試', async ({ page }) => {
    await page.goto('/products');
    
    // 等待頁面加載
    await page.waitForSelector('.pagination-container');
    
    // 檢查分頁組件
    const pagination = page.locator('.el-pagination');
    await expect(pagination).toBeVisible();
    
    // 測試頁面大小選擇
    const pageSizeSelect = page.locator('.el-pagination__sizes .el-select');
    if (await pageSizeSelect.isVisible()) {
      await pageSizeSelect.click();
      await page.click('.el-select-dropdown__item:has-text("24")');
      await page.waitForTimeout(1000);
    }
    
    // 測試下一頁（如果有多頁）
    const nextButton = page.locator('.el-pagination__next');
    if (await nextButton.isEnabled()) {
      await nextButton.click();
      await page.waitForTimeout(1000);
      
      // 檢查URL是否更新了頁碼
      await page.waitForFunction(() => {
        return window.location.search.includes('page=') || 
               document.querySelector('.el-pagination__current')?.textContent !== '1';
      });
    }
  });

  test('響應式設計測試', async ({ page }) => {
    // 測試移動端視圖
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/products');
    
    // 檢查移動端佈局
    await page.waitForSelector('.products-view');
    
    // 檢查分類樹在移動端的顯示
    const sidebar = page.locator('.sidebar');
    await expect(sidebar).toBeVisible();
    
    // 測試平板視圖
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.reload();
    await page.waitForSelector('.products-view');
    
    // 檢查商品網格在平板上的佈局
    const productGrid = page.locator('.product-grid');
    await expect(productGrid).toBeVisible();
    
    // 恢復桌面視圖
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('錯誤處理測試', async ({ page }) => {
    // 測試訪問不存在的商品
    await page.goto('/products/99999');
    
    // 檢查錯誤處理（可能重定向到商品列表或顯示錯誤信息）
    await page.waitForTimeout(2000);
    
    // 檢查是否有錯誤提示或重定向
    const currentUrl = page.url();
    const hasErrorMessage = await page.locator('.el-message--error').isVisible();
    
    expect(currentUrl.includes('/products') || hasErrorMessage).toBeTruthy();
  });

  test('性能測試 - 頁面加載時間', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/products');
    await page.waitForSelector('.product-card');
    
    const loadTime = Date.now() - startTime;
    
    // 檢查頁面加載時間是否在合理範圍內（5秒內）
    expect(loadTime).toBeLessThan(5000);
    
    console.log(`商品列表頁面加載時間: ${loadTime}ms`);
  });
});
