<template>
  <!-- 有子菜單的情況 -->
  <el-sub-menu
    v-if="hasChildren"
    :index="menu.id?.toString()"
    :disabled="!menu.enabled"
  >
    <template #title>
      <el-icon v-if="menu.icon && !collapsed">
        <component :is="getIconComponent(menu.icon)" />
      </el-icon>
      <span v-if="!collapsed">{{ menu.name }}</span>
    </template>
    
    <menu-item
      v-for="child in menu.children"
      :key="child.id"
      :menu="child"
      :collapsed="collapsed"
    />
  </el-sub-menu>
  
  <!-- 沒有子菜單的情況 -->
  <el-menu-item
    v-else
    :index="menu.path || menu.id?.toString()"
    :disabled="!menu.enabled"
    @click="handleMenuClick"
  >
    <el-icon v-if="menu.icon">
      <component :is="getIconComponent(menu.icon)" />
    </el-icon>
    <template #title>
      <span>{{ menu.name }}</span>
    </template>
  </el-menu-item>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import type { MenuDto } from '@/api/menu'
import {
  House,
  User,
  UserFilled,
  Setting,
  Menu,
  View,
  CreditCard,
  Star,
  Monitor,
  Document,
  Folder,
  DataLine,
  Management,
  Key,
  Lock,
  Bell,
  Message,
  Calendar,
  Clock,
  Location,
  Phone,
  Download,
  Upload,
  Share,
  Delete,
  Edit,
  Plus,
  Minus,
  Search,
  Refresh,
  Close,
  Check,
  Warning,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  ShoppingBag,
  Box,
  FolderOpened,
  Lightning,
  Trophy,
  Tools
} from '@element-plus/icons-vue'

// Props
interface Props {
  menu: MenuDto
  collapsed?: boolean
}

const props = defineProps<Props>()

// 路由
const router = useRouter()

// 計算屬性
const hasChildren = computed(() => {
  return props.menu.children && props.menu.children.length > 0
})

// 圖標組件映射
const iconComponents = {
  House,
  User,
  UserFilled,
  Setting,
  Menu,
  View,
  CreditCard,
  Star,
  Monitor,
  Document,
  Folder,
  DataLine,
  Management,
  Key,
  Lock,
  Bell,
  Message,
  Calendar,
  Clock,
  Location,
  Phone,
  Download,
  Upload,
  Share,
  Delete,
  Edit,
  Plus,
  Minus,
  Search,
  Refresh,
  Close,
  Check,
  Warning,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  ShoppingBag,
  Box,
  FolderOpened,
  Lightning,
  Trophy,
  Tools
}

// 方法
const getIconComponent = (iconName: string) => {
  return iconComponents[iconName as keyof typeof iconComponents] || Document
}

const handleMenuClick = () => {
  // 處理菜單點擊事件
  console.log('菜單點擊:', props.menu.name, props.menu.path)

  // 如果是外部鏈接，在新窗口打開
  if (props.menu.menuType === 'LINK' && props.menu.path) {
    if (props.menu.path.startsWith('http://') || props.menu.path.startsWith('https://')) {
      window.open(props.menu.path, '_blank')
      return
    }
  }

  // 處理內部路由跳轉
  if (props.menu.menuType === 'MENU' && props.menu.path) {
    console.log('跳轉到路由:', props.menu.path)
    router.push(props.menu.path)
  }
}
</script>

<style scoped>
/* 菜單項圖標樣式 */
:deep(.el-menu-item .el-icon),
:deep(.el-sub-menu .el-sub-menu__title .el-icon) {
  margin-right: 12px;
  width: 18px;
  height: 18px;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

/* 禁用狀態樣式 */
:deep(.el-menu-item.is-disabled),
:deep(.el-sub-menu.is-disabled .el-sub-menu__title) {
  opacity: 0.4;
  cursor: not-allowed;
  color: rgba(255, 255, 255, 0.4);
}

:deep(.el-menu-item.is-disabled .el-icon),
:deep(.el-sub-menu.is-disabled .el-sub-menu__title .el-icon) {
  color: rgba(255, 255, 255, 0.3);
}

/* 子菜單縮進和樣式 */
:deep(.el-sub-menu .el-menu-item) {
  padding-left: 48px !important;
  margin: 2px 12px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  font-size: 14px;
}

:deep(.el-sub-menu .el-sub-menu .el-menu-item) {
  padding-left: 64px !important;
  margin: 2px 8px;
}

:deep(.el-sub-menu .el-sub-menu .el-sub-menu .el-menu-item) {
  padding-left: 80px !important;
  margin: 2px 4px;
}

/* 子菜單展開動畫 */
:deep(.el-sub-menu .el-menu) {
  background: transparent;
  border-radius: 8px;
  margin: 4px 0;
  overflow: hidden;
}

/* 子菜單標題樣式 */
:deep(.el-sub-menu .el-sub-menu__title) {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  position: relative;
}

:deep(.el-sub-menu .el-sub-menu__title::after) {
  content: '';
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid rgba(255, 255, 255, 0.6);
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  transition: transform 0.3s ease;
}

:deep(.el-sub-menu.is-opened .el-sub-menu__title::after) {
  transform: translateY(-50%) rotate(90deg);
}

/* 折疊狀態下的樣式調整 */
:deep(.el-menu--collapse .el-sub-menu__title span),
:deep(.el-menu--collapse .el-menu-item span) {
  display: none;
}

:deep(.el-menu--collapse .el-menu-item .el-icon),
:deep(.el-menu--collapse .el-sub-menu .el-sub-menu__title .el-icon) {
  margin-right: 0;
  width: 20px;
  height: 20px;
}

/* 菜單項懸停效果 */
:deep(.el-menu-item:hover) {
  background: rgba(255, 255, 255, 0.15) !important;
  color: white;
  transform: translateX(4px);
}

:deep(.el-menu-item:hover .el-icon) {
  color: white;
  transform: scale(1.1);
}

:deep(.el-sub-menu .el-sub-menu__title:hover) {
  background: rgba(255, 255, 255, 0.15) !important;
  color: white;
  transform: translateX(4px);
}

:deep(.el-sub-menu .el-sub-menu__title:hover .el-icon) {
  color: white;
  transform: scale(1.1);
}

/* 激活狀態的樣式 */
:deep(.el-menu-item.is-active) {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

:deep(.el-menu-item.is-active .el-icon) {
  color: white;
  transform: scale(1.1);
}

:deep(.el-sub-menu.is-active .el-sub-menu__title) {
  color: white;
  background: rgba(255, 255, 255, 0.15);
}

:deep(.el-sub-menu.is-active .el-sub-menu__title .el-icon) {
  color: white;
}

/* 子菜單項的特殊樣式 */
:deep(.el-sub-menu .el-menu-item:hover) {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: translateX(2px);
}

/* 菜單項文字樣式 */
:deep(.el-menu-item span),
:deep(.el-sub-menu .el-sub-menu__title span) {
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

/* 响应式调整 */
@media (max-width: 768px) {
  :deep(.el-menu-item .el-icon),
  :deep(.el-sub-menu .el-sub-menu__title .el-icon) {
    width: 16px;
    height: 16px;
    margin-right: 10px;
  }
  
  :deep(.el-menu-item span),
  :deep(.el-sub-menu .el-sub-menu__title span) {
    font-size: 13px;
  }
}
</style>
