<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f6f6f6;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
        }
    </style>
</head>
<body>
    <h1>支付寶沙箱支付測試</h1>
    
    <div>
        <h3>測試信息</h3>
        <p><strong>訂單ID:</strong> 3</p>
        <p><strong>訂單金額:</strong> ¥89.00</p>
        <p><strong>商品:</strong> 男士休閒T恤</p>
        <p><strong>ngrok地址:</strong> https://b02ad650a2dc.ngrok-free.app</p>
    </div>

    <div>
        <button onclick="testPayment()">測試支付寶支付</button>
        <button onclick="checkOrderStatus()">檢查訂單狀態</button>
    </div>

    <div id="result" class="result"></div>

    <div>
        <h3>支付寶沙箱測試賬號</h3>
        <p><strong>買家賬號:</strong> <EMAIL></p>
        <p><strong>登錄密碼:</strong> 111111</p>
        <p><strong>支付密碼:</strong> 111111</p>
    </div>

    <script>
        const resultDiv = document.getElementById('result');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            resultDiv.textContent += `[${timestamp}] ${message}\n`;
            resultDiv.className = `result ${type}`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        async function testPayment() {
            try {
                log('開始測試支付寶支付...');
                
                // 獲取 token
                const token = localStorage.getItem('token');
                if (!token) {
                    log('錯誤: 未找到認證 token，請先登錄', 'error');
                    return;
                }
                
                log(`使用 token: ${token.substring(0, 20)}...`);
                
                // 發起支付請求
                const orderId = 3;
                const url = `http://localhost:8080/api/payment/alipay/create?orderId=${orderId}`;
                log(`請求 URL: ${url}`);
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`響應狀態: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log(`HTTP 錯誤: ${errorText}`, 'error');
                    return;
                }
                
                const result = await response.json();
                log(`響應結果: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success && result.data) {
                    log('支付請求成功，正在打開支付頁面...', 'success');
                    
                    // 創建並提交支付表單
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = 'about:blank';
                    form.target = '_blank';
                    form.innerHTML = result.data;
                    
                    document.body.appendChild(form);
                    form.submit();
                    document.body.removeChild(form);
                    
                    log('支付頁面已在新窗口打開，請使用測試賬號完成支付', 'success');
                } else {
                    log(`支付失敗: ${result.message || '未知錯誤'}`, 'error');
                }
                
            } catch (error) {
                log(`請求失敗: ${error.message}`, 'error');
                console.error('支付測試錯誤:', error);
            }
        }

        async function checkOrderStatus() {
            try {
                log('檢查訂單狀態...');
                
                const token = localStorage.getItem('token');
                if (!token) {
                    log('錯誤: 未找到認證 token', 'error');
                    return;
                }
                
                const response = await fetch('http://localhost:8080/api/orders/3', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (!response.ok) {
                    log(`HTTP 錯誤: ${response.status}`, 'error');
                    return;
                }
                
                const order = await response.json();
                log(`訂單狀態: ${JSON.stringify(order, null, 2)}`);
                
                if (order.success && order.data) {
                    const status = order.data.status;
                    const statusText = status === 0 ? '待付款' : status === 1 ? '已付款' : status === 2 ? '已取消' : '未知';
                    log(`當前訂單狀態: ${statusText} (${status})`, status === 1 ? 'success' : 'info');
                }
                
            } catch (error) {
                log(`檢查訂單狀態失敗: ${error.message}`, 'error');
            }
        }

        // 頁面加載時檢查 token
        window.onload = function() {
            const token = localStorage.getItem('token');
            if (token) {
                log(`已找到認證 token: ${token.substring(0, 20)}...`);
            } else {
                log('未找到認證 token，請先在主應用中登錄', 'error');
            }
        };
    </script>
</body>
</html>
