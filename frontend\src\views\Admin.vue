<template>
  <div class="admin-container">
    <!-- 頂部導航 -->
    <el-header class="header">
      <div class="header-content">
        <el-button @click="$router.go(-1)" link style="color: white;">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1>管理後台</h1>
        <div></div>
      </div>
    </el-header>

    <el-main class="main-content">
      <!-- 統計卡片 -->
      <div class="stats-cards">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-item">
                <div class="stat-icon pending">
                  <el-icon><Clock /></el-icon>
                </div>
                <div class="stat-content">
                  <h3>{{ pendingCount }}</h3>
                  <p>待審核</p>
                </div>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-item">
                <div class="stat-icon approved">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="stat-content">
                  <h3>{{ approvedCount }}</h3>
                  <p>已通過</p>
                </div>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-item">
                <div class="stat-icon rejected">
                  <el-icon><Close /></el-icon>
                </div>
                <div class="stat-content">
                  <h3>{{ rejectedCount }}</h3>
                  <p>已拒絕</p>
                </div>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-item">
                <div class="stat-icon total">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="stat-content">
                  <h3>{{ totalCount }}</h3>
                  <p>總計</p>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 待審核列表 -->
      <el-card>
        <template #header>
          <div class="card-header">
            <h3>待審核身份認證</h3>
            <el-button @click="loadPendingVerifications" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>
        
        <el-table 
          :data="pendingVerifications" 
          v-loading="loading"
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          
          <el-table-column prop="username" label="用戶名" width="120" />
          
          <el-table-column prop="email" label="郵箱" width="200" />
          
          <el-table-column prop="realName" label="真實姓名" width="120" />
          
          <el-table-column prop="idCardNumber" label="身份證號" width="180">
            <template #default="scope">
              {{ maskIdCard(scope.row.idCardNumber) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="submittedAt" label="提交時間" width="180">
            <template #default="scope">
              {{ formatDate(scope.row.submittedAt) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button 
                type="primary" 
                size="small" 
                @click="viewDetail(scope.row)"
              >
                查看詳情
              </el-button>
              <el-button 
                type="success" 
                size="small" 
                @click="approveVerification(scope.row.id)"
              >
                通過
              </el-button>
              <el-button 
                type="danger" 
                size="small" 
                @click="rejectVerification(scope.row.id)"
              >
                拒絕
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div v-if="pendingVerifications.length === 0 && !loading" class="no-data">
          <el-empty description="暫無待審核的身份認證" />
        </div>
      </el-card>
    </el-main>

    <!-- 詳情對話框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="身份認證詳情"
      width="800px"
      :before-close="closeDetailDialog"
    >
      <div v-if="currentDetail" class="detail-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-section">
              <h4>基本信息</h4>
              <p><strong>用戶名：</strong>{{ currentDetail.username }}</p>
              <p><strong>郵箱：</strong>{{ currentDetail.email }}</p>
              <p><strong>真實姓名：</strong>{{ currentDetail.realName }}</p>
              <p><strong>身份證號：</strong>{{ currentDetail.idCardNumber }}</p>
              <p><strong>提交時間：</strong>{{ formatDate(currentDetail.submittedAt) }}</p>
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="detail-section">
              <h4>身份證圖片</h4>
              <div class="id-card-images">
                <div class="id-card-item">
                  <p>正面：</p>
                  <img 
                    :src="`http://localhost:8080/api/files/${currentDetail.idCardFrontUrl}`" 
                    alt="身份證正面"
                    @click="previewImage(`http://localhost:8080/api/files/${currentDetail.idCardFrontUrl}`)"
                  />
                </div>
                <div class="id-card-item">
                  <p>反面：</p>
                  <img 
                    :src="`http://localhost:8080/api/files/${currentDetail.idCardBackUrl}`" 
                    alt="身份證反面"
                    @click="previewImage(`http://localhost:8080/api/files/${currentDetail.idCardBackUrl}`)"
                  />
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <div class="review-section">
          <h4>審核操作</h4>
          <el-form :model="reviewForm" label-width="100px">
            <el-form-item label="審核意見">
              <el-input
                v-model="reviewForm.comment"
                type="textarea"
                :rows="3"
                placeholder="請輸入審核意見（可選）"
              />
            </el-form-item>
          </el-form>
          
          <div class="review-buttons">
            <el-button 
              type="success" 
              @click="reviewVerification(currentDetail.id, true)"
              :loading="reviewLoading"
            >
              通過審核
            </el-button>
            <el-button 
              type="danger" 
              @click="reviewVerification(currentDetail.id, false)"
              :loading="reviewLoading"
            >
              拒絕審核
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 圖片預覽對話框 -->
    <el-dialog
      v-model="imagePreviewVisible"
      title="圖片預覽"
      width="600px"
    >
      <div class="image-preview-content">
        <img :src="previewImageUrl" alt="預覽圖片" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  ArrowLeft, 
  Clock, 
  Check, 
  Close, 
  Document, 
  Refresh 
} from '@element-plus/icons-vue'
import { identityAPI } from '../api'

const loading = ref(false)
const reviewLoading = ref(false)
const detailDialogVisible = ref(false)
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')

const pendingVerifications = ref<any[]>([])
const currentDetail = ref<any>(null)

const reviewForm = reactive({
  comment: ''
})

// 統計數據
const pendingCount = computed(() => pendingVerifications.value.length)
const approvedCount = ref(0)
const rejectedCount = ref(0)
const totalCount = computed(() => pendingCount.value + approvedCount.value + rejectedCount.value)

onMounted(() => {
  loadPendingVerifications()
})

const loadPendingVerifications = async () => {
  loading.value = true
  try {
    const response = await identityAPI.getPendingVerifications()
    if (response.success) {
      pendingVerifications.value = response.data
    }
  } catch (error) {
    ElMessage.error('加載待審核列表失敗')
  } finally {
    loading.value = false
  }
}

const viewDetail = async (row: any) => {
  try {
    const response = await identityAPI.getVerificationDetail(row.id)
    if (response.success) {
      currentDetail.value = response.data
      reviewForm.comment = ''
      detailDialogVisible.value = true
    }
  } catch (error) {
    ElMessage.error('獲取詳情失敗')
  }
}

const approveVerification = async (id: number) => {
  try {
    await ElMessageBox.confirm('確定要通過這個身份認證嗎？', '確認操作', {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'success'
    })
    
    await reviewVerification(id, true)
  } catch {
    // 用戶取消
  }
}

const rejectVerification = async (id: number) => {
  try {
    const { value: comment } = await ElMessageBox.prompt('請輸入拒絕原因', '拒絕審核', {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      inputType: 'textarea',
      inputPlaceholder: '請輸入拒絕原因'
    })
    
    await reviewVerification(id, false, comment)
  } catch {
    // 用戶取消
  }
}

const reviewVerification = async (id: number, approved: boolean, comment?: string) => {
  reviewLoading.value = true
  try {
    const reviewComment = comment || reviewForm.comment
    const response = await identityAPI.reviewVerification(id, approved, reviewComment)
    
    if (response.success) {
      ElMessage.success(approved ? '審核通過' : '審核拒絕')
      detailDialogVisible.value = false
      loadPendingVerifications() // 刷新列表
      
      // 更新統計
      if (approved) {
        approvedCount.value++
      } else {
        rejectedCount.value++
      }
    }
  } catch (error: any) {
    ElMessage.error(error.message || '審核失敗')
  } finally {
    reviewLoading.value = false
  }
}

const closeDetailDialog = () => {
  detailDialogVisible.value = false
  currentDetail.value = null
  reviewForm.comment = ''
}

const previewImage = (url: string) => {
  previewImageUrl.value = url
  imagePreviewVisible.value = true
}

const maskIdCard = (idCard: string) => {
  if (!idCard || idCard.length < 8) return idCard
  return idCard.substring(0, 4) + '****' + idCard.substring(idCard.length - 4)
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style scoped>
.admin-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.header-content h1 {
  margin: 0;
  font-size: 24px;
}

.main-content {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.pending {
  background-color: #e6a23c;
}

.stat-icon.approved {
  background-color: #67c23a;
}

.stat-icon.rejected {
  background-color: #f56c6c;
}

.stat-icon.total {
  background-color: #409eff;
}

.stat-content h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #333;
}

.stat-content p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #333;
}

.no-data {
  text-align: center;
  padding: 40px;
}

.detail-content {
  padding: 20px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.detail-section p {
  margin: 8px 0;
  color: #666;
}

.id-card-images {
  display: flex;
  gap: 20px;
}

.id-card-item {
  flex: 1;
}

.id-card-item p {
  margin: 0 0 10px 0;
  font-weight: bold;
}

.id-card-item img {
  width: 100%;
  max-width: 200px;
  height: 120px;
  object-fit: cover;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
}

.id-card-item img:hover {
  transform: scale(1.05);
}

.review-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.review-section h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.review-buttons {
  margin-top: 15px;
  text-align: right;
}

.image-preview-content {
  text-align: center;
}

.image-preview-content img {
  max-width: 100%;
  max-height: 500px;
  border-radius: 4px;
}
</style>
