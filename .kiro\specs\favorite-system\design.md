# 收藏系统设计文档

## 概述

收藏系统是一个允许用户收藏内容、管理个人收藏夹并查看热门收藏排行榜的功能模块。该系统将集成到现有的SpringBoot + Vue3用户认证系统中，采用RESTful API设计，支持实时状态同步和高性能查询。

## 架构

### 整体架构
```
前端 (Vue3 + TypeScript)
    ↓ HTTP/REST API
后端 (SpringBoot + Spring Security)
    ↓ JPA/Hibernate
数据库 (MySQL 8.0)
    ↓ 缓存层
Redis (排行榜缓存 + 速率限制)
```

### 核心组件
- **FavoriteController**: REST API控制器，处理收藏相关的HTTP请求
- **FavoriteService**: 业务逻辑层，处理收藏操作和排行榜计算
- **FavoriteRepository**: 数据访问层，提供数据库操作接口
- **Favorite实体**: 收藏关系的数据模型
- **FavoriteItem实体**: 可收藏内容的数据模型
- **Redis缓存**: 用于排行榜缓存和操作频率限制

## 组件和接口

### 后端组件

#### 1. 实体类 (Entity)

**Favorite实体**
```java
@Entity
@Table(name = "favorites")
public class Favorite {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Column(name = "item_id", nullable = false) 
    private Long itemId;
    
    @Column(name = "item_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private ItemType itemType;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    // 关联关系
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "item_id", insertable = false, updatable = false)
    private FavoriteItem item;
}
```

**FavoriteItem实体**
```java
@Entity
@Table(name = "favorite_items")
public class FavoriteItem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String title;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "item_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private ItemType itemType;
    
    @Column(name = "content_url")
    private String contentUrl;
    
    @Column(name = "thumbnail_url")
    private String thumbnailUrl;
    
    @Column(name = "favorite_count")
    private Integer favoriteCount = 0;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
```

**ItemType枚举**
```java
public enum ItemType {
    ARTICLE,    // 文章
    VIDEO,      // 视频
    IMAGE,      // 图片
    LINK,       // 链接
    OTHER       // 其他
}
```

#### 2. 数据访问层 (Repository)

**FavoriteRepository**
```java
@Repository
public interface FavoriteRepository extends JpaRepository<Favorite, Long> {
    // 查找用户的收藏
    Page<Favorite> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);
    
    // 检查收藏状态
    boolean existsByUserIdAndItemId(Long userId, Long itemId);
    
    // 删除收藏
    void deleteByUserIdAndItemId(Long userId, Long itemId);
    
    // 统计收藏数量
    long countByItemId(Long itemId);
    
    // 获取热门收藏统计
    @Query("SELECT f.itemId, COUNT(f) as count FROM Favorite f GROUP BY f.itemId ORDER BY count DESC")
    List<Object[]> findPopularItems(Pageable pageable);
}
```

**FavoriteItemRepository**
```java
@Repository
public interface FavoriteItemRepository extends JpaRepository<FavoriteItem, Long> {
    // 按收藏数排序
    Page<FavoriteItem> findAllByOrderByFavoriteCountDesc(Pageable pageable);
    
    // 按类型查询
    Page<FavoriteItem> findByItemTypeOrderByFavoriteCountDesc(ItemType itemType, Pageable pageable);
    
    // 批量更新收藏数
    @Modifying
    @Query("UPDATE FavoriteItem f SET f.favoriteCount = :count WHERE f.id = :itemId")
    void updateFavoriteCount(Long itemId, Integer count);
}
```

#### 3. 业务逻辑层 (Service)

**FavoriteService接口**
```java
public interface FavoriteService {
    // 收藏操作
    ApiResponse<String> addFavorite(Long userId, Long itemId);
    
    // 取消收藏
    ApiResponse<String> removeFavorite(Long userId, Long itemId);
    
    // 检查收藏状态
    ApiResponse<Boolean> isFavorited(Long userId, Long itemId);
    
    // 获取用户收藏列表
    ApiResponse<PagedResponse<FavoriteDto>> getUserFavorites(Long userId, int page, int size);
    
    // 获取收藏排行榜
    ApiResponse<PagedResponse<FavoriteItemDto>> getFavoriteRanking(int page, int size, ItemType itemType);
    
    // 获取收藏统计
    ApiResponse<FavoriteStatsDto> getFavoriteStats(Long itemId);
}
```

#### 4. 控制器层 (Controller)

**FavoriteController**
```java
@RestController
@RequestMapping("/api/favorite")
public class FavoriteController {
    
    @PostMapping("/{itemId}")
    public ApiResponse<String> addFavorite(@PathVariable Long itemId, Authentication auth);
    
    @DeleteMapping("/{itemId}")
    public ApiResponse<String> removeFavorite(@PathVariable Long itemId, Authentication auth);
    
    @GetMapping("/status/{itemId}")
    public ApiResponse<Boolean> getFavoriteStatus(@PathVariable Long itemId, Authentication auth);
    
    @GetMapping("/my-favorites")
    public ApiResponse<PagedResponse<FavoriteDto>> getMyFavorites(
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "20") int size,
        Authentication auth);
    
    @GetMapping("/ranking")
    public ApiResponse<PagedResponse<FavoriteItemDto>> getFavoriteRanking(
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "20") int size,
        @RequestParam(required = false) ItemType itemType);
    
    @GetMapping("/stats/{itemId}")
    public ApiResponse<FavoriteStatsDto> getFavoriteStats(@PathVariable Long itemId);
}
```

### 前端组件

#### 1. API服务层

**favoriteAPI**
```typescript
export const favoriteAPI = {
  // 收藏内容
  addFavorite: (itemId: number): Promise<ApiResponse<string>> =>
    api.post(`/favorite/${itemId}`),

  // 取消收藏
  removeFavorite: (itemId: number): Promise<ApiResponse<string>> =>
    api.delete(`/favorite/${itemId}`),

  // 检查收藏状态
  getFavoriteStatus: (itemId: number): Promise<ApiResponse<boolean>> =>
    api.get(`/favorite/status/${itemId}`),

  // 获取我的收藏
  getMyFavorites: (page: number = 0, size: number = 20): Promise<ApiResponse<PagedResponse<FavoriteDto>>> =>
    api.get(`/favorite/my-favorites?page=${page}&size=${size}`),

  // 获取收藏排行榜
  getFavoriteRanking: (page: number = 0, size: number = 20, itemType?: string): Promise<ApiResponse<PagedResponse<FavoriteItemDto>>> =>
    api.get(`/favorite/ranking?page=${page}&size=${size}${itemType ? `&itemType=${itemType}` : ''}`),

  // 获取收藏统计
  getFavoriteStats: (itemId: number): Promise<ApiResponse<FavoriteStatsDto>> =>
    api.get(`/favorite/stats/${itemId}`)
}
```

#### 2. 状态管理 (Pinia Store)

**favoriteStore**
```typescript
export const useFavoriteStore = defineStore('favorite', {
  state: () => ({
    myFavorites: [] as FavoriteDto[],
    favoriteRanking: [] as FavoriteItemDto[],
    favoriteStatus: new Map<number, boolean>(),
    loading: false,
    error: null as string | null
  }),

  actions: {
    async addFavorite(itemId: number),
    async removeFavorite(itemId: number),
    async loadMyFavorites(page: number, size: number),
    async loadFavoriteRanking(page: number, size: number, itemType?: string),
    async checkFavoriteStatus(itemId: number)
  }
})
```

#### 3. Vue组件

**FavoriteButton.vue** - 收藏按钮组件
**MyFavorites.vue** - 个人收藏夹页面
**FavoriteRanking.vue** - 收藏排行榜页面

## 数据模型

### 数据库表结构

**favorites表**
```sql
CREATE TABLE favorites (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    item_id BIGINT NOT NULL,
    item_type ENUM('ARTICLE', 'VIDEO', 'IMAGE', 'LINK', 'OTHER') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_item (user_id, item_id),
    INDEX idx_user_id (user_id),
    INDEX idx_item_id (item_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

**favorite_items表**
```sql
CREATE TABLE favorite_items (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    item_type ENUM('ARTICLE', 'VIDEO', 'IMAGE', 'LINK', 'OTHER') NOT NULL,
    content_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    favorite_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_favorite_count (favorite_count),
    INDEX idx_item_type (item_type),
    INDEX idx_created_at (created_at)
);
```

### DTO类

**FavoriteDto**
```java
public class FavoriteDto {
    private Long id;
    private Long itemId;
    private String itemTitle;
    private String itemDescription;
    private ItemType itemType;
    private String thumbnailUrl;
    private LocalDateTime createdAt;
}
```

**FavoriteItemDto**
```java
public class FavoriteItemDto {
    private Long id;
    private String title;
    private String description;
    private ItemType itemType;
    private String contentUrl;
    private String thumbnailUrl;
    private Integer favoriteCount;
    private LocalDateTime createdAt;
    private Boolean isFavorited; // 当前用户是否已收藏
}
```

**FavoriteStatsDto**
```java
public class FavoriteStatsDto {
    private Long itemId;
    private Integer favoriteCount;
    private Boolean isFavorited;
    private LocalDateTime lastFavoriteTime;
}
```

## 错误处理

### 异常类型
- **FavoriteNotFoundException**: 收藏记录不存在
- **DuplicateFavoriteException**: 重复收藏
- **RateLimitExceededException**: 操作频率超限
- **UnauthorizedFavoriteException**: 无权限操作

### 错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "errorCode": "FAVORITE_001",
  "timestamp": "2025-01-15T10:30:00Z"
}
```

### 错误处理策略
1. **数据库约束错误**: 捕获唯一键冲突，返回友好提示
2. **权限验证错误**: 验证用户身份，返回401/403状态码
3. **速率限制错误**: 使用Redis实现，返回429状态码
4. **数据一致性错误**: 使用事务确保数据一致性

## 测试策略

### 单元测试
- **Service层测试**: 测试业务逻辑，使用Mock Repository
- **Repository层测试**: 测试数据访问，使用@DataJpaTest
- **Controller层测试**: 测试API接口，使用@WebMvcTest

### 集成测试
- **API集成测试**: 测试完整的请求-响应流程
- **数据库集成测试**: 测试数据库操作和事务
- **缓存集成测试**: 测试Redis缓存功能

### 性能测试
- **并发收藏测试**: 测试高并发收藏操作
- **排行榜查询测试**: 测试大数据量下的排行榜性能
- **缓存性能测试**: 测试Redis缓存的命中率和响应时间

### 测试数据
- 创建测试用户和测试内容
- 模拟不同类型的收藏场景
- 测试边界条件和异常情况

## 性能优化

### 数据库优化
1. **索引策略**: 在user_id、item_id、created_at字段上创建索引
2. **分页查询**: 使用LIMIT和OFFSET进行分页
3. **批量操作**: 批量更新收藏计数，减少数据库访问
4. **读写分离**: 考虑使用读写分离提高查询性能

### 缓存策略
1. **排行榜缓存**: 使用Redis缓存热门排行榜，定时更新
2. **收藏状态缓存**: 缓存用户的收藏状态，减少数据库查询
3. **计数缓存**: 缓存收藏计数，异步更新数据库
4. **缓存失效**: 设置合理的缓存过期时间和失效策略

### 前端优化
1. **懒加载**: 收藏列表和排行榜使用懒加载
2. **状态缓存**: 在前端缓存收藏状态，减少API调用
3. **防抖处理**: 收藏按钮添加防抖，避免重复点击
4. **虚拟滚动**: 长列表使用虚拟滚动提高性能

## 安全考虑

### 权限控制
1. **用户认证**: 所有收藏操作需要用户登录
2. **操作权限**: 用户只能操作自己的收藏
3. **内容权限**: 验证用户是否有权限收藏特定内容

### 速率限制
1. **收藏操作限制**: 每分钟最多20次收藏操作
2. **查询限制**: 每分钟最多100次查询请求
3. **IP限制**: 对异常IP进行限制

### 数据安全
1. **SQL注入防护**: 使用参数化查询
2. **XSS防护**: 对用户输入进行转义
3. **CSRF防护**: 使用CSRF令牌
4. **数据加密**: 敏感数据加密存储