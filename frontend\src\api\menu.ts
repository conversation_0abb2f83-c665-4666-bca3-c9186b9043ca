import api, { type ApiResponse } from './index'

// 菜單類型枚舉
export enum MenuType {
  MENU = 'MENU',
  BUTTON = 'BUTTON',
  LINK = 'LINK'
}

// 菜單數據接口
export interface MenuDto {
  id?: number
  name: string
  path?: string
  icon?: string
  description?: string
  sortOrder?: number
  enabled?: boolean
  menuType?: MenuType
  permission?: string
  parentId?: number
  children?: MenuDto[]
  createdAt?: string
  updatedAt?: string
}

// 菜單創建請求接口
export interface MenuCreateRequest {
  name: string
  path?: string
  icon?: string
  description?: string
  sortOrder?: number
  enabled?: boolean
  menuType?: MenuType
  permission?: string
  parentId?: number
}

// 菜單更新請求接口
export interface MenuUpdateRequest {
  name?: string
  path?: string
  icon?: string
  description?: string
  sortOrder?: number
  enabled?: boolean
  menuType?: MenuType
  permission?: string
  parentId?: number
}

/**
 * 菜單API服務類
 */
export class MenuService {
  private static readonly BASE_URL = '/menu'

  /**
   * 獲取當前用戶的菜單樹
   */
  static async getUserMenuTree(): Promise<MenuDto[]> {
    try {
      const response = await api.get<ApiResponse<MenuDto[]>>(`${this.BASE_URL}/tree`)

      if (response.success) {
        return response.data || []
      } else {
        throw new Error(response.message || '獲取菜單樹失敗')
      }
    } catch (error: any) {
      console.error('獲取用戶菜單樹失敗:', error)
      throw new Error('Access Denied')
    }
  }

  /**
   * 獲取完整菜單樹（管理員專用）
   */
  static async getFullMenuTree(): Promise<MenuDto[]> {
    try {
      const response = await api.get<ApiResponse<MenuDto[]>>(`${this.BASE_URL}/admin/tree`)

      if (response.success) {
        return response.data || []
      } else {
        throw new Error(response.message || '獲取完整菜單樹失敗')
      }
    } catch (error: any) {
      console.error('獲取完整菜單樹失敗:', error)
      throw new Error('獲取完整菜單樹失敗')
    }
  }

  /**
   * 根據ID獲取菜單詳情（管理員專用）
   */
  static async getMenuById(id: number): Promise<MenuDto> {
    try {
      const response = await api.get<ApiResponse<MenuDto>>(`${this.BASE_URL}/admin/${id}`)

      if (response.success) {
        return response.data!
      } else {
        throw new Error(response.message || '獲取菜單詳情失敗')
      }
    } catch (error: any) {
      console.error('獲取菜單詳情失敗:', error)
      throw new Error('獲取菜單詳情失敗')
    }
  }

  /**
   * 創建菜單（管理員專用）
   */
  static async createMenu(request: MenuCreateRequest): Promise<MenuDto> {
    try {
      const response = await api.post<ApiResponse<MenuDto>>(`${this.BASE_URL}/admin`, request)

      if (response.success) {
        return response.data!
      } else {
        throw new Error(response.message || '創建菜單失敗')
      }
    } catch (error: any) {
      console.error('創建菜單失敗:', error)
      throw new Error('創建菜單失敗')
    }
  }

  /**
   * 更新菜單（管理員專用）
   */
  static async updateMenu(id: number, request: MenuUpdateRequest): Promise<MenuDto> {
    try {
      const response = await api.put<ApiResponse<MenuDto>>(`${this.BASE_URL}/admin/${id}`, request)

      if (response.success) {
        return response.data!
      } else {
        throw new Error(response.message || '更新菜單失敗')
      }
    } catch (error: any) {
      console.error('更新菜單失敗:', error)
      throw new Error('更新菜單失敗')
    }
  }

  /**
   * 刪除菜單（管理員專用）
   */
  static async deleteMenu(id: number): Promise<void> {
    try {
      const response = await api.delete<ApiResponse<string>>(`${this.BASE_URL}/admin/${id}`)

      if (!response.success) {
        throw new Error(response.message || '刪除菜單失敗')
      }
    } catch (error: any) {
      console.error('刪除菜單失敗:', error)
      throw new Error('刪除菜單失敗')
    }
  }

  /**
   * 清除菜單緩存（管理員專用）
   */
  static async clearMenuCache(): Promise<void> {
    try {
      const response = await api.post<ApiResponse<string>>(`${this.BASE_URL}/admin/cache/clear`)

      if (!response.success) {
        throw new Error(response.message || '清除菜單緩存失敗')
      }
    } catch (error: any) {
      console.error('清除菜單緩存失敗:', error)
      throw new Error('清除菜單緩存失敗')
    }
  }

  /**
   * 預熱菜單緩存（管理員專用）
   */
  static async warmUpMenuCache(): Promise<void> {
    try {
      const response = await api.post<ApiResponse<string>>(`${this.BASE_URL}/admin/cache/warmup`)

      if (!response.success) {
        throw new Error(response.message || '預熱菜單緩存失敗')
      }
    } catch (error: any) {
      console.error('預熱菜單緩存失敗:', error)
      throw new Error('預熱菜單緩存失敗')
    }
  }

  /**
   * 將菜單樹轉換為扁平列表（用於選擇父菜單等場景）
   */
  static flattenMenuTree(menuTree: MenuDto[]): MenuDto[] {
    const result: MenuDto[] = []
    
    const flatten = (menus: MenuDto[], level: number = 0) => {
      for (const menu of menus) {
        // 添加層級標識用於顯示
        const flatMenu = { 
          ...menu, 
          name: '　'.repeat(level) + menu.name // 使用全角空格縮進
        }
        result.push(flatMenu)
        
        if (menu.children && menu.children.length > 0) {
          flatten(menu.children, level + 1)
        }
      }
    }
    
    flatten(menuTree)
    return result
  }

  /**
   * 根據路徑查找菜單項
   */
  static findMenuByPath(menuTree: MenuDto[], path: string): MenuDto | null {
    for (const menu of menuTree) {
      if (menu.path === path) {
        return menu
      }
      
      if (menu.children && menu.children.length > 0) {
        const found = this.findMenuByPath(menu.children, path)
        if (found) {
          return found
        }
      }
    }
    
    return null
  }

  /**
   * 獲取菜單的所有父級路徑（用於面包屑導航）
   */
  static getMenuBreadcrumb(menuTree: MenuDto[], targetPath: string): MenuDto[] {
    const breadcrumb: MenuDto[] = []
    
    const findPath = (menus: MenuDto[], path: string, currentPath: MenuDto[]): boolean => {
      for (const menu of menus) {
        const newPath = [...currentPath, menu]
        
        if (menu.path === path) {
          breadcrumb.push(...newPath)
          return true
        }
        
        if (menu.children && menu.children.length > 0) {
          if (findPath(menu.children, path, newPath)) {
            return true
          }
        }
      }
      
      return false
    }
    
    findPath(menuTree, targetPath, [])
    return breadcrumb
  }
}

export default MenuService
