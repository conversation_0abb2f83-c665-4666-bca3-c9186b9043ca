package com.example.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 訂單項目實體類
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Entity
@Table(name = "order_items")
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "order", "product"})
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderItem {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 訂單ID
     */
    @Column(name = "order_id", nullable = false)
    private Long orderId;
    
    /**
     * 商品ID
     */
    @Column(name = "product_id", nullable = false)
    private Long productId;
    
    /**
     * 商品名稱快照
     */
    @Column(name = "product_name", nullable = false, length = 200)
    private String productName;
    
    /**
     * 商品圖片快照
     */
    @Column(name = "product_image_url")
    private String productImageUrl;
    
    /**
     * 商品數量
     */
    @Column(name = "quantity", nullable = false)
    private Integer quantity;
    
    /**
     * 商品單價快照
     */
    @Column(name = "unit_price", nullable = false, precision = 10, scale = 2)
    private BigDecimal unitPrice;
    
    /**
     * 小計金額
     */
    @Column(name = "subtotal", nullable = false, precision = 10, scale = 2)
    private BigDecimal subtotal;
    
    /**
     * 商品品牌快照
     */
    @Column(name = "product_brand", length = 100)
    private String productBrand;
    
    /**
     * 商品型號快照
     */
    @Column(name = "product_model", length = 100)
    private String productModel;
    
    /**
     * 創建時間
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    /**
     * 更新時間
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // 關聯關係 - 訂單
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", insertable = false, updatable = false)
    private Order order;
    
    // 關聯關係 - 商品
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", insertable = false, updatable = false)
    private Product product;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        // 自動計算小計
        if (unitPrice != null && quantity != null) {
            subtotal = unitPrice.multiply(BigDecimal.valueOf(quantity));
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
        // 自動計算小計
        if (unitPrice != null && quantity != null) {
            subtotal = unitPrice.multiply(BigDecimal.valueOf(quantity));
        }
    }
    
    /**
     * 構造函數 - 創建新訂單項目
     */
    public OrderItem(Long orderId, Long productId, String productName, String productImageUrl, 
                    Integer quantity, BigDecimal unitPrice, String productBrand, String productModel) {
        this.orderId = orderId;
        this.productId = productId;
        this.productName = productName;
        this.productImageUrl = productImageUrl;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.productBrand = productBrand;
        this.productModel = productModel;
        this.subtotal = unitPrice.multiply(BigDecimal.valueOf(quantity));
    }
    
    /**
     * 計算小計
     */
    public BigDecimal calculateSubtotal() {
        if (unitPrice != null && quantity != null) {
            return unitPrice.multiply(BigDecimal.valueOf(quantity));
        }
        return BigDecimal.ZERO;
    }
}
