package com.example.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class RegisterRequest {
    
    @NotBlank(message = "用戶名不能為空")
    @Size(min = 3, max = 20, message = "用戶名長度必須在3-20個字符之間")
    private String username;
    
    @NotBlank(message = "郵箱不能為空")
    @Email(message = "郵箱格式不正確")
    private String email;
    
    @NotBlank(message = "密碼不能為空")
    @Size(min = 5, max = 20, message = "密碼長度必須在5-20個字符之間")
    private String password;
    
    @NotBlank(message = "驗證碼不能為空")
    private String verificationCode;
}
