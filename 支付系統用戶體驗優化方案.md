# 支付系統用戶體驗優化方案

## 🎯 當前用戶體驗問題

### 1. 支付流程問題
- 支付頁面加載慢
- 支付狀態反饋不及時
- 支付失敗處理不友好
- 缺少支付進度指示

### 2. 界面交互問題
- 支付按鈕響應慢
- 缺少加載動畫
- 錯誤提示不清晰
- 移動端適配不佳

## 🚀 用戶體驗優化實施

### 1. 支付流程優化

```vue
<!-- PaymentView.vue 優化版本 -->
<template>
  <div class="payment-container">
    <!-- 支付進度指示器 -->
    <div class="payment-progress">
      <el-steps :active="currentStep" finish-status="success">
        <el-step title="確認訂單" icon="el-icon-document"></el-step>
        <el-step title="選擇支付" icon="el-icon-credit-card"></el-step>
        <el-step title="支付中" icon="el-icon-loading"></el-step>
        <el-step title="支付完成" icon="el-icon-check"></el-step>
      </el-steps>
    </div>

    <!-- 支付狀態卡片 -->
    <div class="payment-status-card">
      <transition name="fade" mode="out-in">
        <!-- 待支付狀態 -->
        <div v-if="paymentStatus === 'pending'" key="pending" class="status-pending">
          <div class="status-icon">
            <el-icon class="pending-icon"><Clock /></el-icon>
          </div>
          <h3>等待支付</h3>
          <p>請點擊下方按鈕完成支付</p>
          
          <!-- 倒計時 -->
          <div class="countdown">
            <span>支付剩餘時間：</span>
            <span class="countdown-time">{{ formatCountdown(remainingTime) }}</span>
          </div>
        </div>

        <!-- 支付中狀態 -->
        <div v-else-if="paymentStatus === 'processing'" key="processing" class="status-processing">
          <div class="status-icon">
            <el-icon class="processing-icon rotating"><Loading /></el-icon>
          </div>
          <h3>支付處理中</h3>
          <p>請在支付寶頁面完成支付，請勿關閉此頁面</p>
          
          <!-- 支付提示 -->
          <div class="payment-tips">
            <div class="tip-item">
              <el-icon><InfoFilled /></el-icon>
              <span>支付完成後會自動跳轉</span>
            </div>
            <div class="tip-item">
              <el-icon><WarningFilled /></el-icon>
              <span>如遇問題請聯繫客服</span>
            </div>
          </div>
        </div>

        <!-- 支付成功狀態 -->
        <div v-else-if="paymentStatus === 'success'" key="success" class="status-success">
          <div class="status-icon">
            <el-icon class="success-icon"><SuccessFilled /></el-icon>
          </div>
          <h3>支付成功</h3>
          <p>您的訂單已支付完成</p>
          
          <!-- 成功動畫 -->
          <div class="success-animation">
            <div class="checkmark">
              <div class="checkmark-circle"></div>
              <div class="checkmark-stem"></div>
              <div class="checkmark-kick"></div>
            </div>
          </div>
        </div>

        <!-- 支付失敗狀態 -->
        <div v-else-if="paymentStatus === 'failed'" key="failed" class="status-failed">
          <div class="status-icon">
            <el-icon class="failed-icon"><CircleCloseFilled /></el-icon>
          </div>
          <h3>支付失敗</h3>
          <p>{{ failureReason || '支付過程中出現問題' }}</p>
          
          <!-- 失敗處理選項 -->
          <div class="failure-actions">
            <el-button type="primary" @click="retryPayment">
              <el-icon><Refresh /></el-icon>
              重新支付
            </el-button>
            <el-button @click="contactSupport">
              <el-icon><Service /></el-icon>
              聯繫客服
            </el-button>
          </div>
        </div>
      </transition>
    </div>

    <!-- 智能支付按鈕 -->
    <div class="payment-actions">
      <el-button
        v-if="paymentStatus === 'pending'"
        type="primary"
        size="large"
        @click="startPayment"
        :loading="paying"
        class="smart-pay-btn"
      >
        <template v-if="!paying">
          <el-icon><CreditCard /></el-icon>
          <span>立即支付 ¥{{ order.totalAmount }}</span>
        </template>
        <template v-else>
          <span>正在跳轉支付寶...</span>
        </template>
      </el-button>
    </div>

    <!-- 支付安全提示 -->
    <div class="security-tips">
      <div class="security-item">
        <el-icon><Lock /></el-icon>
        <span>256位SSL加密保護</span>
      </div>
      <div class="security-item">
        <el-icon><Shield /></el-icon>
        <span>支付寶安全保障</span>
      </div>
      <div class="security-item">
        <el-icon><Checked /></el-icon>
        <span>7天無理由退款</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const paymentStatus = ref<'pending' | 'processing' | 'success' | 'failed'>('pending')
const currentStep = ref(1)
const remainingTime = ref(15 * 60) // 15分鐘倒計時
const failureReason = ref('')
const paying = ref(false)

// 倒計時格式化
const formatCountdown = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 倒計時邏輯
const countdownTimer = ref<number | null>(null)
const startCountdown = () => {
  countdownTimer.value = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value--
    } else {
      // 支付超時
      paymentStatus.value = 'failed'
      failureReason.value = '支付超時，請重新發起支付'
      stopCountdown()
    }
  }, 1000)
}

const stopCountdown = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
}

// 智能支付邏輯
const startPayment = async () => {
  try {
    paying.value = true
    paymentStatus.value = 'processing'
    currentStep.value = 2
    
    // 停止倒計時
    stopCountdown()
    
    const response = await fetch(`/api/payment/alipay/create?orderId=${order.value.id}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    const result = await response.json()
    
    if (result.success && result.data) {
      // 跳轉到支付寶
      const newWindow = window.open('', '_blank')
      if (newWindow) {
        newWindow.document.write(result.data)
        newWindow.document.close()
        
        // 開始監聽支付結果
        startPaymentMonitoring()
        
        ElMessage.success('正在跳轉到支付寶...')
      } else {
        throw new Error('彈窗被阻止，請允許彈窗後重試')
      }
    } else {
      throw new Error(result.message || '發起支付失敗')
    }
  } catch (error) {
    console.error('發起支付失敗:', error)
    paymentStatus.value = 'failed'
    failureReason.value = error.message
    ElMessage.error(error.message)
  } finally {
    paying.value = false
  }
}

// 支付監控
const startPaymentMonitoring = () => {
  let checkCount = 0
  const maxChecks = 60 // 最多檢查5分鐘
  
  const checkPaymentStatus = async () => {
    try {
      const response = await fetch(`/api/payment/status/${order.value.id}`)
      const result = await response.json()
      
      if (result.success && result.data) {
        const status = result.data.status
        
        if (status === 'SUCCESS') {
          paymentStatus.value = 'success'
          currentStep.value = 4
          await refreshOrderInfo()
          
          // 播放成功音效
          playSuccessSound()
          
          // 顯示成功消息
          ElMessage({
            type: 'success',
            message: '支付成功！',
            duration: 3000,
            showClose: true
          })
          
          return
        } else if (status === 'FAILED') {
          paymentStatus.value = 'failed'
          failureReason.value = '支付失敗，請重試'
          return
        }
      }
      
      checkCount++
      if (checkCount < maxChecks) {
        // 動態調整檢查間隔
        const interval = Math.min(3000 + checkCount * 500, 10000)
        setTimeout(checkPaymentStatus, interval)
      } else {
        // 超時處理
        paymentStatus.value = 'failed'
        failureReason.value = '支付狀態檢查超時，請手動刷新頁面確認'
      }
      
    } catch (error) {
      console.error('檢查支付狀態失敗:', error)
      checkCount++
      if (checkCount < maxChecks) {
        setTimeout(checkPaymentStatus, 5000)
      }
    }
  }
  
  // 延遲3秒開始檢查，給支付寶處理時間
  setTimeout(checkPaymentStatus, 3000)
}

// 重新支付
const retryPayment = () => {
  paymentStatus.value = 'pending'
  currentStep.value = 1
  remainingTime.value = 15 * 60
  startCountdown()
}

// 聯繫客服
const contactSupport = () => {
  ElMessageBox.alert(
    '客服電話：400-123-4567\n客服郵箱：<EMAIL>\n在線客服：點擊右下角客服圖標',
    '聯繫客服',
    {
      confirmButtonText: '我知道了',
      type: 'info'
    }
  )
}

// 播放成功音效
const playSuccessSound = () => {
  try {
    const audio = new Audio('/sounds/payment-success.mp3')
    audio.volume = 0.3
    audio.play().catch(() => {
      // 忽略音頻播放失敗
    })
  } catch (error) {
    // 忽略音頻相關錯誤
  }
}

onMounted(() => {
  startCountdown()
})

onUnmounted(() => {
  stopCountdown()
})
</script>

<style scoped>
.payment-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.payment-progress {
  margin-bottom: 30px;
}

.payment-status-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.status-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.pending-icon {
  color: #f39c12;
}

.processing-icon {
  color: #3498db;
}

.success-icon {
  color: #27ae60;
}

.failed-icon {
  color: #e74c3c;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.countdown {
  margin-top: 20px;
  font-size: 18px;
}

.countdown-time {
  color: #e74c3c;
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

.smart-pay-btn {
  width: 100%;
  height: 60px;
  font-size: 18px;
  font-weight: bold;
  border-radius: 30px;
  background: linear-gradient(45deg, #1890ff, #36cfc9);
  border: none;
  transition: all 0.3s ease;
}

.smart-pay-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(24, 144, 255, 0.3);
}

.security-tips {
  display: flex;
  justify-content: space-around;
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.security-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 成功動畫 */
.success-animation {
  margin: 20px 0;
}

.checkmark {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: block;
  stroke-width: 3;
  stroke: #27ae60;
  stroke-miterlimit: 10;
  margin: 0 auto;
  box-shadow: inset 0px 0px 0px #27ae60;
  animation: fill 0.4s ease-in-out 0.4s forwards, scale 0.3s ease-in-out 0.9s both;
  position: relative;
}

.checkmark-circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 3;
  stroke-miterlimit: 10;
  stroke: #27ae60;
  fill: none;
  animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
}

@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes fill {
  100% {
    box-shadow: inset 0px 0px 0px 40px #27ae60;
  }
}

@keyframes scale {
  0%, 100% {
    transform: none;
  }
  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}
</style>
```

## 🎨 視覺設計優化

### 1. 響應式設計
- 移動端優先設計
- 自適應布局
- 觸摸友好的按鈕尺寸

### 2. 動畫效果
- 平滑的狀態轉換
- 加載動畫
- 成功/失敗反饋動畫

### 3. 色彩系統
- 一致的品牌色彩
- 清晰的狀態指示
- 高對比度文字

## 📱 移動端優化

### 1. 觸摸體驗
- 44px 最小觸摸目標
- 防誤觸設計
- 手勢支持

### 2. 性能優化
- 圖片懶加載
- 代碼分割
- 預加載關鍵資源

## 🔔 通知系統

### 1. 實時通知
- WebSocket 連接
- 支付狀態推送
- 瀏覽器通知

### 2. 多渠道通知
- 短信通知
- 郵件通知
- 站內消息

## 📊 預期效果

### 用戶滿意度
- **支付完成率**: 提升25%
- **用戶停留時間**: 增加40%
- **客服諮詢**: 減少60%

### 業務指標
- **支付轉化率**: 提升30%
- **用戶復購率**: 提升20%
- **品牌認知度**: 顯著提升
