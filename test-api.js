// 測試管理員 API 功能
const https = require('https');
const http = require('http');

// 創建 HTTP 請求函數
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(body)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: body
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testAdminAPI() {
  console.log('🚀 開始測試管理員 API...');
  
  try {
    // 1. 測試管理員登入
    console.log('📍 測試管理員登入...');
    const loginResponse = await makeRequest({
      hostname: 'localhost',
      port: 8080,
      path: '/api/admin/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, {
      username: 'admin',
      password: 'admin'
    });
    
    console.log('✅ 登入響應狀態:', loginResponse.status);
    console.log('📄 登入響應數據:', loginResponse.data);
    
    if (loginResponse.status === 200 && loginResponse.data.success) {
      const token = loginResponse.data.data.token;
      console.log('🔑 獲取到 Token:', token.substring(0, 20) + '...');
      
      // 2. 測試獲取待審核列表
      console.log('📋 測試獲取待審核列表...');
      const pendingResponse = await makeRequest({
        hostname: 'localhost',
        port: 8080,
        path: '/api/admin/identity/pending',
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('✅ 待審核列表響應狀態:', pendingResponse.status);
      console.log('📊 待審核列表數據:', pendingResponse.data);
      
      if (pendingResponse.status === 200 && pendingResponse.data.success) {
        const pendingList = pendingResponse.data.data;
        console.log('📈 待審核申請數量:', pendingList.length);
        
        if (pendingList.length > 0) {
          console.log('📝 第一個申請詳情:');
          console.log('  - 用戶名:', pendingList[0].username);
          console.log('  - 郵箱:', pendingList[0].email);
          console.log('  - 真實姓名:', pendingList[0].realName);
          console.log('  - 身份證號:', pendingList[0].idCardNumber);
          console.log('  - 提交時間:', pendingList[0].submittedAt);
        }
      }
      
      // 3. 測試獲取所有申請
      console.log('📋 測試獲取所有申請...');
      const allResponse = await makeRequest({
        hostname: 'localhost',
        port: 8080,
        path: '/api/admin/identity/all',
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('✅ 所有申請響應狀態:', allResponse.status);
      if (allResponse.status === 200 && allResponse.data.success) {
        console.log('📊 所有申請數量:', allResponse.data.data.length);
      }
      
    } else {
      console.error('❌ 管理員登入失敗');
    }
    
    console.log('✅ API 測試完成！');
    
  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testAdminAPI();
