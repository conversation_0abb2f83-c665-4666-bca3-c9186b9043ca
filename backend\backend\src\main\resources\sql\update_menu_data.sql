-- 更新菜單數據，添加商品相關菜單
-- 請在MySQL中手動執行此SQL

-- 清空現有菜單數據
DELETE FROM menus;

-- 重置自增ID
ALTER TABLE menus AUTO_INCREMENT = 1;

-- 插入新的菜單數據
INSERT INTO menus (name, path, icon, description, sort_order, enabled, menu_type, permission, parent_id) VALUES
-- 根菜單
('首頁', '/app/home', 'House', '系統首頁', 1, TRUE, 'MENU', NULL, NULL),
('用戶管理', '/app/user', 'User', '用戶相關功能', 2, TRUE, 'MENU', 'USER', NULL),
('商品管理', '/app/products', 'ShoppingBag', '商品相關功能', 3, TRUE, 'MENU', 'PRODUCT', NULL),
('系統管理', '/app/system', 'Setting', '系統管理功能', 4, TRUE, 'MENU', 'ADMIN', NULL);

-- 插入用戶管理子菜單
INSERT INTO menus (name, path, icon, description, sort_order, enabled, menu_type, permission, parent_id) VALUES
('個人資料', '/app/profile', 'UserFilled', '查看和編輯個人資料', 1, TRUE, 'MENU', NULL, 
  (SELECT id FROM (SELECT id FROM menus WHERE name = '用戶管理') AS temp)),
('身份認證', '/app/identity', 'CreditCard', '身份認證管理', 2, TRUE, 'MENU', NULL, 
  (SELECT id FROM (SELECT id FROM menus WHERE name = '用戶管理') AS temp)),
('關注管理', '/app/follow', 'Star', '關注和粉絲管理', 3, TRUE, 'MENU', NULL, 
  (SELECT id FROM (SELECT id FROM menus WHERE name = '用戶管理') AS temp)),
('我的收藏', '/app/my-favorites', 'Star', '我的收藏管理', 4, TRUE, 'MENU', NULL, 
  (SELECT id FROM (SELECT id FROM menus WHERE name = '用戶管理') AS temp));

-- 插入商品管理子菜單
INSERT INTO menus (name, path, icon, description, sort_order, enabled, menu_type, permission, parent_id) VALUES
('商品列表', '/app/products', 'Box', '瀏覽商品列表', 1, TRUE, 'MENU', NULL, 
  (SELECT id FROM (SELECT id FROM menus WHERE name = '商品管理') AS temp)),
('商品分類', '/app/products/categories', 'FolderOpened', '商品分類瀏覽', 2, TRUE, 'MENU', NULL, 
  (SELECT id FROM (SELECT id FROM menus WHERE name = '商品管理') AS temp)),
('商品搜索', '/app/products/search', 'Search', '搜索商品', 3, TRUE, 'MENU', NULL, 
  (SELECT id FROM (SELECT id FROM menus WHERE name = '商品管理') AS temp)),
('熱門商品', '/app/products/hot', 'Fire', '熱門商品', 4, TRUE, 'MENU', NULL, 
  (SELECT id FROM (SELECT id FROM menus WHERE name = '商品管理') AS temp)),
('推薦商品', '/app/products/recommended', 'Trophy', '推薦商品', 5, TRUE, 'MENU', NULL, 
  (SELECT id FROM (SELECT id FROM menus WHERE name = '商品管理') AS temp));

-- 插入系統管理子菜單
INSERT INTO menus (name, path, icon, description, sort_order, enabled, menu_type, permission, parent_id) VALUES
('菜單管理', '/app/system/menu', 'Menu', '系統菜單管理', 1, TRUE, 'MENU', 'ADMIN', 
  (SELECT id FROM (SELECT id FROM menus WHERE name = '系統管理') AS temp)),
('用戶審核', '/app/system/user-audit', 'View', '用戶身份認證審核', 2, TRUE, 'MENU', 'ADMIN', 
  (SELECT id FROM (SELECT id FROM menus WHERE name = '系統管理') AS temp)),
('系統設置', '/app/system/settings', 'Tools', '系統參數設置', 3, TRUE, 'MENU', 'ADMIN', 
  (SELECT id FROM (SELECT id FROM menus WHERE name = '系統管理') AS temp));

-- 檢查插入結果
SELECT * FROM menus ORDER BY parent_id, sort_order;