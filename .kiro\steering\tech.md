# 技术栈
## 前端
- **框架**：Vue 3，结合了 Composition API 和 `<script setup>` 语法
- **语言**：TypeScript
- **构建工具**：Vite
- **UI 库**：Element Plus
- **状态管理**：Pinia
- **HTTP 客户端**：Axios
- **路由**：Vue Router
## 后端
- **框架**：SpringBoot 3.2.12
- **语言**：Java 21
- **安全**：Spring Security + JWT
- **数据访问**：Spring Data JPA
- **数据库**：MySQL 8.0
- **缓存**：Redis 6.0+
- **电子邮件**：Spring Boot Starter Mail
- **文件上传**：多部分文件处理
## 开发工具
- **包管理器**：npm（前端），Maven（后端）
- **API 文档**：可在 `/swagger-ui/index.html` 地址处查看 Swagger UI 页面
## 常用指令
### 前端开发
```bash
cd frontend
npm 安装          # 安装依赖项
npm 运行开发模式  # 启动开发服务器（http://localhost：5173）
npm 构建         # 生成生产版本
npm 预览         # 预览生产版本构建结果
``````

### 后端开发
```bash
cd backend/backend
mvn spring-boot:run # 启动 SpringBoot 应用程序（地址：http://localhost：8080）
mvn clean compile   # 清理并编译代码
mvn test           # 运行测试用例```

### 数据库设置
```sql
创建数据库“user_auth_db”，字符集设置为 utf8mb4，排序规则设置为 utf8mb4_unicode_ci；
``````

### Redis 设置
```bash
redis-server        # 启动 Redis 服务器
``````

## 环境要求
- Java 21
- Node.js 18 及以上版本
- MySQL 8.0
- Redis 6.0 及以上版本
## 配置文件
- 后端：`backend/backend/src/main/resources/application.yml`
- 前端：`frontend/vite.config.ts`、`frontend/tsconfig.json`