package com.example.repository;

import com.example.entity.Payment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 支付記錄Repository
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Repository
public interface PaymentRepository extends JpaRepository<Payment, Long> {
    
    /**
     * 根據訂單ID查找支付記錄
     */
    Optional<Payment> findByOrderId(Long orderId);
    
    /**
     * 根據第三方交易號查找支付記錄
     */
    Optional<Payment> findByThirdPartyTradeNo(String thirdPartyTradeNo);
    
    /**
     * 根據支付狀態查詢支付記錄
     */
    Page<Payment> findByPaymentStatusOrderByCreatedAtDesc(Integer paymentStatus, Pageable pageable);
    
    /**
     * 根據支付方式查詢支付記錄
     */
    Page<Payment> findByPaymentMethodOrderByCreatedAtDesc(Integer paymentMethod, Pageable pageable);
    
    /**
     * 根據訂單ID查找支付記錄（預加載訂單信息）
     */
    @Query("SELECT p FROM Payment p LEFT JOIN FETCH p.order WHERE p.orderId = :orderId")
    Optional<Payment> findByOrderIdWithOrder(@Param("orderId") Long orderId);
    
    /**
     * 查詢指定時間範圍內的支付記錄
     */
    @Query("SELECT p FROM Payment p WHERE p.paidAt BETWEEN :startTime AND :endTime ORDER BY p.paidAt DESC")
    List<Payment> findByPaidAtBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查詢超時未支付的記錄
     */
    @Query("SELECT p FROM Payment p WHERE p.paymentStatus = 0 AND p.createdAt < :timeoutTime")
    List<Payment> findTimeoutUnpaidPayments(@Param("timeoutTime") LocalDateTime timeoutTime);
    
    /**
     * 統計支付成功的記錄數量
     */
    long countByPaymentStatus(Integer paymentStatus);
    
    /**
     * 統計各支付方式的使用次數
     */
    @Query("SELECT p.paymentMethod, COUNT(p) FROM Payment p WHERE p.paymentStatus = 1 GROUP BY p.paymentMethod")
    List<Object[]> countSuccessPaymentsByMethod();
    
    /**
     * 計算總支付金額
     */
    @Query("SELECT SUM(p.paymentAmount) FROM Payment p WHERE p.paymentStatus = 1")
    java.math.BigDecimal sumSuccessPaymentAmount();
    
    /**
     * 計算指定時間範圍內的支付金額
     */
    @Query("SELECT SUM(p.paymentAmount) FROM Payment p WHERE p.paymentStatus = 1 AND p.paidAt BETWEEN :startTime AND :endTime")
    java.math.BigDecimal sumPaymentAmountBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查詢用戶的支付記錄
     */
    @Query("SELECT p FROM Payment p JOIN p.order o WHERE o.userId = :userId ORDER BY p.createdAt DESC")
    Page<Payment> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, Pageable pageable);
    
    /**
     * 統計用戶的支付次數
     */
    @Query("SELECT COUNT(p) FROM Payment p JOIN p.order o WHERE o.userId = :userId AND p.paymentStatus = 1")
    long countSuccessPaymentsByUserId(@Param("userId") Long userId);
    
    /**
     * 計算用戶的總支付金額
     */
    @Query("SELECT SUM(p.paymentAmount) FROM Payment p JOIN p.order o WHERE o.userId = :userId AND p.paymentStatus = 1")
    java.math.BigDecimal sumPaymentAmountByUserId(@Param("userId") Long userId);
}
