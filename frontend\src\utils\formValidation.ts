import { ElMessage } from 'element-plus'

// 驗證規則類型
export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => boolean
  message: string
}

export interface ValidationRules {
  [key: string]: ValidationRule[]
}

// 常用驗證規則
export const commonRules = {
  // 必填驗證
  required: (message: string = '此項為必填項'): ValidationRule => ({
    required: true,
    message
  }),

  // 手機號驗證
  phone: (): ValidationRule => ({
    pattern: /^1[3-9]\d{9}$/,
    message: '請輸入正確的手機號碼'
  }),

  // 郵箱驗證
  email: (): ValidationRule => ({
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: '請輸入正確的郵箱地址'
  }),

  // 長度驗證
  length: (min: number, max: number, message?: string): ValidationRule => ({
    min,
    max,
    message: message || `長度應在 ${min} 到 ${max} 個字符之間`
  }),

  // 最小長度驗證
  minLength: (min: number, message?: string): ValidationRule => ({
    min,
    message: message || `最少需要 ${min} 個字符`
  }),

  // 最大長度驗證
  maxLength: (max: number, message?: string): ValidationRule => ({
    max,
    message: message || `最多允許 ${max} 個字符`
  }),

  // 數字驗證
  number: (message: string = '請輸入有效的數字'): ValidationRule => ({
    pattern: /^\d+(\.\d+)?$/,
    message
  }),

  // 整數驗證
  integer: (message: string = '請輸入整數'): ValidationRule => ({
    pattern: /^\d+$/,
    message
  }),

  // 身份證驗證
  idCard: (): ValidationRule => ({
    pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
    message: '請輸入正確的身份證號碼'
  }),

  // 自定義驗證
  custom: (validator: (value: any) => boolean, message: string): ValidationRule => ({
    validator,
    message
  })
}

// 表單驗證類
export class FormValidator {
  private rules: ValidationRules
  private errors: { [key: string]: string } = {}

  constructor(rules: ValidationRules) {
    this.rules = rules
  }

  // 驗證單個字段
  validateField(field: string, value: any): boolean {
    const fieldRules = this.rules[field]
    if (!fieldRules) return true

    for (const rule of fieldRules) {
      if (!this.checkRule(value, rule)) {
        this.errors[field] = rule.message
        return false
      }
    }

    delete this.errors[field]
    return true
  }

  // 驗證所有字段
  validate(data: { [key: string]: any }): boolean {
    let isValid = true
    this.errors = {}

    for (const field in this.rules) {
      const value = data[field]
      if (!this.validateField(field, value)) {
        isValid = false
      }
    }

    return isValid
  }

  // 檢查單個規則
  private checkRule(value: any, rule: ValidationRule): boolean {
    // 必填驗證
    if (rule.required && (value === null || value === undefined || value === '')) {
      return false
    }

    // 如果值為空且不是必填，跳過其他驗證
    if (!rule.required && (value === null || value === undefined || value === '')) {
      return true
    }

    // 最小長度驗證
    if (rule.min !== undefined && String(value).length < rule.min) {
      return false
    }

    // 最大長度驗證
    if (rule.max !== undefined && String(value).length > rule.max) {
      return false
    }

    // 正則驗證
    if (rule.pattern && !rule.pattern.test(String(value))) {
      return false
    }

    // 自定義驗證
    if (rule.validator && !rule.validator(value)) {
      return false
    }

    return true
  }

  // 獲取錯誤信息
  getErrors(): { [key: string]: string } {
    return { ...this.errors }
  }

  // 獲取單個字段錯誤
  getFieldError(field: string): string | undefined {
    return this.errors[field]
  }

  // 清除錯誤
  clearErrors(): void {
    this.errors = {}
  }

  // 清除單個字段錯誤
  clearFieldError(field: string): void {
    delete this.errors[field]
  }

  // 顯示錯誤消息
  showErrors(): void {
    const errorMessages = Object.values(this.errors)
    if (errorMessages.length > 0) {
      ElMessage.error(errorMessages[0])
    }
  }
}

// 預定義的表單驗證規則
export const formRules = {
  // 用戶註冊表單
  register: {
    username: [
      commonRules.required('請輸入用戶名'),
      commonRules.length(3, 20, '用戶名長度應在3-20個字符之間')
    ],
    email: [
      commonRules.required('請輸入郵箱'),
      commonRules.email()
    ],
    password: [
      commonRules.required('請輸入密碼'),
      commonRules.minLength(6, '密碼至少需要6個字符')
    ],
    confirmPassword: [
      commonRules.required('請確認密碼')
    ]
  },

  // 用戶登錄表單
  login: {
    username: [
      commonRules.required('請輸入用戶名或郵箱')
    ],
    password: [
      commonRules.required('請輸入密碼')
    ]
  },

  // 收貨地址表單
  address: {
    receiverName: [
      commonRules.required('請輸入收貨人姓名'),
      commonRules.length(2, 20, '姓名長度應在2-20個字符之間')
    ],
    receiverPhone: [
      commonRules.required('請輸入聯繫電話'),
      commonRules.phone()
    ],
    receiverAddress: [
      commonRules.required('請輸入詳細地址'),
      commonRules.minLength(5, '地址至少需要5個字符')
    ]
  },

  // 商品評價表單
  review: {
    rating: [
      commonRules.required('請選擇評分'),
      commonRules.custom((value) => value >= 1 && value <= 5, '評分應在1-5之間')
    ],
    content: [
      commonRules.required('請輸入評價內容'),
      commonRules.length(10, 500, '評價內容應在10-500個字符之間')
    ]
  }
}

// 實時驗證 Hook
export const useFormValidation = (rules: ValidationRules) => {
  const validator = new FormValidator(rules)
  
  return {
    validator,
    validateField: validator.validateField.bind(validator),
    validate: validator.validate.bind(validator),
    getErrors: validator.getErrors.bind(validator),
    getFieldError: validator.getFieldError.bind(validator),
    clearErrors: validator.clearErrors.bind(validator),
    clearFieldError: validator.clearFieldError.bind(validator),
    showErrors: validator.showErrors.bind(validator)
  }
}
