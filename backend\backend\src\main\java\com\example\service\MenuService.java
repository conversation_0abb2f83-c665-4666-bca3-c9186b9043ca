package com.example.service;

import com.example.dto.MenuDto;
import com.example.entity.Menu;
import com.example.entity.User;
import com.example.repository.MenuRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜單服務類
 * 提供菜單的CRUD操作和緩存管理
 */
@Service
@Slf4j
public class MenuService {
    
    @Autowired
    private MenuRepository menuRepository;
    
    @Autowired
    private RedisService redisService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 獲取完整菜單樹（帶緩存）
     */
    public List<MenuDto> getMenuTree() {
        try {
            // 先從緩存獲取
            String cachedMenuTree = redisService.getCachedMenuTree();
            if (cachedMenuTree != null) {
                log.debug("從緩存獲取菜單樹");
                return objectMapper.readValue(cachedMenuTree, 
                    objectMapper.getTypeFactory().constructCollectionType(List.class, MenuDto.class));
            }
            
            // 緩存未命中，從數據庫獲取
            log.debug("緩存未命中，從數據庫獲取菜單樹");
            List<MenuDto> menuTree = buildMenuTreeFromDatabase();
            
            // 緩存結果
            String menuTreeJson = objectMapper.writeValueAsString(menuTree);
            redisService.cacheMenuTree(menuTreeJson);
            
            return menuTree;
            
        } catch (JsonProcessingException e) {
            log.error("菜單樹JSON處理失敗", e);
            // 發生錯誤時直接從數據庫獲取
            return buildMenuTreeFromDatabase();
        }
    }
    
    /**
     * 獲取用戶可訪問的菜單樹（帶緩存）
     */
    public List<MenuDto> getUserMenuTree(User user) {
        try {
            // 先從緩存獲取
            String cachedUserMenuTree = redisService.getCachedUserMenuTree(user.getId());
            if (cachedUserMenuTree != null) {
                log.debug("從緩存獲取用戶菜單樹: {}", user.getUsername());
                return objectMapper.readValue(cachedUserMenuTree, 
                    objectMapper.getTypeFactory().constructCollectionType(List.class, MenuDto.class));
            }
            
            // 緩存未命中，構建用戶菜單樹
            log.debug("緩存未命中，構建用戶菜單樹: {}", user.getUsername());
            List<MenuDto> userMenuTree = buildUserMenuTree(user);
            
            // 緩存結果
            String menuTreeJson = objectMapper.writeValueAsString(userMenuTree);
            redisService.cacheUserMenuTree(user.getId(), menuTreeJson);
            
            return userMenuTree;
            
        } catch (JsonProcessingException e) {
            log.error("用戶菜單樹JSON處理失敗", e);
            return buildUserMenuTree(user);
        }
    }
    
    /**
     * 從數據庫構建完整菜單樹
     */
    private List<MenuDto> buildMenuTreeFromDatabase() {
        List<Menu> allMenus = menuRepository.findAllEnabled();
        return buildMenuTree(allMenus);
    }
    
    /**
     * 構建用戶專屬菜單樹（基於權限過濾）
     */
    private List<MenuDto> buildUserMenuTree(User user) {
        List<Menu> allMenus = menuRepository.findAllEnabled();
        
        // 根據用戶角色過濾菜單
        List<Menu> filteredMenus = filterMenusByUserRole(allMenus, user);
        
        return buildMenuTree(filteredMenus);
    }
    
    /**
     * 根據用戶角色過濾菜單
     */
    private List<Menu> filterMenusByUserRole(List<Menu> menus, User user) {
        return menus.stream()
            .filter(menu -> hasMenuPermission(menu, user))
            .collect(Collectors.toList());
    }
    
    /**
     * 檢查用戶是否有菜單權限
     */
    private boolean hasMenuPermission(Menu menu, User user) {
        // 如果菜單沒有設置權限，則所有用戶都可以訪問
        if (menu.getPermission() == null || menu.getPermission().trim().isEmpty()) {
            return true;
        }
        
        // 根據用戶角色檢查權限
        String userRole = user.getRole().name();
        String menuPermission = menu.getPermission();
        
        // 簡單的權限檢查邏輯，可以根據需要擴展
        if ("ADMIN".equals(userRole)) {
            return true; // 管理員可以訪問所有菜單
        }
        
        // 普通用戶只能訪問沒有特殊權限要求的菜單
        return !menuPermission.contains("ADMIN");
    }
    
    /**
     * 構建菜單樹結構
     */
    private List<MenuDto> buildMenuTree(List<Menu> menus) {
        // 將菜單按ID分組，便於查找
        Map<Long, Menu> menuMap = menus.stream()
            .collect(Collectors.toMap(Menu::getId, menu -> menu));
        
        // 找出所有根菜單
        List<Menu> rootMenus = menus.stream()
            .filter(Menu::isRoot)
            .sorted(Comparator.comparing(Menu::getSortOrder))
            .collect(Collectors.toList());
        
        // 遞歸構建菜單樹
        return rootMenus.stream()
            .map(rootMenu -> buildMenuDtoWithChildren(rootMenu, menuMap))
            .collect(Collectors.toList());
    }
    
    /**
     * 遞歸構建菜單DTO及其子菜單
     */
    private MenuDto buildMenuDtoWithChildren(Menu menu, Map<Long, Menu> menuMap) {
        MenuDto menuDto = MenuDto.fromEntity(menu);
        
        // 查找並添加子菜單
        List<Menu> children = menuMap.values().stream()
            .filter(m -> Objects.equals(m.getParentId(), menu.getId()))
            .sorted(Comparator.comparing(Menu::getSortOrder))
            .collect(Collectors.toList());
        
        for (Menu child : children) {
            MenuDto childDto = buildMenuDtoWithChildren(child, menuMap);
            menuDto.getChildren().add(childDto);
        }
        
        return menuDto;
    }
    
    /**
     * 創建菜單
     */
    @Transactional
    public MenuDto createMenu(MenuDto menuDto) {
        // 驗證菜單數據
        validateMenuData(menuDto);
        
        // 設置排序順序
        if (menuDto.getSortOrder() == null) {
            Integer maxOrder = menuDto.getParentId() != null ? 
                menuRepository.findMaxSortOrderByParentId(menuDto.getParentId()) :
                menuRepository.findMaxSortOrderForRoot();
            menuDto.setSortOrder(maxOrder + 1);
        }
        
        // 保存菜單
        Menu menu = menuDto.toEntity();
        Menu savedMenu = menuRepository.save(menu);
        
        // 清除緩存
        clearMenuCache();
        
        log.info("創建菜單成功: {}", savedMenu.getName());
        return MenuDto.fromEntity(savedMenu);
    }
    
    /**
     * 更新菜單
     */
    @Transactional
    public MenuDto updateMenu(Long id, MenuDto menuDto) {
        Menu existingMenu = menuRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("菜單不存在"));
        
        // 驗證菜單數據
        validateMenuData(menuDto);
        
        // 更新菜單信息
        existingMenu.setName(menuDto.getName());
        existingMenu.setPath(menuDto.getPath());
        existingMenu.setIcon(menuDto.getIcon());
        existingMenu.setDescription(menuDto.getDescription());
        existingMenu.setSortOrder(menuDto.getSortOrder());
        existingMenu.setEnabled(menuDto.getEnabled());
        existingMenu.setMenuType(menuDto.getMenuType());
        existingMenu.setPermission(menuDto.getPermission());
        existingMenu.setParentId(menuDto.getParentId());
        
        Menu updatedMenu = menuRepository.save(existingMenu);
        
        // 清除緩存
        clearMenuCache();
        
        log.info("更新菜單成功: {}", updatedMenu.getName());
        return MenuDto.fromEntity(updatedMenu);
    }
    
    /**
     * 刪除菜單
     */
    @Transactional
    public void deleteMenu(Long id) {
        Menu menu = menuRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("菜單不存在"));
        
        // 檢查是否有子菜單
        List<Menu> children = menuRepository.findChildrenByParentId(id);
        if (!children.isEmpty()) {
            throw new RuntimeException("存在子菜單，無法刪除");
        }
        
        menuRepository.delete(menu);
        
        // 清除緩存
        clearMenuCache();
        
        log.info("刪除菜單成功: {}", menu.getName());
    }
    
    /**
     * 根據ID獲取菜單
     */
    public MenuDto getMenuById(Long id) {
        Menu menu = menuRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("菜單不存在"));
        return MenuDto.fromEntity(menu);
    }
    
    /**
     * 驗證菜單數據
     */
    private void validateMenuData(MenuDto menuDto) {
        if (menuDto.getName() == null || menuDto.getName().trim().isEmpty()) {
            throw new RuntimeException("菜單名稱不能為空");
        }
        
        // 檢查同一父菜單下是否有重複名稱
        Long excludeId = menuDto.getId() != null ? menuDto.getId() : -1L;
        boolean nameExists = menuDto.getParentId() != null ?
            menuRepository.existsByNameAndParentIdAndIdNot(menuDto.getName(), menuDto.getParentId(), excludeId) :
            menuRepository.existsByNameAndParentIdIsNullAndIdNot(menuDto.getName(), excludeId);
        
        if (nameExists) {
            throw new RuntimeException("同一層級下菜單名稱不能重複");
        }
    }
    
    /**
     * 清除所有菜單緩存
     */
    public void clearMenuCache() {
        redisService.clearAllMenuCache();
        log.info("清除菜單緩存完成");
    }
    
    /**
     * 預熱菜單緩存
     */
    public void warmUpMenuCache() {
        log.info("開始預熱菜單緩存");
        getMenuTree(); // 這會觸發緩存
        log.info("菜單緩存預熱完成");
    }
}
