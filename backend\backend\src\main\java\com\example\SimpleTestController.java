package com.example;

import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/simple")
@CrossOrigin(origins = "*")
public class SimpleTestController {
    
    @GetMapping("/hello")
    public String hello() {
        return "Hello World! SpringBoot is running!";
    }
    
    @PostMapping("/test-email")
    public String testEmail(@RequestParam String email) {
        try {
            // 使用之前測試成功的郵件發送代碼
            org.springframework.mail.javamail.JavaMailSenderImpl mailSender = 
                new org.springframework.mail.javamail.JavaMailSenderImpl();
            mailSender.setHost("smtp.gmail.com");
            mailSender.setPort(587);
            mailSender.setUsername("<EMAIL>");
            mailSender.setPassword("pddi avwc nxuw eqam");
            
            java.util.Properties props = mailSender.getJavaMailProperties();
            props.put("mail.transport.protocol", "smtp");
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");
            
            org.springframework.mail.SimpleMailMessage message = 
                new org.springframework.mail.SimpleMailMessage();
            message.setFrom("<EMAIL>");
            message.setTo(email);
            message.setSubject("測試郵件 - SpringBoot API");
            message.setText("這是通過 SpringBoot API 發送的測試郵件！\n\n驗證碼：" + 
                          generateCode() + "\n\n驗證碼5分鐘內有效。");
            
            mailSender.send(message);
            return "郵件發送成功到: " + email;
            
        } catch (Exception e) {
            return "郵件發送失敗: " + e.getMessage();
        }
    }
    
    private String generateCode() {
        return String.valueOf((int)(Math.random() * 900000) + 100000);
    }
}
