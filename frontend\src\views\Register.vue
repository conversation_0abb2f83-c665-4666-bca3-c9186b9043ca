<template>
  <div class="register-container">
    <div class="register-content">
      <div class="left-section">
        <div class="brand">
          <h1>歡迎加入我們</h1>
          <p class="slogan">創建您的帳戶，開啟全新體驗</p>
        </div>
        <div class="features">
          <div class="feature">
            <el-icon class="feature-icon"><Document /></el-icon>
            <div class="feature-text">
              <h3>無縫體驗</h3>
              <p>享受流暢的用戶界面和直觀的操作體驗</p>
            </div>
          </div>
          <div class="feature">
            <el-icon class="feature-icon"><Lock /></el-icon>
            <div class="feature-text">
              <h3>安全可靠</h3>
              <p>您的資料安全是我們的首要任務</p>
            </div>
          </div>
          <div class="feature">
            <el-icon class="feature-icon"><Connection /></el-icon>
            <div class="feature-text">
              <h3>隨時連接</h3>
              <p>無論您身在何處，都能隨時使用我們的服務</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="right-section">
        <div class="form-header">
          <h2>用戶註冊</h2>
          <p class="subtitle">填寫以下信息創建您的帳戶</p>
        </div>
        
        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          label-position="top"
          @submit.prevent="handleRegister"
          class="register-form"
        >
          <el-form-item label="用戶名" prop="username">
            <el-input
              v-model="registerForm.username"
              placeholder="請輸入用戶名"
              :prefix-icon="User"
              @blur="checkUsername"
            />
          </el-form-item>
          
          <el-form-item label="郵箱" prop="email">
            <el-input
              v-model="registerForm.email"
              placeholder="請輸入郵箱"
              :prefix-icon="Message"
              @blur="checkEmail"
            />
          </el-form-item>
          
          <el-form-item label="驗證碼" prop="verificationCode">
            <div class="verification-input">
              <el-input
                v-model="registerForm.verificationCode"
                placeholder="請輸入驗證碼"
                :prefix-icon="Key"
              />
              <el-button
                :disabled="!canSendCode || sendCodeLoading"
                :loading="sendCodeLoading"
                type="primary"
                @click="sendVerificationCode"
                class="verification-btn"
              >
                {{ sendCodeText }}
              </el-button>
            </div>
          </el-form-item>
          
          <el-form-item label="密碼" prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="請輸入密碼"
              :prefix-icon="Lock"
              show-password
            />
          </el-form-item>
          
          <el-form-item label="確認密碼" prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="請再次輸入密碼"
              :prefix-icon="Lock"
              show-password
            />
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              :loading="userStore.loading"
              @click="handleRegister"
              class="submit-btn"
            >
              創建帳戶
            </el-button>
          </el-form-item>
          
          <div class="form-footer">
            <p>已有帳戶？ <router-link to="/login" class="login-link">立即登入</router-link></p>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { User, Lock, Message, Key, Document, Connection } from '@element-plus/icons-vue'
import { useUserStore } from '../stores/user'
import { authAPI } from '../api'

const router = useRouter()
const userStore = useUserStore()

const registerFormRef = ref<FormInstance>()
const sendCodeLoading = ref(false)
const countdown = ref(0)
const timer = ref<ReturnType<typeof setInterval>>()

const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  verificationCode: ''
})

const canSendCode = computed(() => {
  return registerForm.email && countdown.value === 0
})

const sendCodeText = computed(() => {
  return countdown.value > 0 ? `${countdown.value}秒後重試` : '發送驗證碼'
})

const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (value !== registerForm.password) {
    callback(new Error('兩次輸入的密碼不一致'))
  } else {
    callback()
  }
}

const registerRules: FormRules = {
  username: [
    { required: true, message: '請輸入用戶名', trigger: 'blur' },
    { min: 3, max: 20, message: '用戶名長度在 3 到 20 個字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '請輸入郵箱', trigger: 'blur' },
    { type: 'email', message: '請輸入正確的郵箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '請輸入密碼', trigger: 'blur' },
    { min: 6, max: 20, message: '密碼長度在 6 到 20 個字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '請確認密碼', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  verificationCode: [
    { required: true, message: '請輸入驗證碼', trigger: 'blur' }
  ]
}

const checkUsername = async () => {
  if (!registerForm.username) return
  
  try {
    const response = await authAPI.checkUsername(registerForm.username)
    if (!response.data) {
      ElMessage.warning('用戶名已存在')
    }
  } catch (error) {
    console.error('檢查用戶名失敗:', error)
  }
}

const checkEmail = async () => {
  if (!registerForm.email) return
  
  try {
    const response = await authAPI.checkEmail(registerForm.email)
    if (!response.data) {
      ElMessage.warning('郵箱已被註冊')
    }
  } catch (error) {
    console.error('檢查郵箱失敗:', error)
  }
}

const sendVerificationCode = async () => {
  if (!registerForm.email) {
    ElMessage.warning('請先輸入郵箱')
    return
  }
  
  sendCodeLoading.value = true
  
  try {
    const response = await authAPI.sendVerificationCode({
      email: registerForm.email,
      type: 'REGISTRATION'
    })
    
    // 檢查響應數據結構
    const data = response as any
    if (data.success) {
      ElMessage.success('驗證碼發送成功')
      startCountdown()
    } else {
      ElMessage.error(data.message || '發送失敗')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '發送失敗')
  } finally {
    sendCodeLoading.value = false
  }
}

const startCountdown = () => {
  countdown.value = 60
  timer.value = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer.value)
    }
  }, 1000)
}

onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})

const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  await registerFormRef.value.validate(async (valid) => {
    if (valid) {
      const result = await userStore.register({
        username: registerForm.username,
        email: registerForm.email,
        password: registerForm.password,
        verificationCode: registerForm.verificationCode
      })
      
      if (result.success) {
        ElMessage.success('註冊成功')
        router.push('/app/home')
      } else {
        ElMessage.error(result.message || '註冊失敗')
      }
    }
  })
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #4b79a1 0%, #283e51 100%);
  padding: 0;
  margin: 0;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.register-content {
  display: flex;
  width: 100%;
  height: 100%;
  max-width: none;
  min-height: auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.left-section {
  flex: 1;
  background: linear-gradient(135deg, #3a7bd5 0%, #00d2ff 100%);
  color: white;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.brand {
  margin-bottom: 2rem;
}

.brand h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.slogan {
  font-size: 1.1rem;
  opacity: 0.9;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.feature {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.feature-icon {
  font-size: 1.8rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 0.8rem;
}

.feature-text h3 {
  font-size: 1.1rem;
  margin: 0 0 0.5rem 0;
}

.feature-text p {
  font-size: 0.9rem;
  opacity: 0.8;
  margin: 0;
}

.right-section {
  flex: 1;
  padding: 3rem;
  display: flex;
  flex-direction: column;
}

.form-header {
  margin-bottom: 2rem;
  text-align: center;
}

.form-header h2 {
  font-size: 2rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: #666;
  font-size: 1rem;
}

.register-form {
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
}

.verification-input {
  display: flex;
  gap: 10px;
}

.verification-input .el-input {
  flex: 1;
}

.verification-btn {
  white-space: nowrap;
  min-width: 120px;
}

.submit-btn {
  width: 100%;
  height: 48px;
  font-size: 1rem;
  font-weight: 500;
  margin-top: 1rem;
}

.form-footer {
  text-align: center;
  margin-top: 1.5rem;
  color: #666;
}

.login-link {
  color: #3a7bd5;
  font-weight: 500;
  text-decoration: none;
}

.login-link:hover {
  text-decoration: underline;
}

/* 響應式設計 */
@media (max-width: 992px) {
  .register-content {
    flex-direction: column;
    max-width: 600px;
  }
  
  .left-section {
    padding: 2rem;
  }
  
  .right-section {
    padding: 2rem;
  }
}

@media (max-width: 576px) {
  .register-container {
    padding: 10px;
  }
  
  .left-section {
    padding: 1.5rem;
  }
  
  .right-section {
    padding: 1.5rem;
  }
  
  .brand h1 {
    font-size: 2rem;
  }
  
  .form-header h2 {
    font-size: 1.8rem;
  }
  
  .verification-input {
    flex-direction: column;
  }
  
  .verification-btn {
    width: 100%;
  }
}

/* 針對大型螢幕 (2560x1440) 的優化 */
@media (min-width: 1920px) {
  .register-content {
    max-width: none;
    min-height: auto;
  }
  
  .left-section, .right-section {
    padding: 4rem;
  }
  
  .brand h1 {
    font-size: 3.5rem;
  }
  
  .slogan {
    font-size: 1.4rem;
  }
  
  .feature-icon {
    font-size: 2.2rem;
    padding: 1rem;
  }
  
  .feature-text h3 {
    font-size: 1.4rem;
  }
  
  .feature-text p {
    font-size: 1.1rem;
  }
  
  .form-header h2 {
    font-size: 2.5rem;
  }
  
  .subtitle {
    font-size: 1.2rem;
  }
  
  .register-form {
    max-width: 550px;
  }
  
  .el-form-item__label {
    font-size: 1.1rem;
  }
  
  .el-input__inner {
    font-size: 1.1rem;
    height: 52px;
  }
  
  .submit-btn {
    height: 56px;
    font-size: 1.2rem;
  }
}
</style>
