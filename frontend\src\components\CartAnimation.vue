<template>
  <div class="cart-animation">
    <!-- 添加到購物車動畫 -->
    <transition name="cart-fly">
      <div 
        v-if="showFlyAnimation" 
        class="flying-item"
        :style="flyingItemStyle"
      >
        <img :src="flyingItemImage" :alt="flyingItemName" />
      </div>
    </transition>

    <!-- 購物車圖標動畫 -->
    <div 
      ref="cartIconRef"
      class="cart-icon-container"
      :class="{ shake: showShakeAnimation }"
    >
      <el-badge :value="cartCount" :hidden="cartCount === 0">
        <el-icon class="cart-icon"><ShoppingCart /></el-icon>
      </el-badge>
    </div>

    <!-- 數量變化動畫 -->
    <transition name="quantity-change">
      <div v-if="showQuantityChange" class="quantity-change">
        {{ quantityChangeText }}
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { ShoppingCart } from '@element-plus/icons-vue'

// Props
interface Props {
  cartCount: number
}

const props = defineProps<Props>()

// 響應式數據
const showFlyAnimation = ref(false)
const showShakeAnimation = ref(false)
const showQuantityChange = ref(false)
const cartIconRef = ref<HTMLElement>()

const flyingItemImage = ref('')
const flyingItemName = ref('')
const flyingItemStartX = ref(0)
const flyingItemStartY = ref(0)
const quantityChangeText = ref('')

// 計算屬性
const flyingItemStyle = computed(() => ({
  left: `${flyingItemStartX.value}px`,
  top: `${flyingItemStartY.value}px`,
}))

// 方法
const playAddToCartAnimation = async (
  itemImage: string,
  itemName: string,
  startElement: HTMLElement
) => {
  if (!cartIconRef.value) return

  // 設置飛行物品信息
  flyingItemImage.value = itemImage
  flyingItemName.value = itemName

  // 獲取起始位置
  const startRect = startElement.getBoundingClientRect()
  flyingItemStartX.value = startRect.left + startRect.width / 2
  flyingItemStartY.value = startRect.top + startRect.height / 2

  // 開始飛行動畫
  showFlyAnimation.value = true

  // 等待動畫完成
  await new Promise(resolve => setTimeout(resolve, 800))

  // 隱藏飛行動畫
  showFlyAnimation.value = false

  // 播放購物車震動動畫
  showShakeAnimation.value = true
  setTimeout(() => {
    showShakeAnimation.value = false
  }, 600)

  // 顯示數量變化
  quantityChangeText.value = '+1'
  showQuantityChange.value = true
  setTimeout(() => {
    showQuantityChange.value = false
  }, 1000)
}

const playQuantityChangeAnimation = (change: number) => {
  quantityChangeText.value = change > 0 ? `+${change}` : `${change}`
  showQuantityChange.value = true
  setTimeout(() => {
    showQuantityChange.value = false
  }, 1000)
}

const playCartShakeAnimation = () => {
  showShakeAnimation.value = true
  setTimeout(() => {
    showShakeAnimation.value = false
  }, 600)
}

// 暴露方法
defineExpose({
  playAddToCartAnimation,
  playQuantityChangeAnimation,
  playCartShakeAnimation
})
</script>

<style scoped>
.cart-animation {
  position: relative;
}

.flying-item {
  position: fixed;
  width: 60px;
  height: 60px;
  z-index: 2000;
  pointer-events: none;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.flying-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cart-icon-container {
  position: relative;
  display: inline-block;
}

.cart-icon {
  font-size: 24px;
  color: #409eff;
  transition: all 0.3s ease;
}

.cart-icon-container.shake {
  animation: shake 0.6s ease-in-out;
}

.quantity-change {
  position: absolute;
  top: -10px;
  right: -10px;
  background: #67c23a;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  z-index: 10;
}

/* 動畫定義 */
.cart-fly-enter-active {
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.cart-fly-enter-from {
  transform: scale(1) translate(0, 0);
  opacity: 1;
}

.cart-fly-enter-to {
  transform: scale(0.3) translate(200px, -100px);
  opacity: 0;
}

.quantity-change-enter-active {
  transition: all 0.3s ease;
}

.quantity-change-leave-active {
  transition: all 0.7s ease;
}

.quantity-change-enter-from {
  transform: scale(0) translateY(10px);
  opacity: 0;
}

.quantity-change-enter-to {
  transform: scale(1) translateY(0);
  opacity: 1;
}

.quantity-change-leave-from {
  transform: scale(1) translateY(0);
  opacity: 1;
}

.quantity-change-leave-to {
  transform: scale(0.8) translateY(-20px);
  opacity: 0;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/* 響應式設計 */
@media (max-width: 768px) {
  .flying-item {
    width: 40px;
    height: 40px;
  }
  
  .cart-icon {
    font-size: 20px;
  }
  
  .quantity-change {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
}
</style>
