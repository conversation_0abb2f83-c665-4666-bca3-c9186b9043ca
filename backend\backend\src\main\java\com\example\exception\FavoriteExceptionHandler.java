package com.example.exception;

import com.example.dto.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 收藏功能全局异常处理器
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@RestControllerAdvice
public class FavoriteExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(FavoriteExceptionHandler.class);
    
    /**
     * 处理收藏记录不存在异常
     */
    @ExceptionHandler(FavoriteNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleFavoriteNotFoundException(FavoriteNotFoundException e) {
        logger.warn("收藏记录不存在: {}", e.getMessage());
        
        ApiResponse<Object> response = createErrorResponse(e.getMessage(), e.getErrorCode(), HttpStatus.NOT_FOUND);
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }
    
    /**
     * 处理重复收藏异常
     */
    @ExceptionHandler(DuplicateFavoriteException.class)
    public ResponseEntity<ApiResponse<Object>> handleDuplicateFavoriteException(DuplicateFavoriteException e) {
        logger.warn("重复收藏: {}", e.getMessage());
        
        ApiResponse<Object> response = createErrorResponse(e.getMessage(), e.getErrorCode(), HttpStatus.CONFLICT);
        return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
    }
    
    /**
     * 处理操作频率超限异常
     */
    @ExceptionHandler(RateLimitExceededException.class)
    public ResponseEntity<ApiResponse<Object>> handleRateLimitExceededException(RateLimitExceededException e) {
        logger.warn("操作频率超限: {}", e.getMessage());
        
        ApiResponse<Object> response = createErrorResponse(e.getMessage(), e.getErrorCode(), HttpStatus.TOO_MANY_REQUESTS);
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).body(response);
    }
    
    /**
     * 处理无权限操作异常
     */
    @ExceptionHandler(UnauthorizedFavoriteException.class)
    public ResponseEntity<ApiResponse<Object>> handleUnauthorizedFavoriteException(UnauthorizedFavoriteException e) {
        logger.warn("无权限操作: {}", e.getMessage());
        
        ApiResponse<Object> response = createErrorResponse(e.getMessage(), e.getErrorCode(), HttpStatus.FORBIDDEN);
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
    }
    
    /**
     * 处理通用收藏异常
     */
    @ExceptionHandler(FavoriteException.class)
    public ResponseEntity<ApiResponse<Object>> handleFavoriteException(FavoriteException e) {
        logger.error("收藏操作异常: {}", e.getMessage(), e);
        
        ApiResponse<Object> response = createErrorResponse(e.getMessage(), e.getErrorCode(), HttpStatus.BAD_REQUEST);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }
    
    /**
     * 创建错误响应
     */
    private ApiResponse<Object> createErrorResponse(String message, String errorCode, HttpStatus status) {
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("errorCode", errorCode);
        errorDetails.put("timestamp", LocalDateTime.now());
        errorDetails.put("status", status.value());
        
        ApiResponse<Object> response = new ApiResponse<>();
        response.setSuccess(false);
        response.setMessage(message);
        response.setData(errorDetails);
        
        return response;
    }
}
