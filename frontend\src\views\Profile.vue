<template>
  <div class="profile-container">
    <!-- 頂部導航 -->
    <el-header class="header">
      <div class="header-content">
        <el-button @click="$router.go(-1)" link style="color: white;">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1>個人資料</h1>
        <div></div>
      </div>
    </el-header>

    <el-main class="main-content">
      <div class="profile-content">
        <el-row :gutter="20">
          <!-- 基本信息 -->
          <el-col :span="16">
            <el-card>
              <template #header>
                <h3>基本信息</h3>
              </template>
              
              <el-form
                ref="profileFormRef"
                :model="profileForm"
                :rules="profileRules"
                label-width="100px"
              >
                <el-form-item label="用戶名">
                  <el-input v-model="profileForm.username" disabled />
                </el-form-item>
                
                <el-form-item label="郵箱">
                  <el-input v-model="profileForm.email" disabled>
                    <template #suffix>
                      <el-tag v-if="profileForm.emailVerified" type="success" size="small">
                        已驗證
                      </el-tag>
                      <el-tag v-else type="warning" size="small">
                        未驗證
                      </el-tag>
                    </template>
                  </el-input>
                </el-form-item>
                
                <el-form-item label="真實姓名" prop="realName">
                  <el-input 
                    v-model="profileForm.realName" 
                    placeholder="請輸入真實姓名"
                    :disabled="!canEditProfile"
                  />
                </el-form-item>
                
                <el-form-item label="手機號碼" prop="phoneNumber">
                  <el-input 
                    v-model="profileForm.phoneNumber" 
                    placeholder="請輸入手機號碼"
                    :disabled="!canEditProfile"
                  />
                </el-form-item>
                
                <el-form-item label="註冊時間">
                  <el-input :value="formatDate(profileForm.createdAt)" disabled />
                </el-form-item>
                
                <el-form-item v-if="canEditProfile">
                  <el-button type="primary" @click="updateProfile" :loading="updateLoading">
                    保存修改
                  </el-button>
                  <el-button @click="resetForm">
                    重置
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
          
          <!-- 狀態信息 -->
          <el-col :span="8">
            <el-card>
              <template #header>
                <h3>帳戶狀態</h3>
              </template>
              
              <div class="status-list">
                <div class="status-item">
                  <span class="status-label">郵箱驗證：</span>
                  <el-tag :type="profileForm.emailVerified ? 'success' : 'warning'">
                    {{ profileForm.emailVerified ? '已驗證' : '未驗證' }}
                  </el-tag>
                </div>
                
                <div class="status-item">
                  <span class="status-label">身份認證：</span>
                  <el-tag :type="getIdentityTagType()">
                    {{ getIdentityStatusText() }}
                  </el-tag>
                </div>
                
                <div class="status-item">
                  <span class="status-label">用戶角色：</span>
                  <el-tag type="info">
                    {{ profileForm.role === 'ADMIN' ? '管理員' : '普通用戶' }}
                  </el-tag>
                </div>
                
                <div class="status-item">
                  <span class="status-label">帳戶狀態：</span>
                  <el-tag type="success">正常</el-tag>
                </div>
              </div>
              
              <el-divider />
              
              <div class="action-buttons">
                <el-button 
                  type="primary" 
                  @click="$router.push('/app/identity')"
                  style="width: 100%; margin-bottom: 10px;"
                >
                  <el-icon><CreditCard /></el-icon>
                  身份認證
                </el-button>
                
                <el-button 
                  type="success" 
                  @click="$router.push('/app/home')"
                  style="width: 100%;"
                >
                  <el-icon><House /></el-icon>
                  返回首頁
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-main>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { 
  ArrowLeft, 
  CreditCard, 
  House 
} from '@element-plus/icons-vue'
import { useUserStore } from '../stores/user'

const userStore = useUserStore()
const profileFormRef = ref<FormInstance>()
const updateLoading = ref(false)

const profileForm = reactive({
  username: '',
  email: '',
  realName: '',
  phoneNumber: '',
  emailVerified: false,
  identityVerified: false,
  identityStatus: 'NOT_SUBMITTED' as const,
  role: 'USER' as const,
  createdAt: ''
})

// 是否可以編輯個人資料（身份認證通過後不能修改真實姓名）
const canEditProfile = computed(() => {
  return profileForm.identityStatus !== 'APPROVED'
})

const profileRules: FormRules = {
  realName: [
    { max: 20, message: '真實姓名不能超過20個字符', trigger: 'blur' }
  ],
  phoneNumber: [
    { pattern: /^1[3-9]\d{9}$/, message: '請輸入正確的手機號碼', trigger: 'blur' }
  ]
}

onMounted(() => {
  loadUserProfile()
})

const loadUserProfile = () => {
  if (userStore.user) {
    Object.assign(profileForm, userStore.user)
  }
}

const getIdentityTagType = () => {
  switch (profileForm.identityStatus) {
    case 'APPROVED': return 'success'
    case 'REJECTED': return 'danger'
    case 'PENDING': return 'warning'
    default: return 'info'
  }
}

const getIdentityStatusText = () => {
  switch (profileForm.identityStatus) {
    case 'NOT_SUBMITTED': return '未提交'
    case 'PENDING': return '待審核'
    case 'APPROVED': return '已通過'
    case 'REJECTED': return '已拒絕'
    default: return '未知'
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

const updateProfile = async () => {
  if (!profileFormRef.value) return
  
  await profileFormRef.value.validate(async (valid) => {
    if (valid) {
      updateLoading.value = true
      
      try {
        const result = await userStore.updateProfile({
          realName: profileForm.realName,
          phoneNumber: profileForm.phoneNumber
        })
        
        if (result.success) {
          ElMessage.success('個人資料更新成功')
          loadUserProfile() // 重新加載用戶信息
        } else {
          ElMessage.error(result.message || '更新失敗')
        }
      } catch (error) {
        ElMessage.error('更新失敗')
      } finally {
        updateLoading.value = false
      }
    }
  })
}

const resetForm = () => {
  loadUserProfile()
  ElMessage.info('已重置為原始數據')
}
</script>

<style scoped>
.profile-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.header-content h1 {
  margin: 0;
  font-size: 24px;
}

.main-content {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.profile-content {
  margin-top: 20px;
}

.status-list {
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.status-label {
  font-weight: 500;
  color: #333;
}

.action-buttons {
  margin-top: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-card__header) {
  background-color: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__header h3) {
  margin: 0;
  color: #333;
  font-size: 16px;
}
</style>
