// 直接測試支付寶 API 的腳本
// 在瀏覽器控制台中運行

async function testAlipayPayment() {
    try {
        // 獲取當前用戶的 token
        const token = localStorage.getItem('token');
        if (!token) {
            console.error('未找到認證 token，請先登錄');
            return;
        }
        
        console.log('使用 token:', token.substring(0, 20) + '...');
        
        // 發起支付請求
        const orderId = 3; // 當前訂單 ID
        const response = await fetch(`/api/payment/alipay/create?orderId=${orderId}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log('響應狀態:', response.status);
        console.log('響應頭:', Object.fromEntries(response.headers.entries()));
        
        const result = await response.json();
        console.log('響應結果:', result);
        
        if (result.success && result.data) {
            console.log('支付表單 HTML:', result.data);
            
            // 創建並提交支付表單
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'about:blank';
            form.target = '_blank';
            form.innerHTML = result.data;
            
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
            
            console.log('支付頁面已在新窗口打開');
        } else {
            console.error('支付失敗:', result.message);
        }
        
    } catch (error) {
        console.error('請求失敗:', error);
    }
}

// 執行測試
console.log('開始測試支付寶支付...');
testAlipayPayment();
