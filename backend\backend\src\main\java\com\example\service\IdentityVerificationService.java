package com.example.service;

import com.example.dto.IdentityVerificationRequest;
import com.example.entity.IdentityVerification;
import com.example.entity.User;
import com.example.repository.IdentityVerificationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
public class IdentityVerificationService {
    
    @Autowired
    private IdentityVerificationRepository identityVerificationRepository;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private FileService fileService;
    
    /**
     * 提交身份認證
     */
    @Transactional
    public IdentityVerification submitIdentityVerification(
            User user, 
            IdentityVerificationRequest request,
            MultipartFile frontImage,
            MultipartFile backImage,
            boolean useExistingFrontImage,
            boolean useExistingBackImage) {
        
        // 檢查是否已有待審核的申請
        Optional<IdentityVerification> existingPending = 
            identityVerificationRepository.findByUserAndStatus(user, IdentityVerification.Status.PENDING);
        
        if (existingPending.isPresent()) {
            throw new RuntimeException("您已有待審核的身份認證申請，請等待審核結果");
        }
        
        // 檢查身份證號是否已被其他用戶使用
        if (identityVerificationRepository.existsByIdCardNumberAndUserIdNot(request.getIdCardNumber(), user.getId())) {
            throw new RuntimeException("該身份證號已被使用");
        }
        
        try {
            String frontImageUrl = null;
            String backImageUrl = null;
            
            // 處理正面照片
            if (frontImage != null && !frontImage.isEmpty()) {
                // 上傳新照片
                frontImageUrl = fileService.uploadFile(frontImage, "identity");
            } else if (useExistingFrontImage && user.getIdCardFrontUrl() != null) {
                // 使用現有照片
                frontImageUrl = user.getIdCardFrontUrl();
            } else {
                throw new RuntimeException("請上傳身份證正面照片");
            }
            
            // 處理反面照片
            if (backImage != null && !backImage.isEmpty()) {
                // 上傳新照片
                backImageUrl = fileService.uploadFile(backImage, "identity");
            } else if (useExistingBackImage && user.getIdCardBackUrl() != null) {
                // 使用現有照片
                backImageUrl = user.getIdCardBackUrl();
            } else {
                throw new RuntimeException("請上傳身份證反面照片");
            }
            
            // 創建身份認證記錄
            IdentityVerification verification = new IdentityVerification();
            verification.setUser(user);
            verification.setRealName(request.getRealName());
            verification.setIdCardNumber(request.getIdCardNumber());
            verification.setIdCardFrontUrl(frontImageUrl);
            verification.setIdCardBackUrl(backImageUrl);
            verification.setStatus(IdentityVerification.Status.PENDING);
            
            IdentityVerification saved = identityVerificationRepository.save(verification);
            
            // 更新用戶狀態
            user.setIdentityStatus(User.IdentityStatus.PENDING);
            user.setRealName(request.getRealName());
            user.setIdCardNumber(request.getIdCardNumber());
            user.setIdCardFrontUrl(frontImageUrl);
            user.setIdCardBackUrl(backImageUrl);
            userService.updateUser(user);
            
            log.info("身份認證申請提交成功: userId={}, realName={}", user.getId(), request.getRealName());
            return saved;
            
        } catch (Exception e) {
            log.error("身份認證申請提交失敗: userId={}", user.getId(), e);
            throw new RuntimeException("身份認證申請提交失敗: " + e.getMessage());
        }
    }
    
    /**
     * 提交身份認證（舊方法，保留向後兼容性）
     */
    @Transactional
    public IdentityVerification submitIdentityVerification(
            User user, 
            IdentityVerificationRequest request,
            MultipartFile frontImage,
            MultipartFile backImage) {
        return submitIdentityVerification(user, request, frontImage, backImage, false, false);
    }
    
    /**
     * 審核身份認證
     */
    @Transactional
    public void reviewIdentityVerification(Long verificationId, boolean approved, String reviewComment, String reviewedBy) {
        Optional<IdentityVerification> verificationOpt = identityVerificationRepository.findById(verificationId);
        
        if (verificationOpt.isEmpty()) {
            throw new RuntimeException("身份認證記錄不存在");
        }
        
        IdentityVerification verification = verificationOpt.get();
        
        if (verification.getStatus() != IdentityVerification.Status.PENDING) {
            throw new RuntimeException("該申請已被審核");
        }
        
        // 更新審核結果
        verification.setStatus(approved ? IdentityVerification.Status.APPROVED : IdentityVerification.Status.REJECTED);
        verification.setReviewedAt(LocalDateTime.now());
        verification.setReviewedBy(reviewedBy);
        verification.setReviewComment(reviewComment);
        identityVerificationRepository.save(verification);
        
        // 更新用戶狀態
        User.IdentityStatus userStatus = approved ? User.IdentityStatus.APPROVED : User.IdentityStatus.REJECTED;
        userService.updateIdentityVerificationStatus(verification.getUser().getId(), userStatus);
        
        log.info("身份認證審核完成: verificationId={}, approved={}, reviewedBy={}", 
                verificationId, approved, reviewedBy);
    }
    
    /**
     * 獲取用戶的身份認證記錄
     */
    public List<IdentityVerification> getUserVerifications(User user) {
        return identityVerificationRepository.findByUserOrderBySubmittedAtDesc(user);
    }
    
    /**
     * 獲取待審核的身份認證列表
     */
    public List<IdentityVerification> getPendingVerifications() {
        return identityVerificationRepository.findByStatusWithUserOrderBySubmittedAtAsc(IdentityVerification.Status.PENDING);
    }
    
    /**
     * 根據ID獲取身份認證記錄
     */
    public Optional<IdentityVerification> getVerificationById(Long id) {
        return identityVerificationRepository.findByIdWithUser(id);
    }

    /**
     * 根據狀態獲取身份認證記錄
     */
    public List<IdentityVerification> getVerificationsByStatus(IdentityVerification.Status status) {
        return identityVerificationRepository.findByStatusOrderBySubmittedAtDesc(status);
    }

    /**
     * 獲取所有身份認證記錄
     */
    public List<IdentityVerification> getAllVerifications() {
        return identityVerificationRepository.findAllWithUserOrderBySubmittedAtDesc();
    }

    /**
     * 獲取身份認證統計信息
     */
    public Map<String, Object> getVerificationStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        long pendingCount = identityVerificationRepository.countByStatus(IdentityVerification.Status.PENDING);
        long approvedCount = identityVerificationRepository.countByStatus(IdentityVerification.Status.APPROVED);
        long rejectedCount = identityVerificationRepository.countByStatus(IdentityVerification.Status.REJECTED);
        long totalCount = identityVerificationRepository.count();

        statistics.put("pendingCount", pendingCount);
        statistics.put("approvedCount", approvedCount);
        statistics.put("rejectedCount", rejectedCount);
        statistics.put("totalCount", totalCount);

        return statistics;
    }
}
