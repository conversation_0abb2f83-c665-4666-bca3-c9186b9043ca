<template>
  <div class="login-container">
    <div class="login-content">
      <div class="left-section">
        <div class="brand">
          <h1>歡迎回來</h1>
          <p class="slogan">登入您的帳戶，繼續您的旅程</p>
        </div>
        <div class="features">
          <div class="feature">
            <el-icon class="feature-icon"><Document /></el-icon>
            <div class="feature-text">
              <h3>個人儀表板</h3>
              <p>登入後訪問您的個人資料和設置</p>
            </div>
          </div>
          <div class="feature">
            <el-icon class="feature-icon"><Lock /></el-icon>
            <div class="feature-text">
              <h3>安全保障</h3>
              <p>我們使用最先進的加密技術保護您的帳戶</p>
            </div>
          </div>
          <div class="feature">
            <el-icon class="feature-icon"><Connection /></el-icon>
            <div class="feature-text">
              <h3>隨處訪問</h3>
              <p>在任何設備上訪問您的帳戶和數據</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="right-section">
        <div class="form-header">
          <h2>用戶登入</h2>
          <p class="subtitle">輸入您的憑證繼續</p>
        </div>
        
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          label-position="top"
          @submit.prevent="handleLogin"
          class="login-form"
        >
          <el-form-item label="用戶名" prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="請輸入用戶名"
              :prefix-icon="User"
            />
          </el-form-item>
          
          <el-form-item label="密碼" prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="請輸入密碼"
              :prefix-icon="Lock"
              show-password
            />
          </el-form-item>
          
          <div class="remember-forgot">
            <el-checkbox v-model="rememberMe">記住我</el-checkbox>
            <a href="#" class="forgot-link">忘記密碼？</a>
          </div>
          
          <el-form-item>
            <el-button
              type="primary"
              :loading="userStore.loading"
              @click="handleLogin"
              class="submit-btn"
            >
              登入
            </el-button>
          </el-form-item>
          
          <div class="form-footer">
            <p>還沒有帳戶？ <router-link to="/register" class="register-link">立即註冊</router-link></p>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { User, Lock, Document, Connection } from '@element-plus/icons-vue'
import { useUserStore } from '../stores/user'

const router = useRouter()
const userStore = useUserStore()

const loginFormRef = ref<FormInstance>()
const rememberMe = ref(false)
const loginForm = reactive({
  username: '',
  password: ''
})

const loginRules: FormRules = {
  username: [
    { required: true, message: '請輸入用戶名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '請輸入密碼', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      const result = await userStore.login(loginForm)
      // 檢查響應數據結構
      const data = result as any
      if (data.success) {
        ElMessage.success('登入成功')
        router.push('/app/home')
      } else {
        ElMessage.error(data.message || '登入失敗')
      }
    }
  })
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #4b79a1 0%, #283e51 100%);
  padding: 0;
  margin: 0;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.login-content {
  display: flex;
  width: 100%;
  height: 100%;
  max-width: none;
  min-height: auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.left-section {
  flex: 1;
  background: linear-gradient(135deg, #3a7bd5 0%, #00d2ff 100%);
  color: white;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.brand {
  margin-bottom: 2rem;
}

.brand h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.slogan {
  font-size: 1.1rem;
  opacity: 0.9;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.feature {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.feature-icon {
  font-size: 1.8rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 0.8rem;
}

.feature-text h3 {
  font-size: 1.1rem;
  margin: 0 0 0.5rem 0;
}

.feature-text p {
  font-size: 0.9rem;
  opacity: 0.8;
  margin: 0;
}

.right-section {
  flex: 1;
  padding: 3rem;
  display: flex;
  flex-direction: column;
}

.form-header {
  margin-bottom: 2rem;
  text-align: center;
}

.form-header h2 {
  font-size: 2rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: #666;
  font-size: 1rem;
}

.login-form {
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
}

.remember-forgot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.forgot-link {
  color: #3a7bd5;
  font-size: 0.9rem;
  text-decoration: none;
}

.forgot-link:hover {
  text-decoration: underline;
}

.submit-btn {
  width: 100%;
  height: 48px;
  font-size: 1rem;
  font-weight: 500;
  margin-top: 1rem;
}

.form-footer {
  text-align: center;
  margin-top: 1.5rem;
  color: #666;
}

.register-link {
  color: #3a7bd5;
  font-weight: 500;
  text-decoration: none;
}

.register-link:hover {
  text-decoration: underline;
}

/* 響應式設計 */
@media (max-width: 992px) {
  .login-content {
    flex-direction: column;
    max-width: 600px;
  }
  
  .left-section {
    padding: 2rem;
  }
  
  .right-section {
    padding: 2rem;
  }
}

@media (max-width: 576px) {
  .login-container {
    padding: 10px;
  }
  
  .left-section {
    padding: 1.5rem;
  }
  
  .right-section {
    padding: 1.5rem;
  }
  
  .brand h1 {
    font-size: 2rem;
  }
  
  .form-header h2 {
    font-size: 1.8rem;
  }
  
  .remember-forgot {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

/* 針對大型螢幕 (2560x1440) 的優化 */
@media (min-width: 1920px) {
  .login-content {
    max-width: none;
    min-height: auto;
  }
  
  .left-section, .right-section {
    padding: 4rem;
  }
  
  .brand h1 {
    font-size: 3.5rem;
  }
  
  .slogan {
    font-size: 1.4rem;
  }
  
  .feature-icon {
    font-size: 2.2rem;
    padding: 1rem;
  }
  
  .feature-text h3 {
    font-size: 1.4rem;
  }
  
  .feature-text p {
    font-size: 1.1rem;
  }
  
  .form-header h2 {
    font-size: 2.5rem;
  }
  
  .subtitle {
    font-size: 1.2rem;
  }
  
  .login-form {
    max-width: 550px;
  }
  
  .el-form-item__label {
    font-size: 1.1rem;
  }
  
  .el-input__inner {
    font-size: 1.1rem;
    height: 52px;
  }
  
  .submit-btn {
    height: 56px;
    font-size: 1.2rem;
  }
}
</style>
