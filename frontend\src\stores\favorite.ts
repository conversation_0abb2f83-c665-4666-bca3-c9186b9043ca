import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { favoriteAPI, type FavoriteDto, type FavoriteItemDto, type PagedResponse, type FavoriteStatsDto } from '@/api'
import { parseError, getFriendlyErrorMessage } from '@/utils/errorHandler'
import { favoriteCache, batchOptimizer, debounce, withCache } from '@/utils/performance'
import { mockFavoriteService } from '@/services/mockFavoriteService'

export const useFavoriteStore = defineStore('favorite', () => {
  // 狀態
  const myFavorites = ref<FavoriteDto[]>([])
  const favoriteRanking = ref<FavoriteItemDto[]>([])
  const favoriteStatus = ref<Map<number, boolean>>(new Map())
  const favoriteStats = ref<Map<number, FavoriteStatsDto>>(new Map())
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 創建帶緩存的API方法
  const cachedGetFavoriteRanking = withCache(
    favoriteAPI.getFavoriteRanking,
    favoriteCache,
    (page, size, itemType) => `ranking:${page}:${size}:${itemType || 'ALL'}`,
    5 * 60 * 1000 // 5分鐘緩存
  )

  const cachedGetFavoriteStats = withCache(
    favoriteAPI.getFavoriteStats,
    favoriteCache,
    (itemId) => `stats:${itemId}`,
    10 * 60 * 1000 // 10分鐘緩存
  )

  const cachedSearchFavoriteItems = withCache(
    favoriteAPI.searchFavoriteItems,
    favoriteCache,
    (keyword, page, size) => `search:${keyword}:${page}:${size}`,
    2 * 60 * 1000 // 2分鐘緩存
  )
  
  // 分頁信息
  const myFavoritesPage = ref({
    current: 0,
    size: 20,
    totalElements: 0,
    totalPages: 0,
    hasNext: false
  })
  
  const rankingPage = ref({
    current: 0,
    size: 20,
    totalElements: 0,
    totalPages: 0,
    hasNext: false
  })

  // 計算屬性
  const favoriteCount = computed(() => myFavorites.value.length)
  const hasMoreFavorites = computed(() => myFavoritesPage.value.hasNext)
  const hasMoreRanking = computed(() => rankingPage.value.hasNext)

  // 操作方法
  const addFavorite = async (itemId: number): Promise<boolean> => {
    try {
      loading.value = true
      error.value = null
      
      // 使用模拟服务替代真实API
      const response = await mockFavoriteService.addFavorite(itemId)
      
      if (response.success) {
        // 更新收藏狀態
        favoriteStatus.value.set(itemId, true)
        
        // 更新統計信息
        const stats = favoriteStats.value.get(itemId)
        if (stats) {
          stats.favoriteCount++
          stats.isFavorited = true
        }
        
        // 更新排行榜中的收藏狀態
        const rankingItem = favoriteRanking.value.find(item => item.id === itemId)
        if (rankingItem) {
          rankingItem.favoriteCount++
          rankingItem.isFavorited = true
        }
        
        return true
      } else {
        error.value = response.message || '收藏失败'
        return false
      }
    } catch (err: any) {
      error.value = err.message || '收藏失败'
      return false
    } finally {
      loading.value = false
    }
  }

  const removeFavorite = async (itemId: number): Promise<boolean> => {
    try {
      loading.value = true
      error.value = null
      
      // 使用模拟服务替代真实API
      const response = await mockFavoriteService.removeFavorite(itemId)
      
      if (response.success) {
        // 更新收藏狀態
        favoriteStatus.value.set(itemId, false)
        
        // 從我的收藏列表中移除
        const index = myFavorites.value.findIndex(fav => fav.itemId === itemId)
        if (index !== -1) {
          myFavorites.value.splice(index, 1)
          myFavoritesPage.value.totalElements--
        }
        
        // 更新統計信息
        const stats = favoriteStats.value.get(itemId)
        if (stats) {
          stats.favoriteCount = Math.max(0, stats.favoriteCount - 1)
          stats.isFavorited = false
        }
        
        // 更新排行榜中的收藏狀態
        const rankingItem = favoriteRanking.value.find(item => item.id === itemId)
        if (rankingItem) {
          rankingItem.favoriteCount = Math.max(0, rankingItem.favoriteCount - 1)
          rankingItem.isFavorited = false
        }
        
        return true
      } else {
        error.value = response.message || '取消收藏失败'
        return false
      }
    } catch (err: any) {
      error.value = err.message || '取消收藏失败'
      return false
    } finally {
      loading.value = false
    }
  }

  const loadMyFavorites = async (page: number = 0, size: number = 20, itemType?: string, append: boolean = false): Promise<boolean> => {
    try {
      loading.value = true
      error.value = null
      
      // 使用模拟服务替代真实API
      const response = await mockFavoriteService.getMyFavorites(page, size, itemType)
      
      if (response.success) {
        const pageData = response.data
        
        if (append) {
          myFavorites.value.push(...pageData.content)
        } else {
          myFavorites.value = pageData.content
        }
        
        myFavoritesPage.value = {
          current: pageData.page,
          size: pageData.size,
          totalElements: pageData.totalElements,
          totalPages: pageData.totalPages,
          hasNext: !pageData.last
        }
        
        return true
      } else {
        error.value = response.message || '載入收藏列表失敗'
        return false
      }
    } catch (err: any) {
      error.value = err.message || '載入收藏列表失敗'
      return false
    } finally {
      loading.value = false
    }
  }

  const loadFavoriteRanking = async (page: number = 0, size: number = 20, itemType?: string, append: boolean = false): Promise<boolean> => {
    try {
      loading.value = true
      error.value = null

      // 使用模拟服务替代真实API
      const response = await mockFavoriteService.getFavoriteRanking(page, size, itemType)

      if (response.success) {
        const pageData = response.data

        if (append) {
          favoriteRanking.value.push(...pageData.content)
        } else {
          favoriteRanking.value = pageData.content
        }

        rankingPage.value = {
          current: pageData.page,
          size: pageData.size,
          totalElements: pageData.totalElements,
          totalPages: pageData.totalPages,
          hasNext: !pageData.last
        }

        return true
      } else {
        error.value = response.message || '載入收藏排行榜失敗'
        return false
      }
    } catch (err: any) {
      error.value = err.message || '載入收藏排行榜失敗'
      return false
    } finally {
      loading.value = false
    }
  }

  const checkFavoriteStatus = async (itemId: number): Promise<boolean | null> => {
    try {
      // 使用模拟服务替代真实API
      const response = await mockFavoriteService.getFavoriteStatus(itemId)
      
      if (response.success) {
        const isFavorited = response.data
        favoriteStatus.value.set(itemId, isFavorited)
        return isFavorited
      } else {
        return null
      }
    } catch (err: any) {
      console.warn('檢查收藏狀態失敗:', err)
      return null
    }
  }

  const loadFavoriteStats = async (itemId: number): Promise<FavoriteStatsDto | null> => {
    try {
      // 使用模拟服务替代真实API
      const response = await mockFavoriteService.getFavoriteStats(itemId)

      if (response.success) {
        const stats = response.data
        favoriteStats.value.set(itemId, stats)
        favoriteStatus.value.set(itemId, stats.isFavorited)
        return stats
      } else {
        return null
      }
    } catch (err: any) {
      console.warn('載入收藏統計失敗:', err)
      return null
    }
  }

  const searchFavoriteItems = async (keyword: string, page: number = 0, size: number = 20): Promise<PagedResponse<FavoriteItemDto> | null> => {
    try {
      loading.value = true
      error.value = null

      // 使用模拟服务替代真实API
      const response = await mockFavoriteService.searchFavoriteItems(keyword, page, size)

      if (response.success) {
        return response.data
      } else {
        error.value = response.message || '搜索失败'
        return null
      }
    } catch (err: any) {
      error.value = err.message || '搜索失败'
      return null
    } finally {
      loading.value = false
    }
  }

  const getRecentItems = async (page: number = 0, size: number = 20, itemType?: string): Promise<PagedResponse<FavoriteItemDto> | null> => {
    try {
      loading.value = true
      error.value = null
      
      // 使用模拟服务替代真实API
      const response = await mockFavoriteService.getRecentItems(page, size, itemType)
      
      if (response.success) {
        return response.data
      } else {
        error.value = response.message || '載入最近內容失敗'
        return null
      }
    } catch (err: any) {
      error.value = err.message || '載入最近內容失敗'
      return null
    } finally {
      loading.value = false
    }
  }

  // 清理方法
  const clearError = () => {
    error.value = null
  }

  const resetStore = () => {
    myFavorites.value = []
    favoriteRanking.value = []
    favoriteStatus.value.clear()
    favoriteStats.value.clear()
    error.value = null
    loading.value = false

    myFavoritesPage.value = {
      current: 0,
      size: 20,
      totalElements: 0,
      totalPages: 0,
      hasNext: false
    }

    rankingPage.value = {
      current: 0,
      size: 20,
      totalElements: 0,
      totalPages: 0,
      hasNext: false
    }

    // 清理緩存
    favoriteCache.clear()
  }

  // 清理過期緩存的定時任務
  const cleanupCache = debounce(() => {
    favoriteCache.cleanup()
  }, 60000) // 每分鐘清理一次

  // 定期清理緩存
  setInterval(cleanupCache, 5 * 60 * 1000) // 每5分鐘執行一次

  return {
    // 狀態
    myFavorites,
    favoriteRanking,
    favoriteStatus,
    favoriteStats,
    loading,
    error,
    myFavoritesPage,
    rankingPage,
    
    // 計算屬性
    favoriteCount,
    hasMoreFavorites,
    hasMoreRanking,
    
    // 操作方法
    addFavorite,
    removeFavorite,
    loadMyFavorites,
    loadFavoriteRanking,
    checkFavoriteStatus,
    loadFavoriteStats,
    searchFavoriteItems,
    getRecentItems,
    clearError,
    resetStore
  }
})
