# 支付寶沙箱配置指南

## 🔧 當前問題

支付功能已經成功跳轉到支付寶，但出現簽名驗證錯誤：
```
錯誤代碼: invalid-signature
錯誤原因: 驗簽出錯，建議檢查簽名字符串或簽名私鑰與應用公鑰是否匹配
```

## 🎯 解決方案

### 1. 生成正確的密鑰對

需要生成一對匹配的 RSA 密鑰：

```bash
# 生成私鑰
openssl genrsa -out app_private_key.pem 2048

# 從私鑰生成公鑰
openssl rsa -in app_private_key.pem -pubout -out app_public_key.pem

# 轉換私鑰為 PKCS#8 格式（Java 需要）
openssl pkcs8 -topk8 -inform PEM -in app_private_key.pem -outform PEM -nocrypt -out app_private_key_pkcs8.pem
```

### 2. 配置支付寶沙箱

1. **登錄支付寶開放平台沙箱**：https://openhome.alipay.com/develop/sandbox/app
2. **上傳應用公鑰**：將 `app_public_key.pem` 的內容上傳
3. **獲取支付寶公鑰**：從沙箱頁面複製支付寶公鑰

### 3. 更新後端配置

將生成的密鑰更新到 `application.yml`：

```yaml
alipay:
  config:
    appid: 9021000129631387  # 沙箱應用ID
    serverUrl: https://openapi-sandbox.dl.alipaydev.com/gateway.do
    alipay-public-key: [從沙箱頁面複製的支付寶公鑰]
    private-key: [app_private_key_pkcs8.pem 的內容，去掉頭尾和換行]
    return-url: https://b02ad650a2dc.ngrok-free.app/payment/success
    notify-url: https://b02ad650a2dc.ngrok-free.app/api/payment/alipay/callback
```

## 🚀 測試步驟

### 1. 重啟後端服務
配置更新後需要重啟 SpringBoot 應用。

### 2. 測試支付流程
1. 登錄系統：http://localhost:5173/login
2. 進入支付頁面：http://localhost:5173/app/payment/3
3. 點擊"立即支付"
4. 應該正常跳轉到支付寶沙箱頁面

### 3. 使用測試賬號
- **買家賬號**: <EMAIL>
- **登錄密碼**: 111111
- **支付密碼**: 111111

## 📋 當前配置狀態

✅ **ngrok 配置正確**：https://b02ad650a2dc.ngrok-free.app
✅ **支付流程正常**：能夠跳轉到支付寶
✅ **前端修復完成**：表單提交邏輯正確
⚠️ **密鑰配置問題**：需要使用匹配的密鑰對

## 🔍 調試信息

從錯誤信息可以看到支付請求的詳細參數：
- **應用ID**: 9021000129631387 ✅
- **商戶訂單號**: ORD202507250940323996 ✅
- **支付金額**: 89.00 ✅
- **回調地址**: https://b02ad650a2dc.ngrok-free.app/api/payment/alipay/callback ✅
- **返回地址**: https://b02ad650a2dc.ngrok-free.app/payment/success ✅

所有參數都正確，只是簽名驗證失敗。

## 💡 快速解決方案

如果需要快速測試，可以：

1. **使用支付寶官方提供的測試密鑰**
2. **或者重新生成密鑰對並上傳到沙箱**
3. **確保私鑰格式正確（PKCS#8，無密碼保護）**

## 📞 技術支持

如果問題持續存在，可以：
1. 檢查支付寶開發者文檔
2. 使用支付寶提供的簽名驗證工具
3. 聯繫支付寶技術支持

---

**注意**：當前支付功能的核心邏輯已經完全正確，只需要解決密鑰配置問題即可正常使用。
