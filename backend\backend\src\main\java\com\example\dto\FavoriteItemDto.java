package com.example.dto;

import com.example.entity.FavoriteItem;
import com.example.enums.ItemType;

import java.time.LocalDateTime;

/**
 * 可收藏内容数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public class FavoriteItemDto {
    
    private Long id;
    private String title;
    private String description;
    private ItemType itemType;
    private String contentUrl;
    private String thumbnailUrl;
    private Integer favoriteCount;
    private LocalDateTime createdAt;
    private Boolean isFavorited; // 当前用户是否已收藏
    
    // 构造函数
    public FavoriteItemDto() {
    }
    
    /**
     * 从实体类转换为DTO
     * 
     * @param item 内容实体
     */
    public FavoriteItemDto(FavoriteItem item) {
        this.id = item.getId();
        this.title = item.getTitle();
        this.description = item.getDescription();
        this.itemType = item.getItemType();
        this.contentUrl = item.getContentUrl();
        this.thumbnailUrl = item.getThumbnailUrl();
        this.favoriteCount = item.getFavoriteCount();
        this.createdAt = item.getCreatedAt();
        this.isFavorited = false; // 默认未收藏，需要在Service层设置
    }
    
    /**
     * 从实体类转换为DTO，并设置收藏状态
     * 
     * @param item 内容实体
     * @param isFavorited 是否已收藏
     */
    public FavoriteItemDto(FavoriteItem item, Boolean isFavorited) {
        this(item);
        this.isFavorited = isFavorited;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public ItemType getItemType() {
        return itemType;
    }
    
    public void setItemType(ItemType itemType) {
        this.itemType = itemType;
    }
    
    public String getContentUrl() {
        return contentUrl;
    }
    
    public void setContentUrl(String contentUrl) {
        this.contentUrl = contentUrl;
    }
    
    public String getThumbnailUrl() {
        return thumbnailUrl;
    }
    
    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }
    
    public Integer getFavoriteCount() {
        return favoriteCount;
    }
    
    public void setFavoriteCount(Integer favoriteCount) {
        this.favoriteCount = favoriteCount;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public Boolean getIsFavorited() {
        return isFavorited;
    }
    
    public void setIsFavorited(Boolean isFavorited) {
        this.isFavorited = isFavorited;
    }
    
    @Override
    public String toString() {
        return "FavoriteItemDto{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", itemType=" + itemType +
                ", favoriteCount=" + favoriteCount +
                ", isFavorited=" + isFavorited +
                ", createdAt=" + createdAt +
                '}';
    }
}
