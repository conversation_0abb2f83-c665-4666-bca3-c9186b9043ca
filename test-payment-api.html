<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付 API 測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>支付寶沙箱支付測試</h1>
    
    <div class="test-section">
        <h3>1. 設置認證 Token</h3>
        <input type="text" id="token" placeholder="輸入 JWT Token" style="width: 400px;">
        <button onclick="setToken()">設置 Token</button>
        <div id="tokenResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 測試訂單查詢</h3>
        <input type="number" id="orderId" placeholder="訂單 ID" value="3">
        <button onclick="testOrderQuery()">查詢訂單</button>
        <div id="orderResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 測試支付寶支付</h3>
        <button onclick="testAlipayPayment()">發起支付寶支付</button>
        <div id="paymentResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. 支付寶沙箱配置信息</h3>
        <div class="result">
應用ID: 9021000129631387
沙箱網關: https://openapi-sandbox.dl.alipaydev.com/gateway.do
買家測試賬號: <EMAIL>
登錄密碼: 111111
支付密碼: 111111
        </div>
    </div>

    <script>
        let authToken = '';

        function setToken() {
            authToken = document.getElementById('token').value;
            document.getElementById('tokenResult').textContent = 
                authToken ? `Token 已設置: ${authToken.substring(0, 20)}...` : '請輸入有效的 Token';
        }

        async function testOrderQuery() {
            const orderId = document.getElementById('orderId').value;
            const resultDiv = document.getElementById('orderResult');
            
            try {
                const response = await fetch(`http://localhost:8080/api/orders/${orderId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                resultDiv.textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                resultDiv.textContent = `錯誤: ${error.message}`;
            }
        }

        async function testAlipayPayment() {
            const orderId = document.getElementById('orderId').value;
            const resultDiv = document.getElementById('paymentResult');
            
            if (!authToken) {
                resultDiv.textContent = '請先設置認證 Token';
                return;
            }
            
            try {
                resultDiv.textContent = '正在發起支付請求...';
                
                const response = await fetch(`http://localhost:8080/api/payment/alipay/create?orderId=${orderId}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success && result.data) {
                    // 創建支付表單
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = 'about:blank';
                    form.target = '_blank';
                    form.innerHTML = result.data;
                    document.body.appendChild(form);
                    form.submit();
                    document.body.removeChild(form);
                    
                    resultDiv.textContent = '支付頁面已在新窗口打開\n\n' + JSON.stringify(result, null, 2);
                } else {
                    resultDiv.textContent = `支付失敗: ${result.message}\n\n` + JSON.stringify(result, null, 2);
                }
            } catch (error) {
                resultDiv.textContent = `錯誤: ${error.message}`;
            }
        }

        // 頁面加載時嘗試從 localStorage 獲取 token
        window.onload = function() {
            const savedToken = localStorage.getItem('token');
            if (savedToken) {
                document.getElementById('token').value = savedToken;
                setToken();
            }
        };
    </script>
</body>
</html>
