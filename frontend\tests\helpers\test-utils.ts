import { Page, expect } from '@playwright/test';

/**
 * 測試輔助工具類
 */
export class TestUtils {
  
  /**
   * 模擬用戶登錄
   */
  static async login(page: Page, email = '<EMAIL>', password = 'password123') {
    await page.goto('/login');
    await page.fill('input[type="email"]', email);
    await page.fill('input[type="password"]', password);
    await page.click('button[type="submit"]');
    await page.waitForURL('/app/home');
  }

  /**
   * 等待元素加載並檢查可見性
   */
  static async waitForElementAndCheck(page: Page, selector: string, timeout = 5000) {
    await page.waitForSelector(selector, { timeout });
    await expect(page.locator(selector)).toBeVisible();
  }

  /**
   * 檢查商品卡片的基本結構
   */
  static async checkProductCardStructure(page: Page, cardSelector = '.product-card') {
    const card = page.locator(cardSelector).first();
    await expect(card).toBeVisible();
    await expect(card.locator('.product-image')).toBeVisible();
    await expect(card.locator('.product-name')).toBeVisible();
    await expect(card.locator('.current-price')).toBeVisible();
  }

  /**
   * 檢查分頁組件功能
   */
  static async checkPaginationComponent(page: Page) {
    const pagination = page.locator('.el-pagination');
    await expect(pagination).toBeVisible();
    
    // 檢查分頁信息
    const total = page.locator('.el-pagination__total');
    if (await total.isVisible()) {
      const totalText = await total.textContent();
      expect(totalText).toMatch(/共\s+\d+\s+條/);
    }
  }

  /**
   * 模擬商品搜索
   */
  static async searchProducts(page: Page, keyword: string) {
    const searchInput = page.locator('.search-box input');
    await searchInput.fill(keyword);
    await page.keyboard.press('Enter');
    await page.waitForTimeout(1000); // 等待搜索結果
  }

  /**
   * 檢查商品詳情頁面結構
   */
  static async checkProductDetailStructure(page: Page) {
    await expect(page.locator('.product-detail')).toBeVisible();
    await expect(page.locator('.product-images')).toBeVisible();
    await expect(page.locator('.product-info')).toBeVisible();
    await expect(page.locator('.product-title')).toBeVisible();
    await expect(page.locator('.current-price')).toBeVisible();
    await expect(page.locator('.action-section')).toBeVisible();
  }

  /**
   * 檢查API響應結構
   */
  static checkApiResponse(data: any, expectedProperties: string[] = ['success']) {
    expectedProperties.forEach(prop => {
      expect(data).toHaveProperty(prop);
    });
  }

  /**
   * 檢查商品數據結構
   */
  static checkProductDataStructure(product: any) {
    const requiredFields = ['id', 'name', 'price', 'stock', 'categoryId', 'status'];
    requiredFields.forEach(field => {
      expect(product).toHaveProperty(field);
    });
    
    expect(typeof product.id).toBe('number');
    expect(typeof product.name).toBe('string');
    expect(typeof product.price).toBe('number');
    expect(typeof product.stock).toBe('number');
    expect(product.price).toBeGreaterThan(0);
    expect(product.stock).toBeGreaterThanOrEqual(0);
  }

  /**
   * 檢查分類數據結構
   */
  static checkCategoryDataStructure(category: any) {
    const requiredFields = ['id', 'name', 'parentId', 'level', 'status'];
    requiredFields.forEach(field => {
      expect(category).toHaveProperty(field);
    });
    
    expect(typeof category.id).toBe('number');
    expect(typeof category.name).toBe('string');
    expect(typeof category.parentId).toBe('number');
    expect(typeof category.level).toBe('number');
    expect(category.level).toBeGreaterThan(0);
  }

  /**
   * 模擬移動端視圖
   */
  static async setMobileViewport(page: Page) {
    await page.setViewportSize({ width: 375, height: 667 });
  }

  /**
   * 模擬平板視圖
   */
  static async setTabletViewport(page: Page) {
    await page.setViewportSize({ width: 768, height: 1024 });
  }

  /**
   * 恢復桌面視圖
   */
  static async setDesktopViewport(page: Page) {
    await page.setViewportSize({ width: 1280, height: 720 });
  }

  /**
   * 檢查響應式佈局
   */
  static async checkResponsiveLayout(page: Page, selector: string) {
    // 桌面視圖
    await this.setDesktopViewport(page);
    await expect(page.locator(selector)).toBeVisible();
    
    // 平板視圖
    await this.setTabletViewport(page);
    await expect(page.locator(selector)).toBeVisible();
    
    // 移動端視圖
    await this.setMobileViewport(page);
    await expect(page.locator(selector)).toBeVisible();
    
    // 恢復桌面視圖
    await this.setDesktopViewport(page);
  }

  /**
   * 測量頁面加載性能
   */
  static async measurePageLoadTime(page: Page, url: string): Promise<number> {
    const startTime = Date.now();
    await page.goto(url);
    await page.waitForLoadState('networkidle');
    return Date.now() - startTime;
  }

  /**
   * 檢查元素是否包含文本（忽略大小寫）
   */
  static async checkElementContainsText(page: Page, selector: string, text: string) {
    const element = page.locator(selector);
    await expect(element).toContainText(text, { ignoreCase: true });
  }

  /**
   * 等待並點擊元素
   */
  static async waitAndClick(page: Page, selector: string, timeout = 5000) {
    await page.waitForSelector(selector, { timeout });
    await page.click(selector);
  }

  /**
   * 滾動到元素位置
   */
  static async scrollToElement(page: Page, selector: string) {
    await page.locator(selector).scrollIntoViewIfNeeded();
  }

  /**
   * 檢查圖片是否加載成功
   */
  static async checkImageLoaded(page: Page, imgSelector: string) {
    const img = page.locator(imgSelector);
    await expect(img).toBeVisible();
    
    // 檢查圖片是否實際加載
    const naturalWidth = await img.evaluate((el: HTMLImageElement) => el.naturalWidth);
    expect(naturalWidth).toBeGreaterThan(0);
  }

  /**
   * 模擬網絡慢速連接
   */
  static async simulateSlowNetwork(page: Page) {
    await page.route('**/*', async route => {
      await new Promise(resolve => setTimeout(resolve, 100)); // 延遲100ms
      await route.continue();
    });
  }

  /**
   * 清除所有本地存儲
   */
  static async clearStorage(page: Page) {
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
  }

  /**
   * 生成隨機測試數據
   */
  static generateRandomString(length = 10): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成隨機價格
   */
  static generateRandomPrice(min = 10, max = 10000): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }
}
