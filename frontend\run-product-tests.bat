@echo off
echo ========================================
echo 商品排序和篩選功能測試
echo ========================================

echo.
echo 🔍 檢查環境...

REM 檢查 Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安裝，請先安裝 Node.js
    pause
    exit /b 1
)

REM 檢查 npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm 未安裝
    pause
    exit /b 1
)

echo ✅ Node.js 和 npm 已安裝

REM 檢查 Playwright
npx playwright --version >nul 2>&1
if errorlevel 1 (
    echo 📦 安裝 Playwright...
    npm install -D @playwright/test
    npx playwright install
    if errorlevel 1 (
        echo ❌ Playwright 安裝失敗
        pause
        exit /b 1
    )
)

echo ✅ Playwright 已準備就緒

echo.
echo 🚀 開始運行測試...
echo.

REM 運行測試
npx playwright test tests/product-sorting-filtering.spec.ts --reporter=list

if errorlevel 1 (
    echo.
    echo ❌ 測試執行失敗
    echo.
    echo 🔧 故障排除建議:
    echo 1. 確保前端服務正在運行 (npm run dev)
    echo 2. 確保後端服務正在運行 (Spring Boot)
    echo 3. 確保數據庫連接正常
    echo 4. 檢查網絡連接
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo 🎉 所有測試完成！
    echo.
)

echo 📊 是否查看詳細測試報告？(y/N)
set /p choice=
if /i "%choice%"=="y" (
    npx playwright show-report
)

pause
