<template>
  <div class="error-handler">
    <!-- 網絡錯誤提示 -->
    <el-alert
      v-if="networkError"
      title="網絡連接異常"
      type="error"
      :closable="false"
      show-icon
      class="network-error"
    >
      <template #default>
        <p>請檢查您的網絡連接，然後重試</p>
        <el-button type="primary" size="small" @click="retryConnection">
          <el-icon><Refresh /></el-icon>
          重試
        </el-button>
      </template>
    </el-alert>

    <!-- 操作失敗重試 -->
    <el-dialog
      v-model="showRetryDialog"
      title="操作失敗"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="retry-content">
        <el-icon class="error-icon"><WarningFilled /></el-icon>
        <p>{{ retryMessage }}</p>
        <div class="retry-actions">
          <el-button @click="cancelRetry">取消</el-button>
          <el-button type="primary" @click="executeRetry" :loading="retrying">
            重試
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 成功動畫 -->
    <transition name="success-fade">
      <div v-if="showSuccessAnimation" class="success-overlay">
        <div class="success-content">
          <div class="success-icon">
            <el-icon><SuccessFilled /></el-icon>
          </div>
          <h3>{{ successMessage }}</h3>
          <p>{{ successDescription }}</p>
        </div>
      </div>
    </transition>

    <!-- 加載動畫 -->
    <transition name="loading-fade">
      <div v-if="showLoadingOverlay" class="loading-overlay">
        <div class="loading-content">
          <el-icon class="loading-spinner"><Loading /></el-icon>
          <p>{{ loadingMessage }}</p>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, WarningFilled, SuccessFilled, Loading } from '@element-plus/icons-vue'

// 響應式數據
const networkError = ref(false)
const showRetryDialog = ref(false)
const showSuccessAnimation = ref(false)
const showLoadingOverlay = ref(false)
const retrying = ref(false)

const retryMessage = ref('')
const retryCallback = ref<(() => Promise<void>) | null>(null)
const successMessage = ref('')
const successDescription = ref('')
const loadingMessage = ref('處理中...')

// 方法
const checkNetworkStatus = () => {
  networkError.value = !navigator.onLine
}

const retryConnection = async () => {
  checkNetworkStatus()
  if (!networkError.value) {
    ElMessage.success('網絡連接已恢復')
  }
}

const showRetry = (message: string, callback: () => Promise<void>) => {
  retryMessage.value = message
  retryCallback.value = callback
  showRetryDialog.value = true
}

const executeRetry = async () => {
  if (!retryCallback.value) return
  
  try {
    retrying.value = true
    await retryCallback.value()
    showRetryDialog.value = false
    ElMessage.success('操作成功')
  } catch (error) {
    ElMessage.error('重試失敗，請稍後再試')
  } finally {
    retrying.value = false
  }
}

const cancelRetry = () => {
  showRetryDialog.value = false
  retryCallback.value = null
}

const showSuccess = (message: string, description: string = '', duration: number = 2000) => {
  successMessage.value = message
  successDescription.value = description
  showSuccessAnimation.value = true
  
  setTimeout(() => {
    showSuccessAnimation.value = false
  }, duration)
}

const showLoading = (message: string = '處理中...') => {
  loadingMessage.value = message
  showLoadingOverlay.value = true
}

const hideLoading = () => {
  showLoadingOverlay.value = false
}

// 暴露方法給父組件
defineExpose({
  showRetry,
  showSuccess,
  showLoading,
  hideLoading
})

// 生命週期
onMounted(() => {
  window.addEventListener('online', checkNetworkStatus)
  window.addEventListener('offline', checkNetworkStatus)
  checkNetworkStatus()
})

onUnmounted(() => {
  window.removeEventListener('online', checkNetworkStatus)
  window.removeEventListener('offline', checkNetworkStatus)
})
</script>

<style scoped>
.error-handler {
  position: relative;
}

.network-error {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3000;
  min-width: 300px;
}

.retry-content {
  text-align: center;
  padding: 20px;
}

.error-icon {
  font-size: 48px;
  color: #f56c6c;
  margin-bottom: 16px;
}

.retry-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

.success-overlay, .loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.success-content, .loading-content {
  background: white;
  padding: 40px;
  border-radius: 12px;
  text-align: center;
  max-width: 400px;
}

.success-icon {
  font-size: 64px;
  color: #67c23a;
  margin-bottom: 20px;
}

.success-content h3 {
  font-size: 24px;
  color: #333;
  margin-bottom: 12px;
}

.success-content p {
  color: #666;
  font-size: 16px;
}

.loading-spinner {
  font-size: 48px;
  color: #409eff;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-content p {
  font-size: 16px;
  color: #666;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.success-fade-enter-active, .success-fade-leave-active,
.loading-fade-enter-active, .loading-fade-leave-active {
  transition: opacity 0.3s ease;
}

.success-fade-enter-from, .success-fade-leave-to,
.loading-fade-enter-from, .loading-fade-leave-to {
  opacity: 0;
}
</style>
