package com.example.dto;

import com.example.entity.Menu;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 菜單創建請求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MenuCreateRequest {
    private String name;
    private String path;
    private String icon;
    private String description;
    private Integer sortOrder;
    private Boolean enabled = true;
    private Menu.MenuType menuType = Menu.MenuType.MENU;
    private String permission;
    private Long parentId;
}
