/**
 * JWT Token 工具函數
 */

interface JWTPayload {
  sub: string // username
  exp: number // 過期時間戳
  iat: number // 簽發時間戳
  [key: string]: any
}

/**
 * 解析 JWT Token（不驗證簽名，僅用於獲取 payload）
 */
export function parseJWT(token: string): JWTPayload | null {
  try {
    const parts = token.split('.')
    if (parts.length !== 3) {
      return null
    }

    const payload = parts[1]
    const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'))
    return JSON.parse(decoded)
  } catch (error) {
    console.error('解析 JWT Token 失敗:', error)
    return null
  }
}

/**
 * 檢查 token 是否過期
 * @param token JWT token
 * @param bufferSeconds 提前多少秒認為過期（默認 0 秒）
 */
export function isTokenExpired(token: string, bufferSeconds: number = 0): boolean {
  const payload = parseJWT(token)
  if (!payload || !payload.exp) {
    return true
  }

  const currentTime = Math.floor(Date.now() / 1000)
  return payload.exp <= (currentTime + bufferSeconds)
}

/**
 * 獲取 token 剩餘有效時間（秒）
 */
export function getTokenRemainingTime(token: string): number {
  const payload = parseJWT(token)
  if (!payload || !payload.exp) {
    return 0
  }

  const currentTime = Math.floor(Date.now() / 1000)
  return Math.max(0, payload.exp - currentTime)
}

/**
 * 檢查 token 是否有效（格式正確且未過期）
 */
export function isTokenValid(token: string): boolean {
  if (!token) return false
  
  const payload = parseJWT(token)
  if (!payload) return false
  
  return !isTokenExpired(token)
}

/**
 * 從 localStorage 獲取 tokens
 */
export function getStoredTokens() {
  return {
    accessToken: localStorage.getItem('token'),
    refreshToken: localStorage.getItem('refreshToken')
  }
}

/**
 * 存儲 tokens 到 localStorage
 */
export function storeTokens(accessToken: string, refreshToken?: string) {
  localStorage.setItem('token', accessToken)
  if (refreshToken) {
    localStorage.setItem('refreshToken', refreshToken)
  }
}

/**
 * 清理所有 tokens
 */
export function clearTokens() {
  localStorage.removeItem('token')
  localStorage.removeItem('refreshToken')
  localStorage.removeItem('user')
}
